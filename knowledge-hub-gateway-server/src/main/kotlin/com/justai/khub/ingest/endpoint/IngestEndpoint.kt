package com.justai.khub.ingest.endpoint

import com.justai.khub.api.fe.IngestApiService
import com.justai.khub.api.fe.model.CancelIngestRequest
import com.justai.khub.api.fe.model.ProjectDTO
import com.justai.khub.common.util.WebUtils
import com.justai.khub.ingest.service.IngestService
import com.justai.khub.project.mapper.ProjectMapper.toDTO
import com.justai.khub.project.mapper.ProjectVersionMapper
import com.justai.khub.project.service.ProjectFileService
import com.justai.khub.project.service.ProjectService
import org.springframework.stereotype.Service

@Service
class IngestEndpoint(
    private val ingestService: IngestService,
    private val projectService: ProjectService,
    private val projectFileService: ProjectFileService,
) : IngestApiService {

    override fun ingest(projectId: Long, version: String?): ProjectDTO {
        val currentAccountId = WebUtils.getCurrentAccountId()
        val actualVersion = version ?: ProjectVersionMapper.DEFAULT_VERSION
        val versionEntity = ingestService.startIngest(projectId, actualVersion, currentAccountId)
        val project = projectService.getProject(projectId, currentAccountId)
        val fileStatuses = projectFileService.getFilesSummaryByStatus(listOf(versionEntity.id))
        return project.toDTO(fileStatuses)
    }

    override fun cancelIngest(projectId: Long, cancelIngestRequest: CancelIngestRequest, version: String?) {
        val currentAccountId = WebUtils.getCurrentAccountId()
        val actualVersion = version ?: ProjectVersionMapper.DEFAULT_VERSION
        ingestService.cancelIngest(projectId, actualVersion, cancelIngestRequest.reason, currentAccountId)
    }
}
