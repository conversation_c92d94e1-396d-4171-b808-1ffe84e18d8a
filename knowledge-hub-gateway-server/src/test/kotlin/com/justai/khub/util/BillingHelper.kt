package com.justai.khub.util

import com.justai.khub.billing.dto.AccountBalanceResponse
import com.justai.khub.billing.dto.AccountBalancesResponse
import com.justai.khub.billing.dto.CurrentLimitsRequestDto
import com.justai.khub.billing.dto.TariffUpdateDto
import com.justai.khub.billing.repository.AccountBalanceRepository
import com.justai.khub.common.util.JSON
import com.justai.khub.util.WebUtils.parseTo
import org.junit.jupiter.api.Assertions
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.MediaType
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.httpBasic
import org.springframework.stereotype.Component
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import org.springframework.transaction.support.TransactionTemplate

@Component
class BillingHelper {

    @Value("\${server.username}")
    private lateinit var basicAuthUser: String

    @Value("\${server.password}")
    private lateinit var basicAuthPassword: String

    @Autowired
    protected lateinit var mvc: MockMvc

    @Autowired
    private lateinit var accountBalanceRepository: AccountBalanceRepository

    @Autowired
    private lateinit var transactionTemplate: TransactionTemplate

    fun updateAccountBalance(update: TariffUpdateDto): AccountBalanceResponse {
        val response = mvc.perform(
            post("/api/khub/internal/billing/cc-tariff-update")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(listOf(update)))
                .with(httpBasic(basicAuthUser, basicAuthPassword))
        )
            .andExpect(status().isOk)
            .andReturn().parseTo<AccountBalancesResponse>()

        Assertions.assertEquals(1, response.limits.size)
        Assertions.assertEquals(update.accountId, response.limits.first().accountId)
        return response.limits.first()
    }

    fun getAccountBalance(accountId: Long): AccountBalanceResponse? {
        val response = mvc.perform(
            post("/api/khub/internal/billing/account-limits/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(CurrentLimitsRequestDto(accountIds = listOf(accountId))))
                .with(httpBasic(basicAuthUser, basicAuthPassword))
        )
            .andExpect(status().isOk)
            .andReturn().parseTo<AccountBalancesResponse>()
        return response.limits.firstOrNull { it.accountId == accountId }
    }

    fun useTariffRequests(accountId: Long, useAmount: Long): AccountBalanceResponse {
        transactionTemplate.execute {
            val entity = accountBalanceRepository.findForUpdateByAccountId(accountId)!!
            entity.usedRequestsByTariff = useAmount
            accountBalanceRepository.saveAndFlush(entity)
        }
        return getAccountBalance(accountId)!!
    }

    fun usePackageRequests(accountId: Long, useAmount: Long): AccountBalanceResponse {
        transactionTemplate.execute {
            val entity = accountBalanceRepository.findForUpdateByAccountId(accountId)!!
            entity.usedRequestsByPackages = useAmount
            accountBalanceRepository.saveAndFlush(entity)
        }
        return getAccountBalance(accountId)!!
    }
}
