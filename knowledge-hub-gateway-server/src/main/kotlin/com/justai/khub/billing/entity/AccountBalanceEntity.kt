package com.justai.khub.billing.entity

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EntityListeners
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

@Entity
@Table(name = "account_balance")
@EntityListeners(AuditingEntityListener::class)
class AccountBalanceEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = -1

    @Column
    var accountId: Long = -1

    @Column
    var availableRequestsByTariff: Long = 0

    @Column
    var tariffVersion: Int = 0

    @Column
    var availableRequestsByPackages: Long = 0

    @Column
    var operationsSuspended: Boolean = false

    @Column
    var usedRequestsByPackages: Long = 0

    @Column
    var usedRequestsByTariff: Long = 0

    @Column
    var usedRequestsByPackagesWhenTariffPeriodStarted: Long = 0


    @Column
    @CreatedDate
    lateinit var createdAt: LocalDateTime

    @Column
    @LastModifiedDate
    lateinit var updatedAt: LocalDateTime

}
