package com.justai.khub.common.dto.rag

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class ChunkResponse(
    val id: String,
    val docId: String,
    val content: String? = null,
    val summary: String? = null,
    val questions: List<String>? = null,
    val keywords: List<String>? = null,
    val score: Double,
)
