package com.justai.khub.chat.repository

import com.justai.khub.chat.entity.ChatEntity
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.EntityGraph
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface ChatRepository : JpaRepository<ChatEntity, Long> {
    @EntityGraph(attributePaths = ["version"])
    @Query("SELECT c FROM ChatEntity c WHERE c.version.id = :versionId")
    fun findAllByVersionId(versionId: Long, pageable: Pageable): Page<ChatEntity>

    @Query("SELECT c FROM ChatEntity c WHERE c.id = :id AND c.version.project.id = :projectId")
    fun findByIdAndProjectId(id: Long, projectId: Long): ChatEntity?

    fun findFirstByVersionIdAndInternalOrderByCreatedAtDesc(versionId: Long, internal: Boolean): ChatEntity?
}
