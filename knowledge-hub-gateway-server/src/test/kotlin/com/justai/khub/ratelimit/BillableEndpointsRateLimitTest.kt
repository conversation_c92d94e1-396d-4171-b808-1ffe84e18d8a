package com.justai.khub.ratelimit

import com.justai.khub.common.util.CustomHeaders
import com.justai.khub.util.TestDataFactory.TEST_RPM
import org.junit.jupiter.api.Test
import org.springframework.test.web.servlet.result.MockMvcResultMatchers

class BillableEndpointsRateLimitTestt : BaseLicenseTest() {


    @Test
    fun testNoRateLimitForNotBillableEndpoints() {
        val chat = chatHelper.createChat(project.id)
        repeat(TEST_RPM + 10) {
            chatHelper.getChatsPage(project.id)
            chatPublicHelper.getChat(chat.id, apiKey = apiKey)
        }
    }

    @Test
    fun testRateLimitForBillableEndpoints() {
        billableEndpoints.forEach { endpoint ->
            globalRateLimitingBucket.reset()
            repeat(TEST_RPM) {
                endpoint().andExpect(MockMvcResultMatchers.status().is2xxSuccessful)
            }
            endpoint()
                .andExpect(MockMvcResultMatchers.status().isTooManyRequests)
                .andExpect(MockMvcResultMatchers.header().exists(CustomHeaders.X_LICENSE_RATE_LIMIT_LIMIT))
                .andExpect(MockMvcResultMatchers.header().exists(CustomHeaders.X_LICENSE_RATE_LIMIT_REMAINING))
                .andExpect(MockMvcResultMatchers.header().exists(CustomHeaders.X_LICENSE_RATE_LIMIT_RESET_MS))
                .andExpect(MockMvcResultMatchers.header().exists(CustomHeaders.X_LICENSE_RETRY_AFTER_MS))
        }
    }
}
