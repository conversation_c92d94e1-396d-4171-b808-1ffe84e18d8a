package com.justai.khub.billing.service

import com.fasterxml.jackson.databind.node.ObjectNode
import com.justai.khub.billing.entity.AccountBalanceEntity
import com.justai.khub.billing.entity.BillableOperationEntity
import com.justai.khub.billing.enumeration.BillableOperationStatus
import com.justai.khub.billing.mapper.BillingMapper.initBillableOperation
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import org.springframework.stereotype.Service
import java.time.Instant
import kotlin.math.max
import kotlin.math.min

@Service
class OperationRequestUsageCalculator {

    fun applyUsage(
        balance: AccountBalanceEntity,
        requestsToUse: Long,
        metadata: ObjectNode? = null
    ): Pair<AccountBalanceEntity, BillableOperationEntity> {
        val availableToUseByTariff = max(0, balance.availableRequestsByTariff - balance.usedRequestsByTariff)
        val availableToUseByPackage = max(0, balance.availableRequestsByPackages - balance.usedRequestsByPackages)
        if (availableToUseByPackage + availableToUseByTariff < requestsToUse) throw KhubException(ApiErrorCode.INSUFFICIENT_ACCOUNT_BALANCE)

        val operationRequestsByTariff = min(requestsToUse, availableToUseByTariff)
        val operationRequestsByPackage = min(requestsToUse - operationRequestsByTariff, availableToUseByPackage)
        val operation = initBillableOperation(balance, operationRequestsByTariff, operationRequestsByPackage, metadata)

        balance.usedRequestsByTariff += operation.usedRequestsByTariff
        balance.usedRequestsByPackages += operation.usedRequestsByPackages

        return balance to operation
    }

    fun applyRollback(balance: AccountBalanceEntity, operation: BillableOperationEntity) {
        balance.usedRequestsByTariff -= operation.usedRequestsByTariff
        balance.usedRequestsByPackages -= operation.usedRequestsByPackages
    }
}
