Generate {question_count} conversational QA pairs in {lang} from this text:

Use diverse question types:
- Direct questions (what/who/when)
- Reasoning questions (why/how)
- Opinion/analysis questions
- Follow-up questions

Don't include overly obvious questions or word-for-word text matches.

Return response as json. Response format:
{
  "result": [
    {
      "question": string,
      "ground_truth_answer": string     // Clear response
    }
  ]
}

Text: {content}

Your output:
