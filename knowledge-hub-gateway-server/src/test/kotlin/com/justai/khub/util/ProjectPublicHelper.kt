package com.justai.khub.util

import com.justai.khub.api.public.model.ProjectInfo
import com.justai.khub.util.WebUtils.parseTo
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.stereotype.Component
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@Component
class ProjectPublicHelper {

    @Autowired
    protected lateinit var mvc: MockMvc

    fun getProjectInfo(apiKey: String): ProjectInfo {
        return mvc.perform(
            get("/api/knowledge-hub/info")
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization", "Bearer $apiKey")
        ).andExpect(status().isOk).andReturn().parseTo<ProjectInfo>()
    }

    fun checkProjectNotFound(apiKey: String) {
        mvc.perform(
            get("/api/knowledge-hub/info")
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization", "Bearer $apiKey")
        ).andExpect(status().isNotFound)
    }
}
