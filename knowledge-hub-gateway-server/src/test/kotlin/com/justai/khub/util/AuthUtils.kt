package com.justai.khub.util

import com.justai.security.session.auth.JustSessionAuthentication
import com.justai.security.session.auth.principal.NoOpPrincipalConverter
import com.justai.security.session.data.AccountVerificationStatus
import com.justai.security.session.data.JustSessionUserData
import org.apache.commons.lang.math.RandomUtils
import org.apache.commons.lang3.RandomStringUtils
import java.util.*

object AuthUtils {
    fun mockCCUser(
        ccAccountId: Long? = null,
        authenticated: Boolean = true,
        customFeatures: List<String> = listOf(),
        customPermissions: List<String> = listOf(),
    ): JustSessionAuthentication {
        return JustSessionAuthentication(
            JustSessionUserData(
                accountId = ccAccountId ?: RandomUtils.nextLong(),
                accountShortName = "short-name-${RandomStringUtils.randomAlphabetic(8)}",
                userId = RandomUtils.nextLong(),
                login = "login-${RandomStringUtils.randomAlphabetic(8)}",
                email = "email-${RandomStringUtils.randomAlphabetic(8)}@test.com",
                phone = null,
                fullName = "full-name-${RandomStringUtils.randomAlphabetic(8)}",
                language = "EN",
                accountVerificationStatus = AccountVerificationStatus.ACCOUNT_NOT_VERIFIED,
                phoneVerified = false,
                emailVerified = true,
                emailVerificationRequired = true,
                permissions = customPermissions,
                features = customFeatures,
                internal = false,
                accountOwner = true,
                avatarUrl = null,
                countryIsoCode = null,
                timeZone = null,
                accounts = null,
                impersonator = null
            ),
            NoOpPrincipalConverter(),
            UUID.randomUUID().toString()
        ).apply { isAuthenticated = authenticated }
    }
}
