package com.justai.khub.qa.endpoint

import com.justai.khub.api.fe.QAApiService
import com.justai.khub.api.fe.model.*
import com.justai.khub.common.configuration.LocalFeaturesConfiguration
import com.justai.khub.common.configuration.dto.RagProperties
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.common.util.WebUtils.getCurrentAccountId
import com.justai.khub.qa.dto.TestSetRunFilter
import com.justai.khub.qa.mapper.QAMapper
import com.justai.khub.qa.service.TestSetOrderService
import com.justai.khub.qa.service.TestSetRunService
import com.justai.khub.qa.service.TestSetScheduleService
import com.justai.khub.qa.service.TestSetService
import org.springframework.core.io.ByteArrayResource
import org.springframework.core.io.Resource
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.time.LocalDate

@Service
class QAEndpoint(
    private val testSetService: TestSetService,
    private val testSetRunService: TestSetRunService,
    private val ragProperties: RagProperties,
    private val localFeaturesConfiguration: LocalFeaturesConfiguration,
    private val qaMapper: QAMapper,
    private val testSetScheduleService: TestSetScheduleService,
    private val testSetOrderService: TestSetOrderService,
) : QAApiService {
    val DEFAULT_TEST_SET_NAME = "New test set"
    val DEFAULT_TEST_SET_SORT = "createdAt"
    val DEFAULT_TEST_SET_PAGE_SIZE = 1000

    override fun deleteTestSet(projectId: Long, testSetId: Long) {
        checkEndpointEnabled()
        val currentAccountId = getCurrentAccountId()
        testSetService.deleteTestSet(testSetId, currentAccountId)
    }

    override fun generateTestSet(projectId: Long, generateTestSetRequest: GenerateTestSetRequest): TestSetDTO {
        checkEndpointEnabled()
        val currentAccountId = getCurrentAccountId()
        val newTestSet = testSetService.createTestSet(projectId, generateTestSetRequest, currentAccountId)
        return qaMapper.toDTO(newTestSet, null, 0)
    }

    override fun cancelGeneration(projectId: Long, testSetId: Long) {
        checkEndpointEnabled()
        val currentAccountId = getCurrentAccountId()
        testSetService.cancelGeneration(testSetId, currentAccountId)
    }

    override fun uploadTestSet(projectId: Long, file: MultipartFile, name: String?): TestSetDTO {
        checkEndpointEnabled()
        val currentAccountId = getCurrentAccountId()
        val newTestSet = testSetService.createTestSet(projectId, name ?: DEFAULT_TEST_SET_NAME, file.bytes, currentAccountId)
        return qaMapper.toDTO(newTestSet, null, 0)
    }

    override fun getTestSetGenerationModels(projectId: Long): List<NamedValue> {
        checkEndpointEnabled()
        return ragProperties.llm.models
            .filter { it.qaEnabled }
            .map { NamedValue(title = it.title, value = it.value) }
    }

    override fun getTestSet(projectId: Long, testSetId: Long): TestSetDTO {
        checkEndpointEnabled()
        val currentAccountId = getCurrentAccountId()
        val testSet = testSetService.getTestSet(testSetId, currentAccountId)
        val activeTestSetRun = testSetRunService.findActiveTestSetRun(testSet.id)
        val testSetRunsCount = testSetRunService.findTestSetRunsCount(testSet.id)
        return qaMapper.toDTO(testSet, activeTestSetRun, testSetRunsCount)
    }

    override fun updateTestSet(projectId: Long, testSetId: Long, testSetUpdateRequest: TestSetUpdateRequest): TestSetDTO {
        checkEndpointEnabled()
        val currentAccountId = getCurrentAccountId()
        val testSet = testSetService.updateTestSet(testSetId, testSetUpdateRequest, currentAccountId)
        val activeTestSetRun = testSetRunService.findActiveTestSetRun(testSet.id)
        val testSetRunsCount = testSetRunService.findTestSetRunsCount(testSet.id)
        return qaMapper.toDTO(testSet, activeTestSetRun, testSetRunsCount)
    }

    override fun getTestSetsPage(projectId: Long): TestSetsPage {
        checkEndpointEnabled()
        val pageable = PageRequest.of(0, DEFAULT_TEST_SET_PAGE_SIZE, Sort.by(Sort.Direction.DESC, DEFAULT_TEST_SET_SORT))
        val currentAccountId = getCurrentAccountId()
        val testSets = testSetService.getTestSetsPage(projectId, currentAccountId, pageable)
        val testSetIds = testSets.content.map { it.id }
        val activeTestSetRuns = testSetRunService.findActiveTestSetRuns(testSetIds)
        val testSetRunsCount = testSetRunService.findTestSetRunsCount(testSetIds)
        val mappedPage = qaMapper.toDTO(testSets, activeTestSetRuns, testSetRunsCount)
        return mappedPage.copy(content = testSetOrderService.sort(projectId, mappedPage.content))
    }

    override fun getArchivedTestSetsPage(projectId: Long, pageSize: Int, pageNum: Int): TestSetsPage {
        checkEndpointEnabled()
        val pageable = PageRequest.of(pageNum, pageSize, Sort.by(Sort.Direction.DESC, DEFAULT_TEST_SET_SORT))
        val currentAccountId = getCurrentAccountId()
        val testSets = testSetService.getArchivedTestSetsPage(projectId, currentAccountId, pageable)
        val testSetIds = testSets.content.map { it.id }
        val activeTestSetRuns = testSetRunService.findActiveTestSetRuns(testSetIds)
        val testSetRunsCount = testSetRunService.findTestSetRunsCount(testSetIds)
        return qaMapper.toDTO(testSets, activeTestSetRuns, testSetRunsCount)
    }

    override fun runTestSet(
        projectId: Long,
        testSetId: Long,
        testSetRunRequest: TestSetRunRequest
    ): TestSetRunDTO {
        checkEndpointEnabled()
        val currentAccountId = getCurrentAccountId()
        val testSetRun = testSetService.runTestSet(testSetId, testSetRunRequest, currentAccountId)
        return qaMapper.toDTO(testSetRun)
    }

    override fun cancelTestSetRun(projectId: Long, testSetRunId: Long) {
        checkEndpointEnabled()
        val currentAccountId = getCurrentAccountId()
        val testSetRun = testSetRunService.findTestSetRun(testSetRunId, currentAccountId)
        testSetRunService.cancelTestSetRun(testSetRun)
    }

    override fun getTestSetRun(projectId: Long, testSetRunId: Long): TestSetRunDTO {
        checkEndpointEnabled()
        val currentAccountId = getCurrentAccountId()
        val testSetRun = testSetRunService.findTestSetRun(testSetRunId, currentAccountId)
        return qaMapper.toDTO(testSetRun)
    }

    override fun getTestSetRunsPage(
        projectId: Long,
        pageSize: Int,
        pageNum: Int,
        testSetIds: String?,
        dateFrom: LocalDate?,
        dateTo: LocalDate?
    ): TestSetRunsPage {
        checkEndpointEnabled()
        val currentAccountId = getCurrentAccountId()
        val filter = TestSetRunFilter(
            testSetIds = testSetIds?.split(",")?.map { it.trim().toLong() }?.toSet() ?: setOf(),
            dateFrom = dateFrom,
            dateTo = dateTo
        )
        val pageable = PageRequest.of(pageNum, pageSize, Sort.by(Sort.Direction.DESC, "updatedAt"))
        val testSetRuns = testSetRunService.getTestSetRunsPage(projectId, filter, pageable, currentAccountId)
        return qaMapper.toDTO(testSetRuns)
    }

    override fun getTestSetRunReportResponseEntity(projectId: Long, testSetRunId: Long): ResponseEntity<Resource> {
        checkEndpointEnabled()
        val currentAccountId = getCurrentAccountId()
        val (testSetName, fileContent) = testSetRunService.generateReport(testSetRunId, currentAccountId)
        return toFileResponse("$testSetName-report.xlsx", fileContent)
    }

    override fun downloadTestSetResponseEntity(projectId: Long, testSetId: Long): ResponseEntity<Resource> {
        checkEndpointEnabled()
        val currentAccountId = getCurrentAccountId()
        val (testSetName, fileContent) = testSetService.generateFile(testSetId, currentAccountId)
        return toFileResponse("$testSetName.xlsx", fileContent)
    }

    override fun downloadTestSetSampleResponseEntity(projectId: Long): ResponseEntity<Resource> {
        checkEndpointEnabled()
        val content = testSetService.getTestSetSample()
        return toFileResponse("test-case-sample.xlsx", content)
    }

    override fun archiveTestSet(projectId: Long, testSetId: Long) {
        checkEndpointEnabled()
        val currentAccountId = getCurrentAccountId()
        testSetService.changeArchivedStatus(testSetId, true, currentAccountId)
    }

    override fun unArchiveTestSet(projectId: Long, testSetId: Long) {
        checkEndpointEnabled()
        val currentAccountId = getCurrentAccountId()
        testSetService.changeArchivedStatus(testSetId, false, currentAccountId)
    }

    fun toFileResponse(fileName: String, fileContent: ByteArray): ResponseEntity<Resource> {
        checkEndpointEnabled()
        val encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString())
        return ResponseEntity.ok()
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"$encodedFileName\"")
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .contentLength(fileContent.size.toLong())
            .body(ByteArrayResource(fileContent))
    }

    override fun getTestSetScheduleResponseEntity(projectId: Long, testSetId: Long): ResponseEntity<TestSetScheduleDTO> {
        checkEndpointEnabled()
        val currentAccountId = getCurrentAccountId()
        val schedule = testSetScheduleService.getTestSetSchedule(testSetId, currentAccountId)
        return if (schedule == null) {
            ResponseEntity.noContent().build()
        } else {
            ResponseEntity.ok(qaMapper.toDTO(schedule))
        }
    }

    override fun setTestSetSchedule(projectId: Long, testSetId: Long, testSetScheduleDTO: TestSetScheduleDTO): TestSetScheduleDTO {
        checkEndpointEnabled()
        val currentAccountId = getCurrentAccountId()
        val schedule = testSetScheduleService.setTestSetSchedule(testSetId, testSetScheduleDTO, currentAccountId)
        return qaMapper.toDTO(schedule)
    }

    override fun deleteTestSetSchedule(projectId: Long, testSetId: Long) {
        checkEndpointEnabled()
        val currentAccountId = getCurrentAccountId()
        testSetScheduleService.deleteTestSetSchedule(testSetId, currentAccountId)
    }

    override fun setTestSetsOrder(projectId: Long, testSetOrderDTO: TestSetOrderDTO) {
        checkEndpointEnabled()
        val currentAccountId = getCurrentAccountId()
        testSetOrderService.setOrder(projectId, testSetOrderDTO.ids, currentAccountId)
    }

    override fun getDefaultTestSetSettings(projectId: Long): TestSetSettingsDTO {
        checkEndpointEnabled()
        return qaMapper.getDefaultTestSetSettings()
    }

    private fun checkEndpointEnabled() {
        if (!localFeaturesConfiguration.qaModuleEnabled) {
            throw KhubException(ApiErrorCode.NOT_FOUND)
        }
    }
}
