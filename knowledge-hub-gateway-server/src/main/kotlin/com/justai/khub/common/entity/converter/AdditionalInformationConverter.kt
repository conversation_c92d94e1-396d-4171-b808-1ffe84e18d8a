package com.justai.khub.common.entity.converter

import com.justai.khub.common.dto.AdditionalGenerationInformation
import com.justai.khub.common.util.JSON
import jakarta.persistence.AttributeConverter
import org.slf4j.LoggerFactory

class AdditionalInformationConverter : AttributeConverter<AdditionalGenerationInformation, String> {
    private val log = LoggerFactory.getLogger(this::class.java)

    override fun convertToDatabaseColumn(addInfo: AdditionalGenerationInformation?): String? {
        return addInfo?.let { JSON.stringify(addInfo) }
    }

    override fun convertToEntityAttribute(addInfoStr: String?): AdditionalGenerationInformation? {
        return try {
            addInfoStr?.let { JSON.parse<AdditionalGenerationInformation>(addInfoStr) }
        } catch (ex: Exception) {
            log.error("Can`not deserialize addInfo to object: $addInfoStr", ex)
            null
        }
    }
}
