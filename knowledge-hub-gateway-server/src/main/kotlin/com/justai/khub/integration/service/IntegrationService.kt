package com.justai.khub.integration.service

import com.justai.khub.api.fe.model.IntegrationDTO
import com.justai.khub.common.configuration.dto.IntegrationProperties
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.integration.dto.*
import com.justai.khub.integration.entity.IntegrationEntity
import com.justai.khub.integration.enumeration.IntegrationStatus
import com.justai.khub.integration.job.IntegrationCheckJob
import com.justai.khub.integration.mapper.IntegrationMapper.toEntity
import com.justai.khub.integration.repository.IntegrationRepository
import com.justai.khub.integration.validator.IntegrationValidator
import com.justai.khub.project.entity.ProjectEntity
import com.justai.khub.project.repository.ProjectFileRepository
import com.justai.khub.project.service.ProjectVersionService
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Lazy
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.jpa.repository.query.EscapeCharacter
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.time.LocalDateTime
import java.util.*
import kotlin.jvm.optionals.getOrNull

@Service
class IntegrationService(
    private val integrationRepository: IntegrationRepository,
    private val projectVersionService: ProjectVersionService,
    private val projectFileRepository: ProjectFileRepository,
    private val integrationProperties: IntegrationProperties,
    private val integrationValidator: IntegrationValidator,
    @Value("\${eureka.instance.instance-id}")
    private val instanceId: String,
    @Lazy private val integrationCheckJob: IntegrationCheckJob? = null
) {
    private val log = LoggerFactory.getLogger(this::class.java)

    @Transactional(rollbackFor = [Exception::class])
    fun createIntegration(
        createRequest: IntegrationDTO,
        projectId: Long,
        versionName: String,
        currentAccountId: Long
    ): IntegrationEntity {
        val version = projectVersionService.getVersion(projectId, versionName, currentAccountId)
        val newIntegration = createRequest.toEntity(version, integrationProperties)
        integrationValidator.validate(newIntegration)
        (newIntegration.settings as? CloudConfluenceIntegrationSettings)?.let { settings ->
            newIntegration.settings = settings.copy(authState = UUID.randomUUID().toString(), token = null) // make unique object to protect OAuth requests
            newIntegration.status = IntegrationStatus.AWAITING_ACTION
        }
        val createdIntegration = integrationRepository.saveAndFlush(newIntegration)

        // Schedule a timeout job for OAuth integrations
        if (createdIntegration.status == IntegrationStatus.AWAITING_ACTION) {
            integrationCheckJob?.scheduleAuthTimeout(createdIntegration)
        }

        return createdIntegration
    }

    @Transactional(readOnly = true)
    fun getIntegration(integrationId: Long, accountId: Long): IntegrationEntity {
        val integration = integrationRepository.findById(integrationId).getOrNull() ?: throw KhubException(ApiErrorCode.INTEGRATION_NOT_FOUND)
        if (integration.createdBy.accountId != accountId) throw KhubException(ApiErrorCode.ACCESS_DENIED)
        return integration
    }

    @Transactional(readOnly = true)
    fun findIntegrationsByIds(ids: Iterable<Long>): List<IntegrationEntity> {
        return integrationRepository.findAllById(ids)
    }

    @Transactional(readOnly = true)
    fun getIntegrations(filter: IntegrationFilter): Page<IntegrationEntity> {
        val statusFilter = filter.statuses.ifEmpty { IntegrationStatus.entries }
        val version = projectVersionService.getVersion(filter.projectId, filter.versionName, filter.accountId)
        return if (filter.namePart.isNullOrEmpty())
            integrationRepository.findAllByVersionIdAndStatusIn(version.id, statusFilter, filter.pageable)
        else {
            val escapedName = EscapeCharacter.of('\\').escape(filter.namePart)!!
            integrationRepository.findAllByVersionIdAndNameContainsAndStatusIn(
                version.id,
                statusFilter,
                escapedName,
                filter.pageable
            )
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    fun deleteIntegration(integrationId: Long, accountId: Long): Boolean {
        val integration = integrationRepository.findById(integrationId).getOrNull() ?: return false
        if (integration.createdBy.accountId != accountId) {
            throw KhubException(ApiErrorCode.ACCESS_DENIED)
        }
        integrationRepository.delete(integration)
        return true
    }

    @Transactional(rollbackFor = [Exception::class])
    fun updateIntegration(updateRequest: IntegrationEntity, currentAccountId: Long): Pair<IntegrationEntity, Boolean> {
        integrationValidator.validate(updateRequest)
        val integration = getIntegration(updateRequest.id, currentAccountId)
        val settingsUpdated = integration.settings != updateRequest.settings
        val oldIntegrationName = integration.name
        val wasAwaitingAction = integration.status == IntegrationStatus.AWAITING_ACTION

        integration.apply {
            autoIngest = updateRequest.autoIngest
            autoSync = updateRequest.autoSync
            (integration.settings as? CloudConfluenceIntegrationSettings)?.let { settings ->
                integration.status = IntegrationStatus.AWAITING_ACTION
            }
            settings = settings.updatedWith(updateRequest.settings)
            name = updateRequest.name
            checkIntervalMinutes = updateRequest.checkIntervalMinutes
        }
        if (settingsUpdated) {
            integration.errorsCount = 0
        }

        val updatedIntegration = integrationRepository.saveAndFlush(integration)
        if (oldIntegrationName != updatedIntegration.name) {
            projectFileRepository.updateRelativePathForIntegration(
                updatedIntegration.id,
                oldIntegrationName,
                updatedIntegration.name
            )
        }

        if (updatedIntegration.status == IntegrationStatus.AWAITING_ACTION &&
            (settingsUpdated || !wasAwaitingAction)) {
            integrationCheckJob?.cancelAuthTimeout(updatedIntegration.id)
            integrationCheckJob?.scheduleAuthTimeout(updatedIntegration)
            log.info("Scheduled auth timeout job for updated integration {}", updatedIntegration.id)
        }

        return updatedIntegration to settingsUpdated
    }

    @Transactional(rollbackFor = [Exception::class])
    fun releaseLockedActiveChecks(): Int {
        return integrationRepository.updateByStatusAndLastLockedBy(
            IntegrationStatus.CHECKING,
            IntegrationStatus.ACTIVE,
            instanceId
        )
    }

    @Transactional(rollbackFor = [Exception::class])
    fun releaseAllTimedOutChecks(lastUpdateBefore: LocalDateTime): Int {
        return integrationRepository.releaseTimedOutChecks(
            IntegrationStatus.CHECKING,
            IntegrationStatus.ACTIVE,
            lastUpdateBefore
        )
    }

    @Transactional(readOnly = true)
    fun getReadyToCheck(limit: Int): List<IntegrationEntity> {
        val pagination = PageRequest.of(0, limit, Sort.by(Sort.Direction.ASC, "last_checked_at"))
        return integrationRepository.getReadyToCheck(
            listOf(IntegrationStatus.ACTIVE, IntegrationStatus.FAILED).map { it.name },
            integrationProperties.checkErrorsLimit,
            pagination
        ).content
    }

    @Transactional(rollbackFor = [Exception::class])
    fun startChecking(integration: IntegrationEntity): Boolean {
        return integrationRepository.startChecking(
            integration.id,
            listOf(IntegrationStatus.ACTIVE, IntegrationStatus.FAILED),
            IntegrationStatus.CHECKING,
            instanceId
        ) > 0
    }

    @Transactional(rollbackFor = [Exception::class])
    fun finishChecking(integration: IntegrationEntity): Boolean {
        return integrationRepository.updateStatusAndClearErrors(integration.id, IntegrationStatus.CHECKING, IntegrationStatus.ACTIVE) > 0
    }

    @Transactional(rollbackFor = [Exception::class])
    fun failChecking(integration: IntegrationEntity, lastError: String): Boolean {
        return integrationRepository.updateStatusAndErrorsCount(
            integration.id,
            IntegrationStatus.CHECKING,
            IntegrationStatus.FAILED,
            lastError
        ) > 0
    }

    @Transactional(rollbackFor = [Exception::class])
    fun disableIntegrationsByProject(project: ProjectEntity) {
        integrationRepository.updateAutoSyncByProjectId(project.id, false)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun findIntegrationByAuthState(authState: String) =
        integrationRepository.getIntegrationEntityBySettingsAuthState(authState = authState)
            ?: throw KhubException(ApiErrorCode.INTEGRATION_NOT_FOUND)

    @Transactional(rollbackFor = [Exception::class])
    fun saveOAuthAccess(authState: String, accessToken: String, expiresIn: Int, refreshToken: String, cloudIds: Map<String, String>): IntegrationEntity {
        val entity = integrationRepository.getIntegrationEntityBySettingsAuthState(authState = authState)
            ?: throw KhubException(ApiErrorCode.INTEGRATION_NOT_FOUND)

        val settings = entity.settings
        require(settings is HasOAuthToken) { "Tried to save oauth data on non-oauth integration" }

        entity.settings = when (settings) {
            is CloudConfluenceIntegrationSettings -> {
                val selectedCloudId = cloudIds.entries.find { it.value == settings.siteUrl }?.key
                settings.copy(
                    token = accessToken,
                    expiresAt = Instant.now().epochSecond + expiresIn,
                    refreshToken = refreshToken,
                    selectedCloudId = selectedCloudId,
                )
            }
        }

        // If the integration was awaiting action, update its status and cancel the timeout job
        if (entity.status == IntegrationStatus.AWAITING_ACTION) {
            entity.status = IntegrationStatus.ACTIVE
            // Cancel the timeout job since authentication was successful
            integrationCheckJob?.cancelAuthTimeout(entity.id)
            log.info("OAuth authentication successful for integration {}, canceled timeout job", entity.id)
        }

        return integrationRepository.saveAndFlush(entity)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun updateOAuthToken(integration: IntegrationEntity, newToken: String, expiresIn: Int, refreshToken: String) {
        val entity = integrationRepository.findById(integration.id).getOrNull()
            ?: throw KhubException(ApiErrorCode.INTEGRATION_NOT_FOUND)

        val settings = entity.settings
        require(settings is HasOAuthToken) { "Non-oauth token not meant to be updated alongside with refresh token)" }

        entity.settings = when (settings) {
            is CloudConfluenceIntegrationSettings -> settings.copy(
                token = newToken,
                expiresAt = Instant.now().epochSecond + expiresIn,
                refreshToken = refreshToken,
            )
        }
        integrationRepository.saveAndFlush(entity)
    }

    /**
     * Updates the status of an integration and saves it to the database.
     * This is used for updating the status of integrations that have timed out during OAuth authentication.
     */
    @Transactional(rollbackFor = [Exception::class])
    fun oauthTimeout(integration: IntegrationEntity, lastError: String): IntegrationEntity {
        val updated = integrationRepository.updateStatusAndError(
            id = integration.id,
            expectedStatus = IntegrationStatus.AWAITING_ACTION,
            newStatus = IntegrationStatus.FAILED,
            lastError = lastError,
        ) > 0

        if (!updated) {
            log.warn("Failed to update status to timed out for integration {}", integration.id)
        }

        return integration
    }

    @Transactional
    fun updateSpaceKey(integration: IntegrationEntity, spaceKey: String) {
        integrationRepository.updateSpaceKey(id = integration.id, spaceKey = spaceKey)
    }

    private fun IntegrationSettings.updatedWith(newSettings: IntegrationSettings): IntegrationSettings = when (this) {
        is CloudConfluenceIntegrationSettings -> {
            require(newSettings is CloudConfluenceIntegrationSettings)
            copy(
                baseUrl = newSettings.baseUrl,
                authBaseUrl = newSettings.authBaseUrl,
                space = newSettings.space,
                siteUrl = newSettings.siteUrl,
                token = null, // Because on every update we are triggering the new authentication process
                refreshToken = null,
            )
        }
        is ConfluenceIntegrationSettings, is MinervaIntegrationSettings -> newSettings
    }
}
