package com.justai.khub.common.dto.rag

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming


@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class QueryResponse(
    val answer: String,
    val completionTokens: Int? = null,
    val promptTokens: Int? = null,
    val relevantSources: List<String>? = null,
    val usedChunks: List<ChunkResponse> = listOf(),
)
