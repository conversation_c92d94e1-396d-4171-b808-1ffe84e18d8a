package com.justai.khub.common

import com.justai.khub.BaseIT
import com.justai.khub.common.dto.PromptRule
import com.justai.khub.common.service.PromptsService
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class PromptServiceIT : BaseIT() {
    @Autowired
    private lateinit var promptsService: PromptsService

    @Test
    fun testAddRelevantDocsToUserPrompt() {
        val userPrompt = """
            You are a Russian-speaking question answering system. You help users look for information in the Documentation.
            Try to answer their Question or find documents related to the Question according to the Documentation provided below.
            Each document starts from New document.
            Follow the rules:
            - (1) Firstly, find relevant documents related to the Question. It is possible that there are no related documents in the Documentation.
            - (2) Answer strictly according to the documents. Don't make things up.
            - (3) Question can be formulated as either an ordinary question with a question mark or words (когда работает банк?) or a search query (режим работы банка). You must deal with all types.
            - (4) If there is more than one answer to the Question, list all possible solutions separately.
            - (5) If there is not a direct Answer, but just information on the related topic, answer with it.
            - (6) If you can't find relevant documents, ask a user to reformulate the Question in your answer.
            - (7) Answer the Question in Russian.
            - (8) If you see a URL to an image in Documentation, use it in answer in your response.
        """.trimIndent()

        val userPromptWithRelevantDocs = promptsService.addRulesAndGet(userPrompt, listOf(promptsService.relevantDocsInAnswerRule()))

        assertEquals("""
            You are a Russian-speaking question answering system. You help users look for information in the Documentation.
            Try to answer their Question or find documents related to the Question according to the Documentation provided below.
            Each document starts from New document.
            Follow the rules:
            - (1) Firstly, find relevant documents related to the Question. It is possible that there are no related documents in the Documentation.
            - (2) Answer strictly according to the documents. Don't make things up.
            - (3) Question can be formulated as either an ordinary question with a question mark or words (когда работает банк?) or a search query (режим работы банка). You must deal with all types.
            - (4) If there is more than one answer to the Question, list all possible solutions separately.
            - (5) If there is not a direct Answer, but just information on the related topic, answer with it.
            - (6) If you can't find relevant documents, ask a user to reformulate the Question in your answer.
            - (7) Answer the Question in Russian.
            - (8) If you see a URL to an image in Documentation, use it in answer in your response.
            - (9) If you use relevant documents in your answer, add their to result in your response.
        """.trimIndent(), userPromptWithRelevantDocs)
    }

    @Test
    fun testAddCustomRuleToPrompt() {
        val userPrompt = """
            You are a Russian-speaking question answering system. You help users look for information in the Documentation.
            Try to answer their Question or find documents related to the Question according to the Documentation provided below.
            Each document starts from New document.
            Follow the rules:
            - (1) Firstly, find relevant documents related to the Question. It is possible that there are no related documents in the Documentation.
            - (2) Answer strictly according to the documents. Don't make things up.
            - (3) Question can be formulated as either an ordinary question with a question mark or words (когда работает банк?) or a search query (режим работы банка). You must deal with all types.

            ----
            Some other text
        """.trimIndent()

        val userPromptWithCustomRule = promptsService.addRulesAndGet(
            userPrompt,
            listOf(
                PromptRule("You are peace of cake!"),
                PromptRule("And U are tasty!"),
            ))

        assertEquals("""
            You are a Russian-speaking question answering system. You help users look for information in the Documentation.
            Try to answer their Question or find documents related to the Question according to the Documentation provided below.
            Each document starts from New document.
            Follow the rules:
            - (1) Firstly, find relevant documents related to the Question. It is possible that there are no related documents in the Documentation.
            - (2) Answer strictly according to the documents. Don't make things up.
            - (3) Question can be formulated as either an ordinary question with a question mark or words (когда работает банк?) or a search query (режим работы банка). You must deal with all types.
            - (4) You are peace of cake!
            - (5) And U are tasty!

            ----
            Some other text
        """.trimIndent(), userPromptWithCustomRule)
    }


    @Test
    fun testRemoveCustomRuleFromPrompt() {
        val userPrompt = """
            You are a Russian-speaking question answering system. You help users look for information in the Documentation.
            Try to answer their Question or find documents related to the Question according to the Documentation provided below.
            Each document starts from New document.
            Follow the rules:
            - (1) Firstly, find relevant documents related to the Question. It is possible that there are no related documents in the Documentation.
            - (2) Answer strictly according to the documents. Don't make things up.
            - (3) Question can be formulated as either an ordinary question with a question mark or words (когда работает банк?) or a search query (режим работы банка). You must deal with all types.
            - (4) You are peace of cake!
            - (5) And U are tasty!

            ----
            Some other text
        """.trimIndent()

        val userPromptWithCustomRule = promptsService.removeRulesAndGet(
            userPrompt,
            listOf(
                PromptRule("You are peace of cake!"),
                PromptRule("And U are tasty!"),
            ))

        assertEquals("""
            You are a Russian-speaking question answering system. You help users look for information in the Documentation.
            Try to answer their Question or find documents related to the Question according to the Documentation provided below.
            Each document starts from New document.
            Follow the rules:
            - (1) Firstly, find relevant documents related to the Question. It is possible that there are no related documents in the Documentation.
            - (2) Answer strictly according to the documents. Don't make things up.
            - (3) Question can be formulated as either an ordinary question with a question mark or words (когда работает банк?) or a search query (режим работы банка). You must deal with all types.

            ----
            Some other text
        """.trimIndent(), userPromptWithCustomRule)
    }
}
