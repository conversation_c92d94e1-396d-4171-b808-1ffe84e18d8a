package com.justai.khub.project.service

import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.project.entity.ApiKeyEntity
import com.justai.khub.user.enumeration.ApiKeyStatus
import com.justai.khub.project.repository.ApiKeyRepository
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.security.crypto.bcrypt.BCrypt
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import kotlin.jvm.optionals.getOrNull

@Service
class ApiKeyService(
    private val apiKeyRepository: ApiKeyRepository
) {
    @Transactional(rollbackFor = [Exception::class])
    fun createApiKey(newApiKey: ApiKeyEntity): ApiKeyEntity {
        return apiKeyRepository.saveAndFlush(newApiKey)
    }

    @Transactional(readOnly = true)
    fun getApiKeys(onlyActive: Boolean, projectId: Long, accountId: Long, pageable: PageRequest): Page<ApiKeyEntity> {
        return if (onlyActive) {
            apiKeyRepository.findActiveByAccountIdAndProjectId(
                accountId,
                projectId,
                ApiKeyStatus.ACTIVE,
                LocalDateTime.now(),
                pageable
            )
        } else {
            apiKeyRepository.findAllByAccountIdAndProjectId(accountId, projectId, pageable)
        }
    }

    @Transactional(readOnly = true)
    fun getApiKey(apiKeyId: Long): ApiKeyEntity {
        return apiKeyRepository.findById(apiKeyId).getOrNull() ?: throw KhubException(ApiErrorCode.API_KEY_NOT_FOUND)
    }

    @Transactional(readOnly = true)
    fun checkActiveApiKeyByName(projectId: Long, accountId: Long, name: String): Boolean {
        val tokens = apiKeyRepository.findActiveByAccountIdAndProjectIdAndName(
            accountId,
            projectId,
            name,
            ApiKeyStatus.ACTIVE,
        )
        return tokens.isNotEmpty()
    }

    @Transactional(rollbackFor = [Exception::class])
    fun revokeApiKey(apiKeyId: Long, accountId: Long): ApiKeyEntity {
        val apiKey = apiKeyRepository.findById(apiKeyId).getOrNull() ?: throw KhubException(ApiErrorCode.API_KEY_NOT_FOUND)
        if (apiKey.createdBy.accountId != accountId) throw KhubException(ApiErrorCode.ACCESS_DENIED)
        if (apiKey.status == ApiKeyStatus.REVOKED) return apiKey
        apiKey.status = ApiKeyStatus.REVOKED
        return apiKeyRepository.saveAndFlush(apiKey)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun touchAndValidateApiKey(apiKeyId: Long, apiKeyValue: String): ApiKeyEntity? {
        val entity = apiKeyRepository.findById(apiKeyId).getOrNull() ?: return null
        if (apiKeyValue != entity.value) return null
        if (
            (entity.expiredAt != null && entity.expiredAt!!.isBefore(LocalDateTime.now()))
            || entity.status != ApiKeyStatus.ACTIVE
        ) {
            return null
        }
        entity.lastUsedAt = LocalDateTime.now()
        return apiKeyRepository.saveAndFlush(entity)
    }
}
