package com.justai.khub.util

import com.justai.accountsadmin.entities.TestDto.UserWithAccount
import com.justai.khub.api.model.ApiKeyCreateRequest
import com.justai.khub.api.model.ProjectCreateRequest
import org.apache.commons.lang3.RandomStringUtils
import org.junit.Assert
import org.springframework.core.io.ClassPathResource
import java.time.Instant

object TestDataGenerator {
    val DEFAULT_VERSION = "main"
    val TEST_PASSWORD = "123123qweQWE"

    fun randomEmail(): String {
        return "bbt_${RandomStringUtils.secure().nextAlphanumeric(10)}@just-ai.com".lowercase()
    }

    fun randomName(): String {
        return "bbt_${RandomStringUtils.secure().nextAlphanumeric(10)}".lowercase()
    }

    fun randomText(n: Int = 20): String {
        return RandomStringUtils.secure().nextAlphabetic(n)
    }

    fun testAccount(
        login: String = randomEmail(),
        password: String = TEST_PASSWORD,
        productName: String = "botadmin",
        features: List<String>? = null,
        strictFeatures: Boolean = false,
        roles: List<String>? = null,
        emailVerificationRequired: Boolean = false,
        initializeAccountInServices: Boolean = false,
    ): UserWithAccount.Create {
        return UserWithAccount.Create().also {
            it.login = login
            it.password = password
            it.accountShortName = randomName()
            it.productName = productName
            it.features = features
            it.strictFeatures = strictFeatures
            it.roles = roles
            it.emailVerificationRequired = emailVerificationRequired
            it.initializeAccountInServices = initializeAccountInServices
        }
    }

    fun testProject(name: String = randomName()): ProjectCreateRequest {
        return ProjectCreateRequest().also {
            it.name = name
        }
    }

    fun testFile(fileName: String): ClassPathResource {
        return ClassPathResource("/files/$fileName").also {
            if (!it.file.exists()) {
                Assert.fail("Can't load test file: $fileName")
            }
        }
    }

    fun testApiKey(name: String = randomName(), expiredAt: Instant? = null): ApiKeyCreateRequest {
        return ApiKeyCreateRequest().also {
            it.name = name
            it.expiredAt = expiredAt
        }
    }
}
