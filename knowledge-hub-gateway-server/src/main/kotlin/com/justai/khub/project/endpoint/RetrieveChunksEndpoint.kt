package com.justai.khub.project.endpoint

import com.justai.khub.api.fe.RetrieveApiService
import com.justai.khub.api.fe.model.RetrieveChunksRequest
import com.justai.khub.api.fe.model.RetrievedChunksDTO
import com.justai.khub.common.util.WebUtils.getCurrentAccountId
import com.justai.khub.integration.dto.CloudConfluenceIntegrationSettings
import com.justai.khub.integration.dto.ConfluenceIntegrationSettings
import com.justai.khub.integration.dto.IntegrationFilter
import com.justai.khub.integration.enumeration.IntegrationStatus
import com.justai.khub.integration.mapper.IntegrationMapper.DEFAULT_PAGINATION
import com.justai.khub.integration.service.IntegrationService
import com.justai.khub.common.dto.DocumentChunkWithSource
import com.justai.khub.chat.mapper.ChatMapper
import com.justai.khub.common.dto.rag.RetrieveChunksResponse
import com.justai.khub.project.mapper.ProjectMapper.toDTO
import com.justai.khub.project.mapper.ProjectVersionMapper
import com.justai.khub.project.service.RetrieveChunksService
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service

@Service
class RetrieveChunksEndpoint(
    private val retrieveChunksService: RetrieveChunksService,
) : RetrieveApiService {

    override fun retrieveChunks(
        projectId: Long,
        retrieveChunksRequest: RetrieveChunksRequest,
        version: String?,
    ): RetrievedChunksDTO {
        val currentAccountId = getCurrentAccountId()
        val actualVersion = version ?: ProjectVersionMapper.DEFAULT_VERSION
        val chunks = retrieveChunksService.retrieveChunks(retrieveChunksRequest.query, projectId, actualVersion, retrieveChunksRequest.topK, currentAccountId)
        return chunks.toDTO()
    }

    override fun retrieveChunksForDefaultChat(
        projectId: Long,
        retrieveChunksRequest: RetrieveChunksRequest,
        version: String?
    ): RetrievedChunksDTO {
        val currentAccountId = getCurrentAccountId()
        val actualVersion = version ?: ProjectVersionMapper.DEFAULT_VERSION
        val chunks = retrieveChunksService.retrieveChunksForDefaultChat(
            retrieveChunksRequest.query,
            projectId,
            actualVersion,
            retrieveChunksRequest.topK,
            currentAccountId
        )
        return chunks.toDTO()
    }

    override fun retrieveChunksForRecordResponseEntity(projectId: Long, chatId: Long, recordId: Long): ResponseEntity<RetrievedChunksDTO> {
        val currentAccountId = getCurrentAccountId()
        val chunks = retrieveChunksService.retrieveChunksForRecord(projectId, chatId, recordId, currentAccountId)
        return if (chunks.isNullOrEmpty()) {
            ResponseEntity(HttpStatus.NO_CONTENT)
        } else {
            ResponseEntity(chunks.toDTO(), HttpStatus.OK)
        }
    }
}
