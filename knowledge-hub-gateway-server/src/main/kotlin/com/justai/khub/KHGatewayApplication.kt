package com.justai.khub

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
import org.springframework.boot.runApplication
import org.springframework.retry.annotation.EnableRetry

@SpringBootApplication(scanBasePackages = ["com.justai.khub"], exclude = [MongoAutoConfiguration::class])
@EnableRetry
class KHGatewayApplication

fun main(args: Array<String>) {
    System.setProperty("user.timezone", "UTC")
    System.setProperty("file.encoding", "UTF-8")
    runApplication<KHGatewayApplication>(*args)
}

object Dev {
    @JvmStatic
    fun main(args: Array<String>) {
        System.setProperty("spring.profiles.active", "dev,local")
        System.setProperty("spring.config.location", "knowledge-hub-gateway-server/src/main/conf/")
        System.setProperty("logging.config", "knowledge-hub-gateway-server/src/main/conf/logback.xml")

        com.justai.khub.main(args)
    }
}
