package com.justai.khub.billing

import com.justai.khub.BaseIT
import com.justai.khub.billing.dto.AccountBalanceResponse
import com.justai.khub.billing.dto.TariffUpdateDto
import com.justai.khub.util.AuthUtils
import com.justai.khub.util.TestDataFactory.testBalanceUpdate
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class AccountBalanceIT : BaseIT() {

    var testAccountId: Long = -1

    @BeforeEach
    fun init() {
        testAccountId = AuthUtils.mockCCUser().userData.accountId!!
    }

    @Test
    fun testInitialBalanceSet() {
        val initialBalance = billingHelper.getAccountBalance(testAccountId)
        assertNull(initialBalance)

        val update = testBalanceUpdate(testAccountId)
        val updatedBalance = billingHelper.updateAccountBalance(update)
        assertAvailableRequestsMatched(updatedBalance, update)
        assertNoUsage(updatedBalance)

        val foundBalance = billingHelper.getAccountBalance(testAccountId)
        assertNotNull(foundBalance)
        assertAvailableRequestsMatched(foundBalance!!, update)
        assertNoUsage(foundBalance)
    }

    @Test
    fun testUpdateWithoutUsage() {
        val firstUpdate = testBalanceUpdate(testAccountId)
        var balance = billingHelper.updateAccountBalance(firstUpdate)
        assertAvailableRequestsMatched(balance, firstUpdate)
        val secondUpdate = firstUpdate.copy(
            currentRequestsByTariff = firstUpdate.currentRequestsByTariff * 2,
            totalRequestsByPackages = firstUpdate.totalRequestsByPackages * 2,
            operationsSuspended = !firstUpdate.operationsSuspended,
            tariffVersion = firstUpdate.tariffVersion + 1
        )
        balance = billingHelper.updateAccountBalance(secondUpdate)
        assertAvailableRequestsMatched(balance, secondUpdate)
        assertNoUsage(balance)
    }

    @Test
    fun testNoTariffUpdateIfVersionNotChanged() {
        val firstUpdate = testBalanceUpdate(testAccountId)
        var balance = billingHelper.updateAccountBalance(firstUpdate)
        assertAvailableRequestsMatched(balance, firstUpdate)
        val secondUpdate = firstUpdate.copy(
            tariffVersion = firstUpdate.tariffVersion,
            currentRequestsByTariff = firstUpdate.currentRequestsByTariff * 2,
            totalRequestsByPackages = firstUpdate.totalRequestsByPackages * 2,
            operationsSuspended = !firstUpdate.operationsSuspended,
        )
        balance = billingHelper.updateAccountBalance(secondUpdate)
        // tariff requests not changed
        assertEquals(firstUpdate.tariffVersion, balance.tariffVersion)
        assertEquals(firstUpdate.currentRequestsByTariff, balance.khubRequestsTariffLimit)
        // package request changed
        assertEquals(secondUpdate.totalRequestsByPackages, balance.khubRequestsPackageLimit)
    }

    @Test
    fun testUsedRequestsByTariffUpdatedWhenTariffVersionChanged() {
        val firstUpdate = testBalanceUpdate(testAccountId)
        val initialBalance = billingHelper.updateAccountBalance(firstUpdate)

        val balanceWithUsedRequests = billingHelper.useTariffRequests(initialBalance.accountId, firstUpdate.currentRequestsByTariff / 2)
        assertEquals(firstUpdate.currentRequestsByTariff / 2, balanceWithUsedRequests.khubRequestsUsedTariffLimit)

        val secondUpdate = firstUpdate.copy(tariffVersion = firstUpdate.tariffVersion + 1)
        val updatedBalance = billingHelper.updateAccountBalance(secondUpdate)

        assertEquals(0, updatedBalance.khubRequestsUsedTariffLimit)
        assertEquals(balanceWithUsedRequests.khubRequestsUsedPackageLimit, updatedBalance.khubRequestsUsedPackageLimitWhenTariffPeriodStarted)
    }

    @Test
    fun testOverdraftProcessing() {
        val firstUpdate = testBalanceUpdate(testAccountId)
        val initialBalance = billingHelper.updateAccountBalance(firstUpdate)

        val balanceWithOverdraft = billingHelper.usePackageRequests(initialBalance.accountId, firstUpdate.totalRequestsByPackages + firstUpdate.currentRequestsByTariff)
        assertEquals(firstUpdate.totalRequestsByPackages + firstUpdate.currentRequestsByTariff, balanceWithOverdraft.khubRequestsUsedPackageLimit)

        val secondUpdate = firstUpdate.copy(tariffVersion = firstUpdate.tariffVersion + 1)
        val updatedBalance = billingHelper.updateAccountBalance(secondUpdate)
        // expect all overdraft was "paid" from updated tariffs request
        assertEquals(updatedBalance.khubRequestsTariffLimit, updatedBalance.khubRequestsUsedTariffLimit)
        assertEquals(secondUpdate.totalRequestsByPackages, updatedBalance.khubRequestsUsedPackageLimit)
    }

    private fun assertAvailableRequestsMatched(actual: AccountBalanceResponse, expected: TariffUpdateDto) {
        assertEquals(expected.tariffVersion, actual.tariffVersion)
        assertEquals(expected.currentRequestsByTariff, actual.khubRequestsTariffLimit)
        assertEquals(expected.totalRequestsByPackages, actual.khubRequestsPackageLimit)
    }

    private fun assertNoUsage(balance: AccountBalanceResponse) {
        assertEquals(0, balance.khubRequestsUsedPackageLimitWhenTariffPeriodStarted)
        assertEquals(0, balance.khubRequestsUsedTariffLimit)
        assertEquals(0, balance.khubRequestsUsedPackageLimit)
    }
}
