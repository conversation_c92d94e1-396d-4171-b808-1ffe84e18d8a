package com.justai.khub.common.service.e2e

import com.justai.e2echeck.E2ECheck.STATUS_OK
import com.justai.e2echeck.E2EComponentDTO
import com.justai.khub.common.connector.LakefsConnector
import com.justai.khub.common.dto.ServiceVersion
import org.springframework.stereotype.Component
import java.time.Instant

@Component
class LakeFsE2EChecker(
    private val lakefsConnector: LakefsConnector
) : E2<PERSON><PERSON><PERSON>(NAME) {
    override fun performE2E(requestId: String, loggingEnabledE2ECheck: Boolean): List<E2EComponentDTO> {
        val startedAt = Instant.now()
        lakefsConnector.ensureAlive()
        val processingTime = Instant.now().toEpochMilli() - startedAt.toEpochMilli()
        return listOf(E2EComponentDTO(formatE2ECheckName(), STATUS_OK, startedAt.toString(), processingTime))
    }

    override fun getVersion(): List<ServiceVersion> {
        val version = lakefsConnector.getVersion() ?: ServiceVersion.UNKNOWN_VERSION
        return listOf(
            ServiceVersion(
                name = NAME,
                version = version,
                status = ServiceVersion.STATUS_OK
            )
        )
    }

    companion object {
        const val NAME = "lakefs"
    }
}
