package com.justai.khub.rag

import com.justai.khub.BaseProjectIT
import com.justai.khub.api.fe.model.ApiKeyDTO
import com.justai.khub.api.fe.model.SourceDTO
import com.justai.khub.api.public.model.*
import com.justai.khub.common.util.JSON
import com.justai.khub.mock.MockRagServiceConnector
import com.justai.khub.project.dto.LLMProvider
import com.justai.khub.project.dto.SearchStrategy
import com.justai.khub.report.dto.ReportFilter
import com.justai.khub.report.enumeration.ReportPeriod
import com.justai.khub.util.TestDataFactory.testBalanceUpdate
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.test.context.TestPropertySource
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import java.time.LocalDate
import java.util.*

// WARN: Don't enable scheduling in multiple tests as it may cause flaky behavior due to multiple Spring contexts running concurrent jobs with same type
// TODO refactor to multiple classses \ spring contexts
@TestPropertySource(properties = ["scheduling.rag.enabled=true"])
class RagRelatedIT : BaseProjectIT() {
    private lateinit var projectApiKey: ApiKeyDTO
    private lateinit var projectSources: List<SourceDTO>

    @Autowired
    protected lateinit var mvc: MockMvc

    @Autowired
    private lateinit var ragServiceConnector: MockRagServiceConnector

    @BeforeEach
    fun init() {
        projectApiKey = apiKeyHelper.createApiKey(project.id)
        projectSources = listOf(
            projectFileHelper.uploadFile(projectId = project.id, fileName = "doc1.docx", fileContent = "тест1"),
            projectFileHelper.uploadFile(projectId = project.id, fileName = "doc2.docx", fileContent = "тест2")
        )
    }

    @Test
    fun testChatCreation() {
        val createdChat = chatPublicHelper.createChat(
            name = UUID.randomUUID().toString(),
            ragSettings = RagSettings(
                pipeline = PipelineType.SEMANTIC,
                search = SearchSettings(7, segment = "segment123")
            ),
            apiKey = projectApiKey.key
        )
        val chat = chatPublicHelper.getChat(chatId = createdChat.id, apiKey = projectApiKey.key)
        assertEquals(7, chat.settings.search!!.similarityTopK)
        assertEquals(PipelineType.SEMANTIC, chat.settings.pipeline)
    }

    @Test
    fun testChatQuery() {
        val createdChat = chatPublicHelper.createChat(
            name = UUID.randomUUID().toString(),
            ragSettings = RagSettings(
                pipeline = PipelineType.SEMANTIC,
                search = SearchSettings(
                    similarityTopK = 7,
                    segment = "segment123",
                    reranker = Reranker(
                        type = RerankerType.MODEL,
                        minScore = 6.0,
                        maxChunks = 7
                    ),
                    fullTextSearch = FullTextSearch(
                        strategy = FullTextSearchStrategy.WEIGHTED,
                        semanticPortion = 5,
                        ftsPortion = 2
                    )
                ),
                llm = LlmSettings(model = "GPT-4o", temperature = 0.34)
            ),
            apiKey = projectApiKey.key
        )

        val query = "long query ${UUID.randomUUID()}"
        val chatQueryResult = chatPublicHelper.makeRequestAndExpectOk(
            chatId = createdChat.id, query = query, apiKey = projectApiKey.key,
            ragSettings = RagSettings(
                pipeline = PipelineType.SEMANTIC,
                search = SearchSettings(
                    similarityTopK = 8,
                    reranker = Reranker(type = RerankerType.MODEL, minScore = 3.0),
                    fullTextSearch = FullTextSearch(strategy = FullTextSearchStrategy.HYBRID)
                ),
            )
        )
        assertEquals(chatQueryResult.status, QueryProcessingStatus.FINISHED)
        assertTrue(chatQueryResult.response!!.isNotEmpty())
        val lastQuery = ragServiceConnector.getQueryByMessage(query)
        assertNotNull(lastQuery, "Missing query to rag service")
        assertEquals(8, lastQuery!!.searchPipelineConfig.search?.similarityTopK)
        assertEquals(80, lastQuery.searchPipelineConfig.search?.numCandidates)
        assertTrue(lastQuery.searchPipelineConfig.search?.fullTextSearch?.ftSearch ?: false)
        assertEquals(SearchStrategy.hybrid, lastQuery.searchPipelineConfig.search?.fullTextSearch?.strategy)
        assertEquals(5, lastQuery.searchPipelineConfig.search?.fullTextSearch?.semanticPortion)
        assertEquals(2, lastQuery.searchPipelineConfig.search?.fullTextSearch?.ftsPortion)
        assertEquals(com.justai.khub.project.dto.RerankerType.model, lastQuery.searchPipelineConfig.reranker?.type)
        assertEquals(3, lastQuery.searchPipelineConfig.reranker?.minScore)
        assertTrue(lastQuery.searchPipelineConfig.search?.useAllEmbeddings ?: false)
        assertTrue(lastQuery.searchPipelineConfig.search?.transformGenerativeMessage ?: false)
        assertEquals(LLMProvider.openai, lastQuery.searchPipelineConfig.llm.llmProvider)
        assertEquals("openai/gpt-4o", lastQuery.searchPipelineConfig.llm.model)
        assertTrue(lastQuery.searchPipelineConfig.search?.useHistory ?: false)
    }

    @Test
    fun testChatQueryAsync() {
        val createdChat = chatPublicHelper.createChat(
            name = UUID.randomUUID().toString(),
            ragSettings = RagSettings(
                pipeline = PipelineType.SEMANTIC,
                search = SearchSettings(7, segment = "segment123")
            ),
            apiKey = projectApiKey.key
        )

        val query = "very long query ${UUID.randomUUID()}"
        var chatQuery = chatPublicHelper.makeRequestAsyncAndExpectOk(
            chatId = createdChat.id,
            query = query,
            apiKey = projectApiKey.key
        )
        assertEquals(QueryProcessingStatus.READY_TO_PROCESS, chatQuery.status)
        assertNull(chatQuery.response)

        chatQuery = chatPublicHelper.getChatQueryResult(
            chatId = chatQuery.chatId,
            queryId = chatQuery.id,
            waitTimeSeconds = 3,
            apiKey = projectApiKey.key
        )
        assertEquals(QueryProcessingStatus.PROCESSING, chatQuery.status)
        assertNull(chatQuery.response)

        chatQuery = chatPublicHelper.getChatQueryResult(
            chatId = chatQuery.chatId,
            queryId = chatQuery.id,
            waitTimeSeconds = 5,
            apiKey = projectApiKey.key
        )
        assertEquals(QueryProcessingStatus.FINISHED, chatQuery.status)
        assertTrue(chatQuery.response!!.isNotEmpty())
        val lastQuery = ragServiceConnector.getQueryByMessage(query)
        assertNotNull(lastQuery, "Missing query to rag service")
        assertEquals(7, lastQuery!!.searchPipelineConfig.search?.similarityTopK)
        assertEquals(70, lastQuery.searchPipelineConfig.search?.numCandidates)
        assertTrue(lastQuery.searchPipelineConfig.search?.useHistory ?: false)
    }

    @Test
    fun testChatQueryCancel() {
        val createdChat = chatPublicHelper.createChat(
            name = UUID.randomUUID().toString(),
            apiKey = projectApiKey.key
        )

        val chatQuery = chatPublicHelper.makeRequestAsyncAndExpectOk(
            chatId = createdChat.id,
            query = "very long query",
            apiKey = projectApiKey.key
        )
        assertEquals(chatQuery.status, QueryProcessingStatus.READY_TO_PROCESS)
        assertNull(chatQuery.response)

        val chatQueryResult = chatPublicHelper.cancelChatQuery(
            chatId = chatQuery.chatId,
            queryId = chatQuery.id,
            apiKey = projectApiKey.key
        )
        assertEquals(chatQueryResult.status, QueryProcessingStatus.CANCELED)
        assertNull(chatQueryResult.response)
    }

    @Test
    fun testGenerationQueryAnswer() {
        val query = "long query ${UUID.randomUUID()}"
        val queryAnswer = chatPublicHelper.generateQueryAnswerAndExpectOk(
            projectId = project.id,
            queryMessage = query,
            apiKey = projectApiKey.key
        )

        assertEquals(queryAnswer.status, QueryProcessingStatus.FINISHED)
        assertTrue(queryAnswer.response!!.isNotEmpty())
        val lastQuery = ragServiceConnector.getQueryByMessage(query)
        assertNotNull(lastQuery, "Missing query to rag service")
        assertEquals(5, lastQuery!!.searchPipelineConfig.search?.similarityTopK)
        assertEquals(50, lastQuery.searchPipelineConfig.search?.numCandidates)
        // we do not send any search settings - used default from project. However, query generate by own random conversation id
        assertTrue(lastQuery.searchPipelineConfig.search?.useHistory ?: false)
    }

    @Test
    fun testAsyncGenerationQueryAnswer() {
        val query = "very long query ${UUID.randomUUID()}"
        val queryAnswer = chatPublicHelper.asyncGenerateQueryAnswerAndExpectOk(
            projectId = project.id,
            ragSettings = RagSettings(
                pipeline = PipelineType.SEMANTIC, search = SearchSettings(
                    similarityTopK = 7,
                    candidateRadius = 2,
                    segment = "segment123"
                )
            ),
            queryMessage = query,
            apiKey = projectApiKey.key,
            history = listOf(
                HistoryRecord(content = "hi", role = ParticipantRole.USER),
                HistoryRecord(content = "bye", role = ParticipantRole.ASSISTANT)
            )
        )
        assertEquals(QueryProcessingStatus.READY_TO_PROCESS, queryAnswer.status)
        assertNull(queryAnswer.response)

        val answerAfterPartTime =
            chatPublicHelper.getQueryAnswer(queryId = queryAnswer.id, waitTimeSeconds = 2, apiKey = projectApiKey.key)
        assertEquals(QueryProcessingStatus.PROCESSING, answerAfterPartTime.status)
        assertNull(queryAnswer.response)

        val answerAfterFullTime =
            chatPublicHelper.getQueryAnswer(queryId = queryAnswer.id, waitTimeSeconds = 5, apiKey = projectApiKey.key)
        assertEquals(QueryProcessingStatus.FINISHED, answerAfterFullTime.status)
        assertTrue(answerAfterFullTime.response!!.isNotEmpty())

        val lastQuery = ragServiceConnector.getQueryByMessage(query)
        assertNotNull(lastQuery, "Missing query to rag service")
        assertEquals(7, lastQuery!!.searchPipelineConfig.search?.similarityTopK)
        assertEquals(70, lastQuery.searchPipelineConfig.search?.numCandidates)
        assertEquals(2, lastQuery.searchPipelineConfig.search?.candidateRadius)
        assertTrue(lastQuery.searchPipelineConfig.search?.useHistory ?: false)
        assertEquals("segment123", lastQuery.segment)
    }

    @Test
    fun testFailToGetQueryResultFromAnotherProject() {
        val anotherProject = projectHelper.createProject()
        val anotherApiKey = apiKeyHelper.createApiKey(anotherProject.id)
        billingHelper.updateAccountBalance(testBalanceUpdate(anotherProject.audit.createdBy))

        val queryAnswer = chatPublicHelper.asyncGenerateQueryAnswerAndExpectOk(
            projectId = anotherProject.id,
            queryMessage = "long query",
            apiKey = anotherApiKey.key,
        )

        chatPublicHelper.getQueryAnswer(queryId = queryAnswer.id, waitTimeSeconds = 2, apiKey = anotherApiKey.key)
        chatPublicHelper.getQueryNotFound(queryId = queryAnswer.id, waitTimeSeconds = 2, apiKey = projectApiKey.key)

        projectHelper.deleteProjectAndExpectOk(anotherProject.id)
        apiKeyHelper.revokeApiKeyAndExpectOk(anotherApiKey.id, anotherApiKey.projectId!!)
        projectHelper.deleteProjectRepository(anotherProject)
    }

    @Test
    fun testInvalidLLMInRequest() {
        val request = GenerateAnswerRequest(
            query = "long query",
            settings = RagSettings(
                pipeline = PipelineType.SEMANTIC,
                llm = LlmSettings(model = "deepseek")
            )
        )
        mvc.perform(
            post("/api/knowledge-hub/query")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
                .header("Authorization", "Bearer ${projectApiKey.key}")
        ).andExpect(MockMvcResultMatchers.status().isBadRequest)
    }

    @Test
    fun testReportGeneration() {
        val defaultChat = chatHelper.getChatsPage(project.id).content.first()

        val firstMessage = chatHelper.makeRequestAndExpectOk(project.id, defaultChat.id, "First message")
        val secondMessage = chatHelper.makeRequestAndExpectOk(project.id, defaultChat.id, "Second message")
        chatHelper.waitProcessing(project.id, firstMessage)
        chatHelper.waitProcessing(project.id, secondMessage)

        val dayReport = reportHelper.generateReport(ReportFilter(defaultAuth.userData.accountId!!, ReportPeriod.day))
        checkReportData(dayReport)

        val weekReport = reportHelper.generateReport(ReportFilter(defaultAuth.userData.accountId!!, ReportPeriod.week))
        checkReportData(weekReport)

        val monthReport =
            reportHelper.generateReport(ReportFilter(defaultAuth.userData.accountId!!, ReportPeriod.month))
        checkReportData(monthReport)

        val customPeriodReport =
            reportHelper.generateReport(
                ReportFilter(
                    defaultAuth.userData.accountId!!,
                    ReportPeriod.custom,
                    LocalDate.now().minusDays(2),
                    LocalDate.now().plusDays(2)
                )
            )
        checkReportData(customPeriodReport)

        val reportWithPeriodInFuture =
            reportHelper.generateReport(
                ReportFilter(
                    defaultAuth.userData.accountId!!,
                    ReportPeriod.custom,
                    LocalDate.now().plusDays(2),
                    LocalDate.now().plusDays(2)
                )
            )
        assertTrue(reportHelper.parseReport(reportWithPeriodInFuture).isEmpty())

        val reportForNonExistedAccount = reportHelper.generateReport(ReportFilter(-23, ReportPeriod.day))
        assertTrue(reportHelper.parseReport(reportForNonExistedAccount).isEmpty())

        chatHelper.clearChatHistory(project.id, defaultChat.id)
        val reportAfterClearHistory =
            reportHelper.generateReport(ReportFilter(defaultAuth.userData.accountId!!, ReportPeriod.day))
        checkReportData(reportAfterClearHistory)
    }

    @Test
    fun testGenerationQueryAnswerWithRelevantSources() {
        val query = "query with docs ${UUID.randomUUID()}"
        val queryAnswer = chatPublicHelper.generateQueryAnswerAndExpectOk(
            projectId = project.id,
            queryMessage = query,
            apiKey = projectApiKey.key
        )

        assertEquals(QueryProcessingStatus.FINISHED, queryAnswer.status)
        assertTrue(queryAnswer.response!!.isNotEmpty())
        val lastQuery = ragServiceConnector.getQueryByMessage(query)
        assertNotNull(lastQuery, "Missing query to rag service")
        assertFalse(queryAnswer.relevantSources.isNullOrEmpty())
        assertTrue(queryAnswer.relevantSources!!.mapNotNull { it.externalLink }.isNotEmpty())
    }

    @Test
    fun testRetrievingWithSources() {
        val query = "some text to retrieving ${UUID.randomUUID()}"
        val retrievingResult = chatPublicHelper.retrieveChunksAndExpectOk(
            query = query,
            apiKey = projectApiKey.key
        )

        assertTrue(retrievingResult.chunks.isNotEmpty())
        assertTrue(retrievingResult.chunks.map { it.source }.mapNotNull { it.externalLink }.isNotEmpty())
    }

    @Test
    fun testRetrievingByChatWithSources() {
        val query = "some text to retrieving ${UUID.randomUUID()}"
        val createdChat = chatPublicHelper.createChat(
            name = UUID.randomUUID().toString(),
            apiKey = projectApiKey.key
        )
        val retrievingResult = chatPublicHelper.retrieveChunksByChatAndExpectOk(
            chatId = createdChat.id,
            query = query,
            apiKey = projectApiKey.key
        )

        assertTrue(retrievingResult.chunks.isNotEmpty())
        assertTrue(retrievingResult.chunks.map { it.source }.mapNotNull { it.externalLink }.isNotEmpty())
    }

    @Test
    fun testRetrieveChunks() {
        val chat = chatHelper.getChatsPage(project.id).content.first()
        val record = chatHelper.makeRequestAndExpectOk(project.id, chat.id, "Test message")
        chatHelper.waitProcessing(project.id, record)
        val chunks = retrieveChunksHelper.retrieveChunks(project.id, chat.id, record.id)
        assertFalse(chunks.chunks.isEmpty())
    }

    private fun checkReportData(rawReport: ByteArray) {
        val report = reportHelper.parseReport(rawReport)
        assertEquals(1, report.size)
        assertEquals(LocalDate.now(), report.first().date)
        assertFalse(report.first().recordsCount == 0)
    }
}
