Прочитай историю диалога и определи, является ли запрос пользователя (USER_QUERY) продолжением диалога (HISTORY). Если да, то переформулируй текущий вопрос пользователя (USER_QUERY) с учетом истории (HISTORY), добавив ее в виде контекста; сделай вопрос более понятным, четким и структурированным и добавь к вопросу похожие запросы и заголовок, верни текст с заголовком.

----
Пример:

USER_QUERY:
"мне нкжно мои деньги снять с карты"

IMPROVED_QUERY:
"""
# Снятие наличных с карты

Как снять деньги с карты

Похожие запросы:
(1) снять деньги с карты
(2) обналичить деньги с карты
(3) перевести деньги с карты
"""

----
Если запрос не является продолжением диалога (HISTORY) или он не относится к диалогу и затрагивает новую тему, в качестве своего ответа (IMPROVED_QUERY) верни только оригинальный запрос.
Отвечай только на русском языке.
----

HISTORY:
{history_str}

USER_QUERY:
{query_str}

{context_str}

IMPROVED_QUERY:

