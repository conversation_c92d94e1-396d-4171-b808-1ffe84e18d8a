package com.justai.khub.util

import com.fasterxml.jackson.databind.node.ObjectNode
import com.justai.accountsadmin.entities.TestDto
import com.justai.khub.config.TestConfig
import com.justai.khub.dto.CheckAuthorizationResponse
import org.junit.Assert
import org.junit.jupiter.api.Assertions.assertTrue
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.HttpMethod.POST
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate

@Service
class TestAccountsService(
    private val testConfig: TestConfig
) {
    private val aaRestTemplate: RestTemplate = RestTemplateFactory.createRestTemplate(baseUrl = testConfig.aa.baseUrl)
    private val gatewayRestTemplate: RestTemplate = RestTemplateFactory.createRestTemplate(baseUrl = testConfig.gateway.baseUrl)

    fun loginWithNewAccount(): Auth {
        val accountCreateRequest = TestDataGenerator.testAccount()
        createTestAccount(accountCreateRequest)
        return login(accountCreateRequest.login, accountCreateRequest.password)
    }

    fun createTestAccount(dto: TestDto.UserWithAccount.Create): TestDto.UserWithAccount.Read {
        val request = HttpEntity(dto, testConfig.aa.asAuthHeaders())
        val userWithAccount = aaRestTemplate.exchange("/test/create-account", POST, request, TestDto.UserWithAccount.Read::class.java).body
        if (userWithAccount == null) {
            Assert.fail("Failed to create account")
        }
        return userWithAccount!!
    }

    fun login(login: String, password: String): Auth {
        val request = HttpEntity(JSON.objectNode().put("email", login).put("password", password), testConfig.aa.asAuthHeaders())
        val loginResponse = aaRestTemplate.postForEntity("/api/accountsadmin/public/authorization/login", request, ObjectNode::class.java)
        if (!loginResponse.statusCode.is2xxSuccessful) Assert.fail("Login failed. Status code: ${loginResponse.statusCode}")

        val cookies = loginResponse.headers[HttpHeaders.SET_COOKIE]?.map { it.split(";")[0] }?.toMutableList() ?: mutableListOf()
        val xXsrfToken = cookies.find { it.startsWith("XSRF-TOKEN") }?.split("=")?.get(1) ?: ""

        val checkAuthResponse = aaRestTemplate.exchange(
            "/api/accountsadmin/c/authorization/check-authorized",
            HttpMethod.GET,
            HttpEntity<String>(HttpHeaders().apply { put(HttpHeaders.COOKIE, cookies) }),
            CheckAuthorizationResponse::class.java
        )

        if (!checkAuthResponse.statusCode.is2xxSuccessful) Assert.fail("Check authorization failed. Status code: ${checkAuthResponse.statusCode}")

        return Auth(
            login = login,
            password = password,
            cookies = cookies,
            xXsrfToken = xXsrfToken,
            user = checkAuthResponse.body!!.userData
        )
    }

    fun changeTestAccountBalance(accountId: Long) {
        val dto = listOf(mapOf(
            "accountId" to accountId,
            "currentRequestsByTariff" to 1000,
            "tariffVersion" to 1,
            "totalRequestsByPackages" to 0,
            "operationsSuspended" to false,
        ))
        val request = HttpEntity(dto, testConfig.gateway.asAuthHeaders())
        val response = gatewayRestTemplate.exchange("/api/khub/internal/billing/cc-tariff-update", POST, request, ObjectNode::class.java)
        assertTrue(response.statusCode.is2xxSuccessful)
    }
}
