package com.justai.khub.common.configuration

import com.fasterxml.jackson.databind.ObjectMapper
import com.justai.khub.attachment.properties.AttachmentsProperties
import com.justai.khub.billing.properties.BillingProperties
import com.justai.khub.common.configuration.dto.*
import com.justai.khub.common.configuration.service.SpringSecurityAuditorAware
import com.justai.khub.common.entity.CCUserEntity
import com.justai.khub.common.util.JSON
import com.justai.khub.ratelimit.dto.RateLimitProperties
import org.hibernate.cfg.AvailableSettings
import org.hibernate.type.format.jackson.JacksonJsonFormatMapper
import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.data.auditing.DateTimeProvider
import org.springframework.data.domain.AuditorAware
import org.springframework.data.jpa.repository.config.EnableJpaAuditing
import org.springframework.data.jpa.repository.config.EnableJpaRepositories
import org.springframework.transaction.annotation.EnableTransactionManagement
import java.time.LocalDateTime
import java.util.*


@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(basePackages = ["com.justai.khub"])
@EnableJpaAuditing(auditorAwareRef = "auditorAware", dateTimeProviderRef = "dateTimeProvider")
@EnableConfigurationProperties(
    value = [
        IntegrationProperties::class,
        ApiKeyProperties::class,
        LoggingProperties::class,
        RagProperties::class,
        IngestProperties::class,
        QAProperties::class,
        LocalFeaturesConfiguration::class,
        BillingProperties::class,
        AttachmentsProperties::class,
        PromptProperties::class,
        RateLimitProperties::class,
        AnalyticsConfiguration::class
    ]
)
class MainConfiguration {

    @Bean
    fun auditorAware(): AuditorAware<CCUserEntity> {
        return SpringSecurityAuditorAware()
    }

    @Bean
    fun dateTimeProvider(): DateTimeProvider {
        return DateTimeProvider { Optional.of(LocalDateTime.now()) }
    }

    @Bean
    fun jsonFormatMapperCustomizer(objectMapper: ObjectMapper?): HibernatePropertiesCustomizer {
        return HibernatePropertiesCustomizer { properties: MutableMap<String?, Any?> ->
            properties[AvailableSettings.JSON_FORMAT_MAPPER] = JacksonJsonFormatMapper(JSON.mapper)
        }
    }
}


