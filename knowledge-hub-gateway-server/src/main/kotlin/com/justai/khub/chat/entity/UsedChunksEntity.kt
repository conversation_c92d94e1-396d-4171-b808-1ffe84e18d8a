package com.justai.khub.chat.entity

import com.justai.khub.common.dto.DocumentChunks
import com.justai.khub.qa.entity.TestRunEntity
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.*
import org.hibernate.annotations.Type

@Entity
@Table(name = "used_chunks")
class UsedChunksEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = -1

    @OneToOne
    @JoinColumn(name = "test_run_id", nullable = true)
    var testRun: TestRunEntity? = null

    @OneToOne
    @JoinColumn(name = "chat_history_record_id", nullable = true)
    var chatHistoryRecord: ChatHistoryRecordEntity? = null

    @Type(JsonType::class)
    @Column(columnDefinition = "jsonb", nullable = true)
    lateinit var chunks: DocumentChunks
}
