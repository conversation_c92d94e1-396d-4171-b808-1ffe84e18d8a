package com.justai.khub.common.util

import com.justai.khub.common.enumeration.MdcField
import org.slf4j.MDC
import java.util.*

object LoggingUtils {

    fun addToMDC(
        accountId: Long? = null,
        requestId: String? = null,
    ) {
        accountId?.let { MDC.put(MdcField.ACCOUNT_ID.key, it.toString()) }
        requestId?.let { MDC.put(MdcField.REQUEST_ID.key, it) }
    }

    fun clearField(field: MdcField) {
        MDC.remove(field.key)
    }

    fun clear(fields: Set<MdcField>) {
        fields.forEach { MDC.remove(it.key) }
    }

    fun clearAll(except: Set<MdcField> = setOf()) {
        if (MDC.getCopyOfContextMap() != null) {
            MDC.getCopyOfContextMap().forEach { entry ->
                if (!except.map { it.key }.contains(entry.key)) MDC.remove(entry.key)
            }
        }
    }

    fun getRequestId(): String {
        var requestId: String? = MDC.get(MdcField.REQUEST_ID.key)
        if (requestId == null) {
            requestId = UUID.randomUUID().toString()
            addToMDC(requestId = requestId)
        }
        return requestId
    }
}
