package com.justai.khub.project

import com.justai.khub.BaseProjectIT
import com.justai.khub.project.dto.ProjectFileMetadata
import com.justai.khub.project.enumeration.ProjectSourceStatus
import com.justai.khub.project.service.ProjectFileService
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class ProjectFileIT : BaseProjectIT() {

    @Autowired
    private lateinit var projectFileService: ProjectFileService

    @Test
    fun testFileSizeIsInitialized() {
        val file = projectFileHelper.uploadFile(projectId = project.id, fileName = "file-test1.txt")
        assertNotNull(file.sizeBytes)
    }

    @Test
    fun getFilesSummary() {
        val initialSummary = projectFileHelper.getFilesSummary(project.id)
        assertEquals(0, initialSummary.totalFiles)
        assertEquals(0, initialSummary.totalFilesSizeBytes)
        assertEquals(0, initialSummary.totalFilesSizeChars)

        val file1 = projectFileHelper.uploadFile(projectId = project.id, fileName = "file-test1.txt")
        val oneFileSummary = projectFileHelper.getFilesSummary(project.id)
        assertEquals(1, oneFileSummary.totalFiles)
        assertEquals(file1.sizeBytes, oneFileSummary.totalFilesSizeBytes)
        assertEquals(0, oneFileSummary.totalFilesSizeChars)

        val file2 = projectFileHelper.uploadFile(projectId = project.id, fileName = "file-test2.txt")
        val twoFilesSummary = projectFileHelper.getFilesSummary(project.id)
        assertEquals(2, twoFilesSummary.totalFiles)
        assertEquals(file1.sizeBytes!! + file2.sizeBytes!!, twoFilesSummary.totalFilesSizeBytes)
        assertEquals(0, twoFilesSummary.totalFilesSizeChars)
    }

    @Test
    fun testSorting() {
        projectFileHelper.uploadFile(projectId = project.id, fileName = "file-test-1.txt")
        projectFileHelper.uploadFile(projectId = project.id, fileName = "file-test-2.txt")
        for (sortOrder in listOf("relativePath", "createdAt", "status")) {
            val page = projectFileHelper.getFilesPage(project.id, mapOf("sort" to sortOrder))
            assertEquals(2, page.content.size)
        }
    }

    @Test
    fun testFilterByStatus() {
        val allFileStatuses = ProjectSourceStatus.entries
        for (fileStatus in allFileStatuses) {
            val file = projectFileHelper.uploadFile(projectId = project.id, fileName = "file-test-${fileStatus.ordinal}.txt")
            val updated = projectFileService.updateStatusAndErrorMessage(file.id, fileStatus)
            assertEquals(1, updated)
            val page = projectFileHelper.getFilesPage(project.id, mapOf("status" to fileStatus.name))
            assertEquals(1, page.content.size)
            assertEquals(fileStatus.name, page.content.first().status)
        }
        val filesWithDifferentStatuses = projectFileHelper
            .getFilesPage(project.id, mapOf("status" to listOf(ProjectSourceStatus.INGESTED, ProjectSourceStatus.READY_TO_INGEST).joinToString { it.name }))
        assertEquals(2, filesWithDifferentStatuses.content.size)
        val filesWithoutFilter = projectFileHelper.getFilesPage(project.id)
        assertEquals(ProjectSourceStatus.entries.size, filesWithoutFilter.content.size)
        val filesWithNameAndStatusFilter = projectFileHelper.getFilesPage(
            project.id,
            mapOf("status" to ProjectSourceStatus.READY_TO_INGEST.name, "name" to "test-${ProjectSourceStatus.READY_TO_INGEST.ordinal}.txt")
        )
        assertEquals(1, filesWithNameAndStatusFilter.content.size)
    }

    @Test
    fun testFilterByName() {
        val file1 = projectFileHelper.uploadFile(projectId = project.id, fileName = "file-test1.txt")
        projectFileHelper.uploadFile(projectId = project.id, fileName = "file-test2.txt")

        var filesPage = projectFileHelper.getFilesPage(project.id)
        assertEquals(2, filesPage.content.size)

        filesPage = projectFileHelper.getFilesPage(project.id, mapOf("name" to "test1"))
        assertEquals(1, filesPage.content.size)
        assertTrue(filesPage.content.any { it.id == file1.id })

        filesPage = projectFileHelper.getFilesPage(project.id, mapOf("name" to ".txt"))
        assertEquals(2, filesPage.content.size)

        filesPage = projectFileHelper.getFilesPage(project.id, mapOf("name" to "    "))
        assertEquals(2, filesPage.content.size)

        filesPage = projectFileHelper.getFilesPage(project.id, mapOf("name" to "_"))
        assertEquals(0, filesPage.content.size)

        filesPage = projectFileHelper.getFilesPage(project.id, mapOf("name" to "%"))
        assertEquals(0, filesPage.content.size)
    }

    @Test
    fun testFilterByNameWithUnderscore() {
        val file1 = projectFileHelper.uploadFile(projectId = project.id, fileName = "___test.txt")
        val filesPage = projectFileHelper.getFilesPage(project.id, mapOf("name" to "__"))
        assertEquals(1, filesPage.content.size)
        assertTrue(filesPage.content.any { it.id == file1.id })
    }

    @Test
    fun testCRUD() {
        val newFile = projectFileHelper.uploadFile(projectId = project.id, fileContent = "test")
        val filesPage = projectFileHelper.getFilesPage(project.id)
        assertTrue(filesPage.content.any { it.id == newFile.id })

        projectFileHelper.deleteFile(project.id, newFile.id)

        val updatedFilesPage = projectFileHelper.getFilesPage(project.id)
        assertTrue(updatedFilesPage.content.none { it.id == newFile.id })
    }

    @Test
    fun testAddWithSegment() {
        val newFile = projectFileHelper.uploadFile(projectId = project.id, fileContent = "test", metadata = ProjectFileMetadata(segment = "test-segment"))
        val addedEntity = projectFileService.getFile(newFile.id)
        assertEquals("test-segment", addedEntity.metadata.segment)
    }

    @Test
    fun testDownload() {
        val initialContent = "тест"
        val newFile = projectFileHelper.uploadFile(projectId = project.id, fileContent = initialContent)
        val downloadedContent = projectFileHelper.downloadFile(project.id, newFile.id)
        assertNotNull(downloadedContent)
    }
}
