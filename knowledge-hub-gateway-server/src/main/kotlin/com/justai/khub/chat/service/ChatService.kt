package com.justai.khub.chat.service

import com.justai.khub.chat.entity.ChatEntity
import com.justai.khub.chat.mapper.ChatMapper
import com.justai.khub.chat.repository.ChatHistoryRepository
import com.justai.khub.chat.repository.ChatRepository
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.project.dto.SearchPipelineConfig
import com.justai.khub.project.dto.UISettings
import com.justai.khub.project.entity.ProjectVersionEntity
import com.justai.khub.project.mapper.ProjectSettingsMapper
import com.justai.khub.project.service.ProjectVersionService
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.*

@Service
class ChatService(
    private val projectVersionService: ProjectVersionService,
    private val chatRepository: ChatRepository,
    private val chatHistoryRepository: ChatHistoryRepository,
    private val projectSettingsMapper: ProjectSettingsMapper,
    private val chatMapper: ChatMapper,
    @Value("\${spring.messages.default-locale}") private val defaultLocale: String
) {
    val pageableDefault = PageRequest.of(0, Integer.MAX_VALUE, Sort.by(Sort.Direction.ASC, "createdAt"))
    val locale = Locale(defaultLocale)

    @Transactional(readOnly = true)
    fun getChats(projectId: Long, versionName: String, accountId: Long, pageable: PageRequest): Page<ChatEntity> {
        val version = projectVersionService.getVersion(projectId, versionName, accountId)
        return chatRepository.findAllByVersionId(version.id, pageable)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun createChat(projectId: Long, versionName: String, chatName: String?, accountId: Long): ChatEntity {
        val version = projectVersionService.getVersion(projectId, versionName, accountId)
        return createChatNoChecks(version, chatName)
    }

    private fun createChatNoChecks(version: ProjectVersionEntity, chatName: String? = null, internal: Boolean = false): ChatEntity {
        val newChat = chatMapper.initChatEntity(version, chatName, internal)
        val savedChat = chatRepository.saveAndFlush(newChat)
        val initialChatRecord = chatMapper.initialChatRecord(savedChat, locale)
        chatHistoryRepository.saveAndFlush(initialChatRecord)
        return savedChat
    }

    @Transactional(readOnly = true)
    fun getChat(projectId: Long, chatId: Long, accountId: Long): ChatEntity {
        val chat = chatRepository.findByIdAndProjectId(chatId, projectId) ?: throw KhubException(ApiErrorCode.CHAT_NOT_FOUND)
        if (chat.createdBy.accountId != accountId) throw KhubException(ApiErrorCode.ACCESS_DENIED)
        return chat
    }

    @Transactional(rollbackFor = [Exception::class])
    fun createChatWithSearchSettings(
        projectId: Long,
        accountId: Long,
        versionName: String,
        settings: SearchPipelineConfig,
        chatName: String? = null
    ): ChatEntity {
        val chat = createChat(projectId, versionName, chatName, accountId)
        setChatSettings(projectId, chat.id, settings, accountId)
        return getChat(projectId, chat.id, accountId)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun getOrCreateDefaultChat(
        projectId: Long,
        versionName: String,
        accountId: Long,
        internal: Boolean = false
    ): ChatEntity {
        val version = projectVersionService.getVersion(projectId, versionName, accountId)
        return chatRepository.findFirstByVersionIdAndInternalOrderByCreatedAtDesc(version.id, internal) ?: createChatNoChecks(version, internal = internal)
    }

    @Transactional(readOnly = true)
    fun getDefaultChat(version: ProjectVersionEntity, internal: Boolean = false): ChatEntity? {
        return chatRepository.findFirstByVersionIdAndInternalOrderByCreatedAtDesc(version.id, internal)
    }

    @Transactional(readOnly = true)
    fun getChatSettings(projectId: Long, chatId: Long, accountId: Long): SearchPipelineConfig {
        val chat = getChat(projectId, chatId, accountId)
        return chat.searchSettings
    }

    @Transactional(rollbackFor = [Exception::class])
    fun getOrCreateDefaultChatSettings(projectId: Long, versionName: String, accountId: Long): SearchPipelineConfig {
        val chat = getOrCreateDefaultChat(projectId, versionName, accountId)
        return chat.searchSettings
    }

    @Transactional(rollbackFor = [Exception::class])
    fun setDefaultChatSettings(projectId: Long, versionName: String, settings: UISettings, accountId: Long): SearchPipelineConfig {
        val chat = getOrCreateDefaultChat(projectId, versionName, accountId)
        chat.searchSettings = projectSettingsMapper.mapUIRagSettingsToInternalSearchConfig(settings, chat.version.ingestSettings, chat.version.searchSettings)
        return chatRepository.saveAndFlush(chat).searchSettings
    }

    @Transactional(rollbackFor = [Exception::class])
    fun setChatSettings(projectId: Long, chatId: Long, settings: UISettings, accountId: Long): SearchPipelineConfig {
        val chat = getChat(projectId, chatId, accountId)
        chat.searchSettings = projectSettingsMapper.mapUIRagSettingsToInternalSearchConfig(settings, chat.version.ingestSettings, chat.version.searchSettings)
        return chatRepository.saveAndFlush(chat).searchSettings
    }

    @Transactional(rollbackFor = [Exception::class])
    fun setChatSettings(projectId: Long, chatId: Long, settings: SearchPipelineConfig, accountId: Long): SearchPipelineConfig {
        val chat = getChat(projectId, chatId, accountId)
        chat.searchSettings = settings
        return chatRepository.saveAndFlush(chat).searchSettings
    }

    @Transactional(rollbackFor = [Exception::class])
    fun setSettingsForAllVersionChats(projectId: Long, versionName: String, settings: SearchPipelineConfig, accountId: Long) {
        val chats = getChats(projectId, versionName, accountId, pageableDefault).content
        chats.forEach { it.searchSettings = settings }
        chatRepository.saveAllAndFlush(chats)
    }
}
