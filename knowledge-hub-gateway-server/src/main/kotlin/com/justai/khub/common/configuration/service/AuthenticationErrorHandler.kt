package com.justai.khub.common.configuration.service

import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.security.core.AuthenticationException
import org.springframework.security.web.AuthenticationEntryPoint
import org.springframework.stereotype.Component

@Component
class AuthenticationErrorHandler : AuthenticationEntryPoint {

    override fun commence(request: HttpServletRequest, response: HttpServletResponse, authException: AuthenticationException) {
        ApiErrorHandler.writeErrorToResponse(
            throwable = authException,
            response = response,
            requestUrl = request.requestURI,
            method = request.method
        )
    }

}
