package com.justai.khub.project.dto

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
enum class ChunkerType {
    sentence,
    llm
}

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
enum class Language {
    english,
    russian
}

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class ChunkerConfig(
    val type: ChunkerType = ChunkerType.sentence,
    val chunkSize: Int? = 1024,
    val tokensSoftLimit: Int? = 1000,
    val tokensHardLimit: Int? = 2000,
    val maxLlmChunkerTokens: Int? = 70000,
    val maxLlmRetries: Int? = 2,
    val largeTableLines: Int? = 10000,
    val useForLargeTables: Boolean? = true,
    val language: Language? = Language.russian,
    val model: String? = null,
    val frequencyPenalty: Double? = 0.0,
    val maxTokens: Int? = 2000,
    val presencePenalty: Double? = 0.0,
    val temperature: Double? = 0.0,
    val topP: Double? = 1.0,
    val contextWindow: Int? = 128000
)
