<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.justai</groupId>
    <artifactId>knowledge-hub-gateway</artifactId>
    <packaging>pom</packaging>
    <version>2025.2-rc-SNAPSHOT</version>

    <modules>
        <module>knowledge-hub-gateway-server</module>
        <module>knowledge-hub-gateway-client</module>
        <module>knowledge-hub-gateway-bbt</module>
    </modules>

    <properties>
        <java.version>17</java.version>
        <kotlin.version>2.0.0</kotlin.version>
        <kotlin.apiVersion>1.9</kotlin.apiVersion>
        <kotlin.languageVersion>1.9</kotlin.languageVersion>
        <jackson.version>2.17.2</jackson.version>

        <openapi-generator-maven.version>7.7.0-JustAI-SNAPSHOT</openapi-generator-maven.version>
        <spring-boot.minor>3.2</spring-boot.minor>
        <spring.boot.version>${spring-boot.minor}.7</spring.boot.version>

        <skipITs>true</skipITs>
        <skipBBTs>true</skipBBTs>
        <useRedHatBaseImage>false</useRedHatBaseImage>

        <maven.deploy.skip>true</maven.deploy.skip>
        <maven.install.skip>true</maven.install.skip>
        <sonar.moduleKey>${project.artifactId}</sonar.moduleKey>
        <platform-bom.version>2025.1-rc-SNAPSHOT</platform-bom.version>
    </properties>

    <repositories>
        <repository>
            <id>nexus-public</id>
            <url>https://nexus.just-ai.com/repository/maven-public/</url>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>nexus-public</id>
            <url>https://nexus.just-ai.com/repository/maven-public/</url>
        </pluginRepository>
    </pluginRepositories>
    <distributionManagement>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <url>https://nexus.just-ai.com/repository/maven-snapshots/</url>
        </snapshotRepository>
        <repository>
            <id>nexus-releases</id>
            <url>https://nexus.just-ai.com/repository/maven-releases/</url>
        </repository>
    </distributionManagement>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-bom</artifactId>
                <version>${kotlin.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <!-- Import dependency management from Spring Boot -->
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.justai</groupId>
                <artifactId>platform-bom</artifactId>
                <version>${platform-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna</artifactId>
                <version>5.7.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>license-maven-plugin</artifactId>
                <version>2.0.1-JustAI-SNAPSHOT</version>
                <configuration>
                    <includedLicenses>https://gitlab.just-ai.com/jaicp/license-list/-/raw/master/licenses.html</includedLicenses>
                    <licenseMerges>
                        <licenseMerge>CDDL|CDDL 1.1|CDDL License|Common Development and Distribution License</licenseMerge>
                        <licenseMerge>GNU GPL|GNU General Public License Version 2|GNU General Public License Version 3</licenseMerge>
                        <licenseMerge>EPL|EPL 2.0|Eclipse Public License - v 1.0|Eclipse Public License v1.0</licenseMerge>
                        <licenseMerge>MPL|MPL 1.1|Mozilla Public License</licenseMerge>
                    </licenseMerges>
                    <excludedScopes>test,provided</excludedScopes>
                    <excludedGroups>com.justai|org.antlr|com.github.luben</excludedGroups>
                    <failOnMissing>true</failOnMissing>
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>
