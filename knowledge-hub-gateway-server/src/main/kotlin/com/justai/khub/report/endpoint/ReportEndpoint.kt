package com.justai.khub.report.endpoint

import com.justai.khub.api.fe.ReportApiService
import com.justai.khub.report.dto.ReportFilter
import com.justai.khub.report.enumeration.ReportPeriod
import com.justai.khub.report.service.ReportService
import org.springframework.core.io.ByteArrayResource
import org.springframework.core.io.Resource
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.time.LocalDate

@Service
class ReportEndpoint(
    private val reportService: ReportService
) : ReportApiService {

    override fun createAccountReportResponseEntity(
        accountId: Long,
        period: String,
        dateFrom: LocalDate?,
        dateTo: LocalDate?
    ): ResponseEntity<Resource> {
        val filter = ReportFilter(
            accountId = accountId,
            period = ReportPeriod.valueOf(period),
            dateFrom = dateFrom,
            dateTo = dateTo
        )
        val reportContent = reportService.generateReport(filter)
        val reportName = "knowledge-khub-report.csv"
        return ResponseEntity.ok()
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"${URLEncoder.encode(reportName, StandardCharsets.UTF_8.toString())}\"")
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .contentLength(reportContent.size.toLong())
            .body(ByteArrayResource(reportContent))
    }
}
