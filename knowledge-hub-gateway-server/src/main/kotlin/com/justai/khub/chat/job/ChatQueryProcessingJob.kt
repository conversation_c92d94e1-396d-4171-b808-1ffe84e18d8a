package com.justai.khub.chat.job

import com.justai.khub.chat.entity.ChatHistoryRecordEntity
import com.justai.khub.chat.service.ChatHistoryService
import com.justai.khub.chat.service.ChatQueryProcessor
import com.justai.khub.common.configuration.dto.KHubUser
import com.justai.khub.common.configuration.dto.RagProperties
import com.justai.khub.common.util.LoggingUtils
import com.justai.shutdown.CallbackAwaiter
import com.justai.shutdown.GracefulShutDownAware
import com.justai.shutdown.ShutDownLatch
import jakarta.annotation.PostConstruct
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Component
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit

@ConditionalOnProperty(value = ["scheduling.rag.enabled"], havingValue = "true", matchIfMissing = true)
@Component
class ChatQueryProcessingJob(
    private val chatHistoryService: ChatHistoryService,
    private val chatQueryProcessor: ChatQueryProcessor,
    private val chatQueryProcessingThreadPool: ThreadPoolExecutor,
    private val chatQueryProcessingExecutor: ExecutorService,
    private val ragProperties: RagProperties
) : GracefulShutDownAware {
    private val log = LoggerFactory.getLogger(this::class.java)

    private val shutDownLatch = ShutDownLatch()

    @PostConstruct
    fun init() {
        releaseActiveRecords()
    }

    fun releaseActiveRecords() {
        val releasedRecords = chatHistoryService.releaseLockedActiveRecords()
        if (releasedRecords > 0) {
            log.warn("Released {} active records", releasedRecords)
        }
    }

    @Scheduled(fixedDelay = 1000)
    fun processNewChatQueries() {
        shutDownLatch.run {
            val availableThreads = ragProperties.chatQueryProcessingJobThreadPoolSize - chatQueryProcessingThreadPool.activeCount
            if (availableThreads <= 0) {
                return@run
            }
            val newQueries = chatHistoryService.getNewChatRecords(availableThreads)
            newQueries.forEach { chatQueryProcessingExecutor.submit { processNewChatQuery(it) } }
        }
    }

    // WARN! Updates MDC and SecurityContext for background thread execution
    private fun processNewChatQuery(chatQuery: ChatHistoryRecordEntity) {
        try {
            val ownerId = chatQuery.chat.createdBy.accountId
            SecurityContextHolder.getContext().authentication = KHubUser(accountId = ownerId)
            LoggingUtils.addToMDC(
                accountId = ownerId,
                requestId = UUID.randomUUID().toString()
            )
            chatQueryProcessor.process(chatQuery)
        } finally {
            SecurityContextHolder.clearContext()
            LoggingUtils.clearAll()
        }
    }

    override fun shutDownGracefully(runnable: Runnable) {
        CallbackAwaiter.withCallback(runnable).awaitAsync {
            log.info("Stop ChatQueryProcessingJob...")
            chatQueryProcessingExecutor.shutdownNow()
            shutDownLatch.awaitShutdown()
            if (!chatQueryProcessingExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                log.warn("release active records")
                releaseActiveRecords()
            }
            log.info("Stop ChatQueryProcessingJob... DONE")
        }
    }
}
