package com.justai.khub.util

import com.justai.khub.api.public.model.*
import com.justai.khub.common.util.JSON
import com.justai.khub.util.WebUtils.parseTo
import com.justai.security.session.auth.JustSessionAuthentication
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.stereotype.Component
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.ResultActions
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers

@Component
class ChatPublicHelper {

    @Autowired
    protected lateinit var mvc: MockMvc

    @Autowired
    protected lateinit var defaultAuth: JustSessionAuthentication

    fun generateQueryAnswerAndExpectOk(
        projectId: Long,
        queryMessage: String,
        ragSettings: RagSettings? = null,
        history: List<HistoryRecord>? = null,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): QueryProcessingResult {
        return generateQueryAnswer(projectId, queryMessage, ragSettings, history, ccAuth, apiKey)
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn().parseTo<QueryProcessingResult>()
    }

    fun generateQueryAnswer(
        projectId: Long,
        queryMessage: String,
        ragSettings: RagSettings? = null,
        history: List<HistoryRecord>? = null,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ResultActions {
        val request = GenerateAnswerRequest(query = queryMessage, settings = ragSettings, history = history)
        return mvc.perform(
            post("/api/knowledge-hub/query")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
                .header("Authorization", "Bearer $apiKey")
        )
    }

    fun asyncGenerateQueryAnswerAndExpectOk(
        projectId: Long,
        queryMessage: String,
        ragSettings: RagSettings? = null,
        history: List<HistoryRecord>? = null,
        apiKey: String? = ""
    ): QueryProcessingResult {
        return asyncGenerateQueryAnswer(projectId, queryMessage, ragSettings, history, apiKey)
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn().parseTo<QueryProcessingResult>()
    }

    fun asyncGenerateQueryAnswer(
        projectId: Long,
        queryMessage: String,
        ragSettings: RagSettings? = null,
        history: List<HistoryRecord>? = null,
        apiKey: String? = ""
    ): ResultActions {
        val request = GenerateAnswerRequest(query = queryMessage, settings = ragSettings, history = history)
        return mvc.perform(
            post("/api/knowledge-hub/async/query")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
                .header("Authorization", "Bearer $apiKey")
        )
    }

    fun getQueryAnswer(
        queryId: Long,
        waitTimeSeconds: Long = 2,
        apiKey: String? = ""
    ): QueryProcessingResult {
        return mvc.perform(
            get("/api/knowledge-hub/query/$queryId")
                .contentType(MediaType.APPLICATION_JSON)
                .param("waitTimeSeconds", waitTimeSeconds.toString())
                .header("Authorization", "Bearer $apiKey")
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn().parseTo<QueryProcessingResult>()
    }

    fun getQueryNotFound(
        queryId: Long,
        waitTimeSeconds: Long = 2,
        apiKey: String? = ""
    ) {
        mvc.perform(
            get("/api/knowledge-hub/query/$queryId")
                .contentType(MediaType.APPLICATION_JSON)
                .param("waitTimeSeconds", waitTimeSeconds.toString())
                .header("Authorization", "Bearer $apiKey")
        )
            .andExpect(MockMvcResultMatchers.status().isNotFound)
    }

    fun createChat(
        name: String? = null,
        ragSettings: RagSettings? = null,
        apiKey: String? = ""
    ): Chat {
        val request = CreateChatRequest(name = name, settings = ragSettings)
        return mvc.perform(
            post("/api/knowledge-hub/chat")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
                .header("Authorization", "Bearer $apiKey")
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn().parseTo<Chat>()
    }

    fun getChat(chatId: Long, apiKey: String? = ""): Chat {
        return mvc.perform(
            get("/api/knowledge-hub/chat/$chatId")
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization", "Bearer $apiKey")
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn().parseTo<Chat>()
    }

    fun makeRequestAndExpectOk(
        chatId: Long,
        query: String,
        ragSettings: RagSettings? = null,
        apiKey: String? = ""
    ): ChatQueryProcessingResult {
        return makeRequest(chatId, query, ragSettings, apiKey)
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn().parseTo<ChatQueryProcessingResult>()
    }

    fun makeRequest(
        chatId: Long,
        query: String,
        ragSettings: RagSettings? = null,
        apiKey: String? = ""
    ): ResultActions {
        val request = ChatRequest(query = query, settings = ragSettings)
        return mvc.perform(
            post("/api/knowledge-hub/chat/$chatId/query")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
                .header("Authorization", "Bearer $apiKey")
        )
    }

    fun makeRequestAsyncAndExpectOk(
        chatId: Long,
        query: String,
        ragSettings: RagSettings? = null,
        apiKey: String? = ""
    ): ChatQueryProcessingResult {
        return makeRequestAsync(chatId, query, ragSettings, apiKey)
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn().parseTo<ChatQueryProcessingResult>()
    }

    fun makeRequestAsync(
        chatId: Long,
        query: String,
        ragSettings: RagSettings? = null,
        apiKey: String? = ""
    ): ResultActions {
        val request = ChatRequest(query = query, settings = ragSettings)
        return mvc.perform(
            post("/api/knowledge-hub/async/chat/$chatId/query")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
                .header("Authorization", "Bearer $apiKey")
        )
    }

    fun cancelChatQuery(
        chatId: Long,
        queryId: Long,
        apiKey: String? = ""
    ): ChatQueryProcessingResult {
        return mvc.perform(
            post("/api/knowledge-hub/chat/$chatId/query/$queryId/cancel")
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization", "Bearer $apiKey")
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn().parseTo<ChatQueryProcessingResult>()
    }

    fun getChatQueryResult(
        chatId: Long,
        queryId: Long,
        waitTimeSeconds: Long = 2,
        apiKey: String? = ""
    ): ChatQueryProcessingResult {
        return mvc.perform(
            get("/api/knowledge-hub/chat/$chatId/query/$queryId")
                .contentType(MediaType.APPLICATION_JSON)
                .param("waitTimeSeconds", waitTimeSeconds.toString())
                .header("Authorization", "Bearer $apiKey")
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn().parseTo<ChatQueryProcessingResult>()
    }

    fun retrieveByChat(
        chatId: Long,
        query: String,
        settings: RetrievingSettings? = null,
        apiKey: String? = ""
    ): ResultActions {
        val request = RetrieveChunksFromChatRequest(query = query, settings = settings)
        return mvc.perform(
            post("/api/knowledge-hub/chat/$chatId/retrieve")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
                .header("Authorization", "Bearer $apiKey")
        )
    }

    fun retrieveByQuery(
        query: String,
        settings: RetrievingSettings? = null,
        apiKey: String? = ""
    ): ResultActions {
        val request = RetrieveChunksRequest(query = query, settings = settings)
        return mvc.perform(
            post("/api/knowledge-hub/retrieve")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
                .header("Authorization", "Bearer $apiKey")
        )
    }

    fun retrieveChunksAndExpectOk(
        query: String,
        settings: RetrievingSettings? = null,
        apiKey: String? = ""
    ): RetrievedChunks {
        return retrieveByQuery(query, settings, apiKey).andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn().parseTo<RetrievedChunks>()
    }

    fun retrieveChunksByChatAndExpectOk(
        chatId: Long,
        query: String,
        settings: RetrievingSettings? = null,
        apiKey: String? = ""
    ): RetrievedChunks {
        return retrieveByChat(chatId, query, settings, apiKey).andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn().parseTo<RetrievedChunks>()
    }
}
