package com.justai.khub.channel.repository

import com.justai.khub.channel.entity.ChannelEntity
import com.justai.khub.channel.enumeration.ChannelStatus
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.EntityGraph
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface ChannelRepository : JpaRepository<ChannelEntity, Long> {
    @EntityGraph(attributePaths = ["version"])
    @Query("SELECT c FROM ChannelEntity c WHERE c.version.id = :versionId AND c.status IN :statuses")
    fun findAllByVersionIdAndStatusIn(versionId: Long, statuses: List<ChannelStatus>, pageable: Pageable): Page<ChannelEntity>

    @Query("SELECT c FROM ChannelEntity c WHERE c.id = :id AND c.version.id = :versionId")
    fun findByIdAndVersionId(id: Long, versionId: Long): ChannelEntity?
}
