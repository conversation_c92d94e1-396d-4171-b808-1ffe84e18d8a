package com.justai.khub.qa.service

import org.apache.poi.ss.usermodel.*
import org.apache.poi.xssf.usermodel.XSSFColor
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import java.awt.Color

abstract class ReportGenerator {
    val HEADER_BG = XSSFColor(Color(224, 224, 224), null)
    val COMMON_CELL_BG = XSSFColor(Color(255, 255, 255), null)
    val ERROR_BG = XSSFColor(Color(255, 225, 225), null)
    val WARN_BG = XSSFColor(Color(255, 245, 225), null)
    val FILL_PATTERN = FillPatternType.LEAST_DOTS

    protected fun createHeaderStyle(workbook: XSSFWorkbook) = workbook.createCellStyle().apply {
        alignment = HorizontalAlignment.CENTER
        setFont(workbook.createFont().apply {
            bold = true
            fontHeightInPoints = 12
            fontName = "Arial"
        })
        setFillPattern(FILL_PATTERN)
        setFillBackgroundColor(HEADER_BG)
        wrapText = true
    }

    protected fun setErrorBg(workbook: XSSFWorkbook, row: Row) {
        setBg(workbook, row, ERROR_BG)
    }
    protected fun setWarnBg(workbook: XSSFWorkbook, row: Row) {
        setBg(workbook, row, WARN_BG)
    }

    protected fun setBg(workbook: XSSFWorkbook, row: Row, color: XSSFColor) {
        row.cellIterator().forEach { cell ->
            val cellStyle = cell.cellStyle ?: workbook.createCellStyle()
            cellStyle.fillPattern = FILL_PATTERN
            cellStyle.setFillBackgroundColor(color)
            cell.cellStyle = cellStyle
        }
    }

    protected fun createDataStyle(workbook: XSSFWorkbook) = workbook.createCellStyle().apply {
        alignment = HorizontalAlignment.CENTER
        setFont(workbook.createFont().apply {
            fontHeightInPoints = 12
            fontName = "Arial"
        })
        setFillPattern(FILL_PATTERN)
        setFillBackgroundColor(COMMON_CELL_BG)
        wrapText = true
    }

    protected fun addBordersToAllCells(workbook: XSSFWorkbook, sheet: Sheet) {
        val borderStyle = BorderStyle.THIN
        sheet.forEach { row ->
            row.forEach { cell ->
                val cellStyle = cell.cellStyle ?: workbook.createCellStyle()
                cellStyle.borderTop = borderStyle
                cellStyle.borderBottom = borderStyle
                cellStyle.borderLeft = borderStyle
                cellStyle.borderRight = borderStyle
                cell.cellStyle = cellStyle
            }
        }
    }
}
