openapi: 3.0.1
info:
  title: Tovie Data Agent public API
  version: 1.0.0
servers:
  - url: https://data-agent.tovie.ai
    description: Production server
tags:
  - name: ProjectsPublic
    description: Knowledge base project operations.
  - name: QueriesPublic
    description: Independent requests for chunk retrieval or response generation. The message history can be optionally passed within the request.
  - name: ChatsPublic
    description: Requests for chunk retrieval and response generation within a user chat.
  - name: SourcesPublic
    description: Sources operations.
  - name: IntegrationsPublic
    description: Integrations operations.
paths:
  # *******************************************************************************
  #                           Project Service
  # *******************************************************************************
  /api/knowledge-hub/info:
    get:
      tags:
        - ProjectsPublic
      operationId: getProjectInfo
      summary: 'Get knowledge base project info'
      description: 'Get information on the current knowledge base project.'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectInfo'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
  /api/knowledge-hub/sources:
    get:
      tags:
        - SourcesPublic
      operationId: getProjectSources
      summary: 'Get list of sources'
      description: 'Get the list of knowledge base sources.'
      parameters:
        - $ref: '#/components/parameters/createDateFrom'
        - $ref: '#/components/parameters/createDateTo'
        - $ref: '#/components/parameters/sourceStatus'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectSources'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
  /api/knowledge-hub/sources/links:
    post:
      tags:
        - SourcesPublic
      operationId: addLink
      summary: 'Add via link'
      description: 'Upload a file via link and add it as a source to the knowledge base. Indexing begins automatically after the source is added.'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddLinkRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceDTO'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
    put:
      tags:
        - SourcesPublic
      operationId: updateLink
      summary: 'Update via link'
      description: 'Upload a file via link and replace the source with the specified name. Indexing begins automatically after the source is updated.'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateLinkRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceDTO'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
  /api/knowledge-hub/sources/texts:
    post:
      tags:
        - SourcesPublic
      operationId: addText
      summary: 'Add from text'
      description: 'Add the provided text as a source to the knowledge base. Indexing begins automatically after the source is added.'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddTextRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceDTO'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
    put:
      tags:
        - SourcesPublic
      operationId: updateText
      summary: 'Update with text'
      description: 'Replace the source with the text provided. Indexing begins automatically after the source is updated.'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTextRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceDTO'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
  /api/knowledge-hub/sources/files:
    post:
      tags:
        - SourcesPublic
      operationId: addFile
      summary: 'Add from file'
      description: 'Add the provided file as a source to the knowledge base. Indexing begins automatically after the source is added.'
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              required:
                - file
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: 'File to upload. See the [file requirements](https://data-agent.tovie.ai/docs/upload#file-requirements).'
                name:
                  type: string
                  description: Name of the source. It must be unique within the project. If not specified, it is automatically generated.
                segment:
                  type: string
                  description: Tag (knowledge base segment). If specified, the source will be available when searching by this tag.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceDTO'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
    put:
      tags:
        - SourcesPublic
      operationId: updateFile
      summary: 'Update with file'
      description: 'Replace the source with the file provided. Indexing begins automatically after the source is updated.'
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              required:
                - file
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: 'File to upload. See the [file requirements](https://data-agent.tovie.ai/docs/upload#file-requirements).'
                name:
                  type: string
                  description: Name of the source. If no source with this name exists, an error is returned.
                segment:
                  type: string
                  description: Tag (knowledge base segment). If specified, the source will be available when searching by this tag.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceDTO'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
  "/api/knowledge-hub/sources/{sourceId}":
    get:
      tags:
        - SourcesPublic
      operationId: getProjectSource
      summary: 'Get source info'
      description: 'Get information about the knowledge base source.'
      parameters:
        - $ref: '#/components/parameters/sourceIdRequired'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceDTO'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
    delete:
      tags:
        - SourcesPublic
      operationId: deleteProjectSource
      summary: 'Delete source'
      description: 'Delete the source from the knowledge base.'
      parameters:
        - $ref: '#/components/parameters/sourceIdRequired'
      responses:
        '200':
          description: OK
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
  "/api/knowledge-hub/sources/{sourceId}/download":
    get:
      tags:
        - SourcesPublic
      operationId: downloadProjectSource
      summary: 'Export source'
      description: 'Export the source file from the knowledge base.'
      parameters:
        - $ref: '#/components/parameters/sourceIdRequired'
      responses:
        '200':
          description: OK
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
          headers:
            Content-Disposition:
              schema:
                type: string
              example: attachment; filename=“example.pdf”
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
  /api/knowledge-hub/integrations:
    post:
      tags:
        - IntegrationsPublic
      operationId: createIntegration
      summary: 'Create integration'
      description: 'Add an integration to the knowledge base. Indexing begins automatically after data is downloaded, both when creating the integration or during automatic synchronisation, if enabled.'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IntegrationCreateRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntegrationDTO'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
    get:
      tags:
        - IntegrationsPublic
      operationId: getIntegrations
      summary: 'Get list of integrations'
      description: 'Get the list of knowledge base integrations.'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectIntegrations'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
  "/api/knowledge-hub/integrations/{integrationId}":
    get:
      tags:
        - IntegrationsPublic
      operationId: getIntegration
      summary: 'Get integration info'
      description: 'Get information about the integration.'
      parameters:
        - $ref: '#/components/parameters/integrationIdRequired'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntegrationDTO'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
    delete:
      tags:
        - IntegrationsPublic
      operationId: deleteIntegration
      summary: 'Delete integration'
      description: 'Remove the integration from the knowledge base.'
      parameters:
        - $ref: '#/components/parameters/integrationIdRequired'
      responses:
        '200':
          description: OK
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
  # *******************************************************************************
  #                           Retrieving Service
  # *******************************************************************************
  /api/knowledge-hub/retrieve:
    post:
      tags:
        - QueriesPublic
      operationId: retrieveChunks
      summary: 'Retrieve chunks'
      description: |
        Retrieve chunks relevant to the user’s query from the knowledge base.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RetrieveChunksRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RetrievedChunks'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
  # *******************************************************************************
  #                           Query service
  # *******************************************************************************
  /api/knowledge-hub/query:
    post:
      tags:
        - QueriesPublic
      operationId: generateQueryAnswer
      summary: 'Generate response'
      description: |
        Synchronous request to generate a response to the user’s query.

        Please note that request processing may take a significant amount of time.
        Ensure that the connection timeout set in your HTTP client is more than 1 minute.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerateAnswerRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QueryProcessingResult'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
  /api/knowledge-hub/async/query:
    post:
      tags:
        - QueriesPublic
      operationId: generateQueryAnswerAsync
      summary: 'Generate response (asynchronous request)'
      description: |
        Asynchronous request to generate a response to the user’s query.

        The result can be obtained via [GET /api/knowledge-hub/query/{queryId}](#operation/getQueryAnswer) endpoint, where `queryId` is the request identifier received in the current response.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerateAnswerRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QueryProcessingResult'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
  "/api/knowledge-hub/query/{queryId}":
    get:
      tags:
        - QueriesPublic
      operationId: getQueryAnswer
      summary: 'Processing status of the response generation request'
      description: |
        Get the current processing status of the response generation request.
        Long polling is used if the `waitTimeSeconds` parameter is specified.
      parameters:
        - $ref: '#/components/parameters/queryIdRequired'
        - $ref: '#/components/parameters/waitTimeSecondsOptional'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QueryProcessingResult'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          description: The current project or the requested chat entry not found or deleted
        '500':
          $ref: '#/components/responses/ServerError'
  "/api/knowledge-hub/query/{queryId}/cancel":
    post:
      tags:
        - QueriesPublic
      operationId: cancelQueryProcessing
      summary: 'Cancel response generation request'
      parameters:
        - $ref: '#/components/parameters/queryIdRequired'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QueryProcessingResult'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          description: The current project or the requested chat entry not found or deleted
        '500':
          $ref: '#/components/responses/ServerError'
  # *******************************************************************************
  #                           Chat service
  # *******************************************************************************
  /api/knowledge-hub/chat:
    post:
      tags:
        - ChatsPublic
      operationId: createChat
      summary: 'Create chat'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateChatRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Chat'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
  "/api/knowledge-hub/chat/{chatId}":
    get:
      tags:
        - ChatsPublic
      operationId: getChat
      summary: 'Get chat info'
      parameters:
        - $ref: '#/components/parameters/chatIdRequired'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Chat'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          description: The current project or chat not found or deleted
        '500':
          $ref: '#/components/responses/ServerError'
  "/api/knowledge-hub/chat/{chatId}/retrieve":
    post:
      tags:
        - ChatsPublic
      operationId: retrieveChunksFromChat
      summary: 'Retrieve chunks'
      description: |
        Retrieve chunks from the knowledge base that are relevant to the user’s query within the chat.
      parameters:
        - $ref: '#/components/parameters/chatIdRequired'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RetrieveChunksFromChatRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RetrievedChunks'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          description: The current project or chat not found or deleted
        '500':
          $ref: '#/components/responses/ServerError'
  "/api/knowledge-hub/chat/{chatId}/query":
    post:
      tags:
        - ChatsPublic
      operationId: processChatQuery
      summary: 'Generate response'
      description: |
        Synchronous request to generate a response to the user’s query. The chat message history is taken into account.

        Please note that request processing may take a significant amount of time. Ensure that the connection timeout set in your HTTP client is more than 1 minute.
      parameters:
        - $ref: '#/components/parameters/chatIdRequired'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatQueryProcessingResult'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          description: The current project or chat not found or deleted
        '500':
          $ref: '#/components/responses/ServerError'
  "/api/knowledge-hub/async/chat/{chatId}/query":
    post:
      tags:
        - ChatsPublic
      operationId: processChatQueryAsync
      summary: 'Generate response (asynchronous request)'
      description: |
        Asynchronous request to generate a response to the user’s query. The chat message history is taken into account.

        The result can be obtained via [GET /api/knowledge-hub/chat/{chatId}/query/{queryId}](#operation/getChatQueryAnswer) endpoint, where `queryId` is the request identifier received in the current response.
      parameters:
        - $ref: '#/components/parameters/chatIdRequired'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatQueryProcessingResult'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          description: The current project or chat not found or deleted
        '500':
          $ref: '#/components/responses/ServerError'
  "/api/knowledge-hub/chat/{chatId}/query/{queryId}":
    get:
      tags:
        - ChatsPublic
      operationId: getChatQueryAnswer
      summary: 'Processing status of the response generation request'
      description: |
        Get the current processing status of the response generation request within a user chat.
        Long polling is used if the `waitTimeSeconds` parameter is specified.
      parameters:
        - $ref: '#/components/parameters/chatIdRequired'
        - $ref: '#/components/parameters/queryIdRequired'
        - $ref: '#/components/parameters/waitTimeSecondsOptional'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatQueryProcessingResult'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          description: The current project or chat entry not found or deleted
        '500':
          $ref: '#/components/responses/ServerError'
  "/api/knowledge-hub/chat/{chatId}/query/{queryId}/cancel":
    post:
      tags:
        - ChatsPublic
      operationId: cancelRecordProcessing
      summary: 'Cancel response generation request within a user chat'
      parameters:
        - $ref: '#/components/parameters/chatIdRequired'
        - $ref: '#/components/parameters/queryIdRequired'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatQueryProcessingResult'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          description: The current project or the requested chat entry not found or deleted
        '500':
          $ref: '#/components/responses/ServerError'
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      description: The API key contains information about the user account and the current knowledge base project.
  schemas:
    LongId:
      description: Resource identifier.
      type: integer
      format: int64
    Error:
      description: Error content with description.
      required:
        - code
        - description
      type: object
      properties:
        code:
          description: Error code.
          type: string
        description:
          description: Error description.
          type: string
    QueryProcessingStatus:
      description: |
        User’s query processing status.
        - `READY_TO_PROCESS`: the query is ready to be processed.
        - `PROCESSING`: the query is being processed.
        - `FINISHED`: processing completed successfully.
        - `FAILED`: processing failed.
        - `CANCELED`: processing stopped.
      type: string
      enum:
        - READY_TO_PROCESS
        - PROCESSING
        - FINISHED
        - FAILED
        - CANCELED
    BriefSourceInfo:
      type: object
      description: Brief information about the source document.
      required:
        - id
        - path
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        path:
          type: string
          description: Name of the source in the knowledge base.
        externalLink:
          description: |
            Link to the source document. This could be:
            - If the document came from an integration, the link to the original location is provided, such as a link to a page in Confluence.<br/>The link might be missing if the integration hasn’t been synced since the field was introduced.
            - If the document was uploaded manually as a file, a temporary download link is provided. Keep in mind this link expires.<br/>Alternatively, you can use the [GET /sources/{sourceId}/download](#operation/downloadProjectSource) endpoint for persistent access.
          type: string
    QueryProcessingResult:
      type: object
      description: The current result of processing the user query to the knowledge base.
      required:
        - id
        - request
        - status
        - createdAt
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        request:
          description: Text of the original user query.
          type: string
        response:
          description: Text of the response generated by the LLM as a result of processing the user query.
          type: string
        status:
          $ref: '#/components/schemas/QueryProcessingStatus'
        createdAt:
          description: Query processing start time.
          type: string
          format: date-time
        updatedAt:
          description: Last query update time.
          type: string
          format: date-time
        comment:
          description: Comment on the result of query processing.
          type: string
        relevantSources:
          description: List of sources the generated response is based on.
          type: array
          items:
            $ref: '#/components/schemas/BriefSourceInfo'
    ChatQueryProcessingResult:
      type: object
      description: The current result of processing the user query in the knowledge base chat.
      required:
        - id
        - chatId
        - request
        - status
        - createdAt
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        chatId:
          $ref: '#/components/schemas/LongId'
        request:
          description: Text of the original user query.
          type: string
        response:
          description: Text of the response generated by the LLM as a result of processing the user query.
          type: string
        status:
          $ref: '#/components/schemas/QueryProcessingStatus'
        createdAt:
          description: Query creation time.
          type: string
          format: date-time
        updatedAt:
          description: Last query update time.
          type: string
          format: date-time
        comment:
          description: Comment on the result of query processing.
          type: string
        relevantSources:
          description: List of sources the generated response is based on.
          type: array
          items:
            $ref: '#/components/schemas/BriefSourceInfo'
    PipelineType:
      type: string
      description: |
        Method of retrieving relevant chunks:
        - `semantic`: by semantic similarity of embeddings. The vector representation of the user’s query is compared to vector representations of your data chunks.
        - `agent`: by LLM. The LLM itself initiates the search for the most relevant embeddings of your data in the vector store using function calling.
      enum:
        - semantic
        - agent
    LlmSettings:
      type: object
      description: LLM settings.
      properties:
        model:
          description: |
            Model in use.

            The list of available models is provided by the [GET /api/knowledge-hub/info](#operation/getProjectInfo) endpoint in the `resources.llmModels` array.

            If `pipeline = agent`, only models that support function calling are acceptable.
          type: string
        contextWindow:
          description: Maximum size of request context window in tokens.
          type: integer
          format: int32
          minimum: 4000
          maximum: 16000
        maxTokens:
          description: Maximum number of tokens in response.
          type: integer
          format: int32
          minimum: 500
          maximum: 1000
        temperature:
          description: |
            Temperature.
            It influences the balance between predictability and creativity in the response.
            Low temperature means more predictability, high means more creativity.
          type: number
          format: double
          minimum: 0.0
          maximum: 1.0
        topP:
          description: |
            Top P.
            It regulates the diversity of model responses.
            The smaller the value, the stricter the word selection, making the response similar to one with a low temperature.
          type: number
          format: double
          minimum: 0.0
          maximum: 1.0
        frequencyPenalty:
          description: |
            Frequency penalty.
            It adjusts the probability of word repetition in the response.
            The higher the value, the less likely repetition is.
            For example, if the word “good” has already appeared 3 times, the frequency penalty reduces the probability of its reappearance.
          type: number
          format: double
          minimum: -2.0
          maximum: 2.0
        presencePenalty:
          description: |
            Presence penalty.
            The model is penalised for repeating tokens in the response.
            Unlike frequency penalty, where the penalty depends on the number of repetitions, presence penalty punishes all repetitions equally, regardless of frequency.
            For example, a token encountered twice will be penalised the same as one encountered 10 times.
          type: number
          format: double
          minimum: -2.0
          maximum: 2.0
    ParticipantRole:
      type: string
      description: Participant role. `user` means a person asking questions, `assistant` means the knowledge base.
      enum:
        - user
        - assistant
    RerankerType:
      type: string
      description: Type of re-ranking. `manual` means empirical, `model` means using a model.
      enum:
        - manual
        - model
    HistoryRecord:
      type: object
      description: Entry of the chat history. The entry contains the message text and the role of the message author.
      required:
        - content
        - role
      properties:
        content:
          description: Message text.
          type: string
        role:
          $ref: '#/components/schemas/ParticipantRole'
    Rephrase:
      type: object
      description: |
        Rephrasing settings for a user query. Rephrasing helps make the query more clear for the LLM.

        The parameter is used only if `pipeline = semantic`.
      required:
        - prompt
      properties:
        prompt:
          description: |
            Prompt for the LLM with rephrasing instruction. Placeholders can be used in the prompt:
            - `{history_str}`: chat history.
            - `{query_str}`: current user query.
            - `{context_str}`: relevant chunks found at the search stage.
          type: string
          example: "Read the dialogue history, rephrase the current user question considering the history by adding it as context. Make the question more understandable, clear, and structured. Add similar queries and a title to the question, and return the text with the title."
    Reranker:
      type: object
      description: |
        Settings for search result re-ranking.

        The settings are used only if `pipeline = semantic`.
      required:
        - type
      properties:
        type:
          $ref: '#/components/schemas/RerankerType'
        minScore:
          description: |
            Relevance score threshold. Search results scoring below the threshold will not be used for generating a response.
          type: number
          format: double
          minimum: -10.0
          maximum: 10.0
        maxChunksPerDocument:
          description: Maximum number of results from a single document.
          type: integer
          format: int32
          minimum: 1
          maximum: 10
        maxChunks:
          description: Maximum number of results after re-ranking.
          type: integer
          format: int32
          minimum: 1
          maximum: 30
        scoreReductionLimit:
          description: |
            Maximum deviation from the best relevance score, %. Chunks with a greater deviation will not be used for generating a response.
          type: integer
          format: int32
          minimum: 1
          maximum: 40
    FullTextSearchStrategy:
      type: string
      description: |
        Full-text search strategy.
        - `hybrid`: combine all received results of semantic and full-text searches.
        - `weighted`: select a specified number of results of semantic and full-text searches.
        - `threshold`: if semantic search hasn’t provided sufficiently relevant results, return the full-text search results.
      enum:
        - hybrid
        - weighted
        - threshold
    FullTextSearch:
      type: object
      description: |
        Settings for full-text search, which can complement semantic search.

        The settings are used only if `pipeline = semantic`.
      required:
        - strategy
      properties:
        strategy:
          $ref: '#/components/schemas/FullTextSearchStrategy'
        semanticPortion:
          description: |
            Number of the best semantic results to be included in the final result.
            The recommended value is 10.

            The parameter is used for the `weighted` strategy.
          type: integer
          format: int32
          minimum: 0
          maximum: 10
        ftsPortion:
          description: |
            Number of the best full-text search results to be included in the final result.
            The recommended value is 1 or 2.

            The parameter is used for the `weighted` strategy.
          type: integer
          format: int32
          minimum: 1
          maximum: 10
        threshold:
          description: |
            Relevance score threshold for semantic search results.
            If the relevance score is below the threshold for all semantic results, the search switches to the full-text mode.

            The parameter is used for the `threshold` strategy.
          type: number
          format: double
          minimum: 0
          maximum: 1
        useOnlyInSegment:
          description: |
            Perform full-text search only in sources with the tag provided in the `search.segment` parameter.
            If `false` or omitted, full-text search is performed over the entire knowledge base.
          type: boolean
    SearchSettings:
      type: object
      description: Settings for searching relevant chunks.
      required:
        - similarityTopK
      properties:
        similarityTopK:
          description: Number of chunks to be retrieved from sources for further processing, such as re-ranking or generating a response.
          type: integer
          format: int32
          minimum: 5
          maximum: 30
        candidateRadius:
          description: |
            Number of adjacent chunks to be retrieved along with each found chunks.

            For example, if `similarityTopK = 10` and `candidateRadius = 3`, then 3 more chunks will be added before and after each found chunk.
            Thus, the volume of the found chunk will increase, resulting in 10 “large” chunks being sent to the model.
          type: integer
          format: int32
          minimum: 0
          maximum: 10
        reranker:
          $ref: '#/components/schemas/Reranker'
        fullTextSearch:
          $ref: '#/components/schemas/FullTextSearch'
        rephraseUserQuery:
          $ref: '#/components/schemas/Rephrase'
        segment:
          description: |
            Tag of source documents for chunk search. Users can specify the tag when uploading sources to the knowledge base.
          type: string
          example: "FAQ"
    ResponseGeneration:
      type: object
      description: Settings for LLM response generation.
      properties:
        prompt:
          description: Prompt upon which LLM generates a response.
          type: string
        showRelevantSources:
          description: |
            Provide list of sources the generated response is based on.
          type: boolean
    RetrievedChunks:
      type: object
      description: Chunks retrieved.
      required:
        - chunks
      properties:
        chunks:
          type: array
          description: List of chunks, sorted in descending order of relevance score.
          items:
            $ref: '#/components/schemas/ChunkWithScore'
    ChunkMetadata:
      type: object
      description: Chunk metadata.
      required:
        - sourcePath
      properties:
        sourcePath:
          description: Name of the source from which the chunk was retrieved.
          type: string
        sourceUrl:
          description: Link to the source from which the chunk was retrieved.
          type: string
        segment:
          description: Source tag specified by a user when uploading the source to the knowledge base.
          type: string
    ChunkWithScore:
      type: object
      description: Information about the chunk with a relevance score.
      required:
        - score
        - content
        - source
      properties:
        score:
          description: Relevance score of the chunk to the user’s query.
          type: number
          format: double
        content:
          description: Text content of the chunk.
          type: string
        docId:
          deprecated: True
          description: |
            Identifier of the source from which the chunk was retrieved.
            This parameter is deprecated. Use `source` instead.
          type: string
        metadata:
          $ref: '#/components/schemas/ChunkMetadata'
        source:
          $ref: '#/components/schemas/BriefSourceInfo'
    RagSettings:
      type: object
      description: |
        Query processing settings.
      required:
        - pipeline
      properties:
        pipeline:
          $ref: '#/components/schemas/PipelineType'
        search:
          $ref: '#/components/schemas/SearchSettings'
        llm:
          $ref: '#/components/schemas/LlmSettings'
        responseGeneration:
          $ref: '#/components/schemas/ResponseGeneration'
    RetrievingSettings:
      type: object
      description: |
        Chunk search settings.
      required:
        - pipeline
      properties:
        pipeline:
          $ref: '#/components/schemas/PipelineType'
        search:
          $ref: '#/components/schemas/SearchSettings'
        llm:
          $ref: '#/components/schemas/LlmSettings'
    GenerateAnswerRequest:
      type: object
      description: |
        Request to the knowledge base to prepare a response.
        Optionally, the request may include query processing settings and dialogue history.
        If the history is provided, the query is modified using LLM to consider the context.
        If no settings are specified in the request, the settings of the project associated with the API key are used.
      required:
        - query
      properties:
        query:
          description: Text of the user’s query.
          type: string
        history:
          type: array
          description: Dialogue history. Entries are displayed in reverse chronological order (from latest to earliest).
          items:
            $ref: '#/components/schemas/HistoryRecord'
        settings:
          $ref: '#/components/schemas/RagSettings'
    CreateChatRequest:
      type: object
      description: |
        Request to create a new chat.
        Optionally, the request may include query processing settings.
        If no settings are provided in the request, the settings of the project associated with the API key are used.
      properties:
        name:
          description: Name of the user’s chat.
          type: string
        settings:
          $ref: '#/components/schemas/RagSettings'
    Chat:
      type: object
      description: Result of creating a user chat. Contains default search and generation settings for all queries in this chat.
      required:
        - id
        - settings
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        name:
          description: Name of the user’s chat.
          type: string
        settings:
          $ref: '#/components/schemas/RagSettings'
    ChatRequest:
      type: object
      description: |
        Request to the knowledge base within the chat.
        Optionally, the request may include query processing settings.
        Request processing considers the context of the current chat history: previous messages between the user and the knowledge base.
      required:
        - query
      properties:
        query:
          description: Text of the user’s query.
          type: string
        settings:
          $ref: '#/components/schemas/RagSettings'
    RetrieveChunksRequest:
      type: object
      description: |
        Request to search for relevant chunks in the knowledge base.
        Optionally, the request may include retrieval settings and dialogue history.
        If the history is provided, the query is modified using LLM to consider the history.
        If no retrieval settings are specified in the request, the settings of the project associated with the API key are used.
      required:
        - query
      properties:
        query:
          description: Text of the user’s query.
          type: string
        history:
          type: array
          description: Dialogue history. Entries are displayed in reverse chronological order (from latest to earliest).
          items:
            $ref: '#/components/schemas/HistoryRecord'
        settings:
          $ref: '#/components/schemas/RetrievingSettings'
    RetrieveChunksFromChatRequest:
      type: object
      description: |
        Request to search for relevant chunks in the knowledge base within the chat.
        Optionally, the request may include retrieval settings.
        Request processing considers the context of the current chat history: previous messages between the user and the knowledge base.
        If no retrieval settings are specified in the request, the settings of the project associated with the API key are used.
      required:
        - query
      properties:
        query:
          description: Text of the user’s query.
          type: string
        settings:
          $ref: '#/components/schemas/RetrievingSettings'
    ProjectStatus:
      description: |
        Knowledge base project status.
        - `CREATED`: project created but not indexed.
        - `INGESTING_DOCUMENTS`: project indexing in progress.
        - `ACTIVE`: project indexed and ready to process user queries.
        - `INGEST_FAILED`: last indexing of the project failed.
        - `DELETED`: project was deleted.
      type: string
      enum:
        - CREATED
        - INGESTING_DOCUMENTS
        - ACTIVE
        - DELETED
        - INGEST_FAILED
    IntegrationStatus:
      description: |
        Integration status.
        - `ACTIVE`: the last data download from the integration was completed successfully, or it has never been performed.
        - `AWAITING_ACTION`: confirmation of the request to access Confluence Cloud is required.
        - `CHECKING`: data download in progress.
        - `FAILED`: the last data download failed.
        - `DISABLED`: reserved for future use.
      type: string
      enum:
        - ACTIVE
        - AWAITING_ACTION
        - CHECKING
        - FAILED
        - DISABLED
    ProjectResources:
      type: object
      description: Resources available in the project.
      required:
        - llmModels
      properties:
        llmModels:
          type: array
          description: Language models.
          items:
            type: string
    ProjectInfo:
      type: object
      description: Knowledge base project information.
      required:
        - id
        - name
        - status
        - createdAt
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        name:
          description: Project name.
          type: string
        status:
          $ref: '#/components/schemas/ProjectStatus'
        resources:
          $ref: '#/components/schemas/ProjectResources'
        createdAt:
          description: Date and time of project creation.
          type: string
          format: date-time
        updatedAt:
          description: Date and time of the last status update of the project.
          type: string
          format: date-time
    SourceDTO:
      description: Information about the source.
      required:
        - id
        - name
        - type
        - status
        - version
        - createdAt
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        name:
          description: Name of the source.
          type: string
        segment:
          description: Source tag specified by a user when uploading the source to the knowledge base.
          type: string
        status:
          description: Indexing status of the source.
          type: string
        lastError:
          description: Last indexing error.
          type: string
        version:
          description: Source version in the project.
          type: string
        sizeBytes:
          description: Source size in bytes.
          type: integer
        sizeChars:
          description: Number of characters in the indexed source.
          type: integer
        createdAt:
          description: Date and time of adding the source to the project.
          type: string
          format: date-time
    ProjectSources:
      type: object
      description: Project sources.
      required:
        - sources
      properties:
        sources:
          type: array
          items:
            $ref: '#/components/schemas/SourceDTO'
    ProjectIntegrations:
      type: object
      description: Project integrations.
      required:
        - integrations
      properties:
        integrations:
          type: array
          items:
            $ref: '#/components/schemas/IntegrationDTO'
    IntegrationCreateRequest:
      description: Request to create an integration.
      required:
        - name
        - settings
      properties:
        name:
          description: Name of the integration.
          type: string
        autoSync:
          type: boolean
          description: Enable automatic data synchronisation
        syncIntervalMinutes:
          type: integer
          description: Update interval in minutes. The minimum value is 30.
        settings:
          $ref: '#/components/schemas/IntegrationSettings'
    IntegrationDTO:
      description: Information about the integration.
      required:
        - id
        - name
        - autoSync
        - syncIntervalMinutes
        - settings
        - status
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        name:
          description: Name of the integration.
          type: string
        autoSync:
          type: boolean
          description: Enable automatic data synchronisation.
        syncIntervalMinutes:
          type: integer
          description: Update interval in minutes.
        settings:
          $ref: '#/components/schemas/IntegrationSettings'
        status:
          $ref: '#/components/schemas/IntegrationStatus'
        lastError:
          type: string
          description: Last data download error.
    IntegrationSettings:
      description: Integration settings.
      type: object
      discriminator:
        propertyName: type
        mapping:
          confluence: '#/components/schemas/ConfluenceSettings'
          cloud_confluence: '#/components/schemas/CloudConfluenceSettings'
      required:
        - baseUrl
      properties:
        baseUrl:
          type: string
          description: Base URL of the integration.
    ConfluenceSettings:
      description: Settings for integration with Confluence Data Center or Confluence Server.
      allOf:
        - $ref: '#/components/schemas/IntegrationSettings'
        - type: object
          required:
            - type
            - space
            - token
          properties:
            type:
              type: string
              enum:
              - confluence
              default: confluence
              description: Type of integration.
            space:
              type: string
              description: Name of the space.
            token:
              type: string
              description: Personal access token from the profile settings.
    CloudConfluenceSettings:
      description: Settings for integration with Confluence Cloud.
      allOf:
        - $ref: '#/components/schemas/IntegrationSettings'
        - type: object
          required:
            - type
            - space
          properties:
            type:
              type: string
              enum:
              - cloud_confluence
              default: cloud_confluence
              description: Type of integration.
            space:
              type: string
              description: Name of the space.
            selectedCloudId:
              type: string
              description: Cloud ID. See the [Atlassian documentation](https://support.atlassian.com/jira/kb/retrieve-my-atlassian-sites-cloud-id/).
    AddLinkRequest:
      required:
        - link
      properties:
        name:
          type: string
          description: Name of the source. It must be unique within the project. If not specified, it is automatically generated.
        link:
          type: string
          description: |
            Link to the file. See the [file requirements](https://data-agent.tovie.ai/docs/upload#file-requirements).
            If the link leads to an HTML page, only the HTML is downloaded, excluding images.
        segment:
          type: string
          description: Tag (knowledge base segment). If specified, the source will be available when searching by this tag.
    UpdateLinkRequest:
      required:
        - link
      properties:
        name:
          type: string
          description: Name of the source. If no source with this name exists, an error is returned.
        link:
          type: string
          description: |
            Link to the file. See the [file requirements](https://data-agent.tovie.ai/docs/upload#file-requirements).
            If the link leads to an HTML page, only the HTML is downloaded, excluding images.
        segment:
          type: string
          description: Tag (knowledge base segment). If specified, the source will be available when searching by this tag.
    AddTextRequest:
      required:
        - text
      properties:
        name:
          type: string
          description: Name of the source. It must be unique within the project. If not specified, it is automatically generated.
        text:
          type: string
          description: 'Text of the source. The maximum size is specified in the [file requirements](https://data-agent.tovie.ai/docs/upload#file-requirements).'
        segment:
          type: string
          description: Tag (knowledge base segment). If specified, the source will be available when searching by this tag.
    UpdateTextRequest:
      required:
        - text
      properties:
        name:
          type: string
          description: Name of the source. If no source with this name exists, an error is returned.
        text:
          type: string
          description: 'Text of the source. The maximum size is specified in the [file requirements](https://data-agent.tovie.ai/docs/upload#file-requirements).'
        segment:
          type: string
          description: Tag (knowledge base segment). If specified, the source will be available when searching by this tag.
  parameters:
    queryIdRequired:
      name: queryId
      in: path
      description: Identifier of the response generation request.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'
    chatIdRequired:
      name: chatId
      in: path
      description: Identifier of the chat in the knowledge base project.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'
    sourceIdRequired:
      name: sourceId
      in: path
      description: Identifier of the source in the knowledge base project.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'
    sourceStatus:
      name: sourceStatus
      in: query
      description: |
        Select sources with the specified indexing status. Acceptable statuses:
        - `READY_TO_INGEST`: ready for indexing.
        - `PROCESSING`: in progress.
        - `INGESTED`: indexed.
        - `FAILED_TO_INGEST`: indexing error.
      required: false
      schema:
        type: string
    createDateFrom:
      name: createDateFrom
      in: query
      description: Select sources added on or after the specified date.
      required: false
      schema:
        type: string
        format: date
    createDateTo:
      name: createDateTo
      in: query
      description: Select sources added on or before the specified date.
      required: false
      schema:
        type: string
        format: date
    waitTimeSecondsOptional:
      in: query
      name: waitTimeSeconds
      description: HTTP request timeout. Used in long polling.
      required: false
      schema:
        type: integer
        format: int32
        minimum: 0
        default: 3
        maximum: 30
    integrationIdRequired:
      name: integrationId
      in: path
      description: Identifier of the integration.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'
  responses:
    UnauthorizedError:
      description: Unauthorized request or invalid API key
    ForbiddenError:
      description: Access denied
    ProjectNotFoundError:
      description: The current project not found or deleted
    ServerError:
      description: Server error
    BadRequestError:
      description: Request parameter error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
security:
  - bearerAuth: [ ]
