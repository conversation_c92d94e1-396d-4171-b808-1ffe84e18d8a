package com.justai.khub.util

import com.justai.khub.api.public.model.IntegrationCreateRequest
import com.justai.khub.api.public.model.IntegrationDTO
import com.justai.khub.api.public.model.ProjectIntegrations
import com.justai.khub.common.util.JSON
import com.justai.khub.util.WebUtils.parseTo
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.stereotype.Component
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.ResultActions
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@Component
class IntegrationPublicHelper {

    @Autowired
    protected lateinit var mvc: MockMvc

    fun createIntegrationAndExpectSuccess(request: IntegrationCreateRequest, apiKey: String): IntegrationDTO {
        return createIntegration(request, apiKey)
            .andExpect(status().isOk)
            .andReturn().parseTo<IntegrationDTO>()
    }

    fun createIntegration(request: IntegrationCreateRequest, apiKey: String): ResultActions {
        return mvc.perform(
            post("/api/knowledge-hub/integrations")
                .header("Authorization", "Bearer $apiKey")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
        )
    }

    fun getIntegration(integrationId: Long, apiKey: String): IntegrationDTO {
        return mvc.perform(
            get("/api/knowledge-hub/integrations/$integrationId")
                .header("Authorization", "Bearer $apiKey")
        )
            .andExpect(status().isOk)
            .andReturn().parseTo<IntegrationDTO>()
    }

    fun getAllIntegrations(apiKey: String): List<IntegrationDTO> {
        return mvc.perform(
            get("/api/knowledge-hub/integrations")
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization", "Bearer $apiKey")
        ).andExpect(status().isOk).andReturn().parseTo<ProjectIntegrations>().integrations
    }

    fun deleteIntegration(integrationId: Long, apiKey: String) {
        mvc.perform(
            delete("/api/knowledge-hub/integrations/$integrationId")
                .header("Authorization", "Bearer $apiKey")
        )
            .andExpect(status().isOk)
            .andReturn()
    }
}
