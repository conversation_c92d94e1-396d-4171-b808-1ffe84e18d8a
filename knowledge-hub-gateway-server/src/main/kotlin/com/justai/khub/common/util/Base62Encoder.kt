package com.justai.khub.common.util

object Base62Encoder {
    /**
     * Base62 characters table sorted to quickly calculate decimal equivalency by compensating.
     */
    private val ALPHABET: CharArray = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".toCharArray()
    private const val BASE: Int = 62

    fun encode(source: Long): String {
        var curr = source
        val sb = StringBuilder()
        do {
            sb.append(ALPHABET[(curr % 62).toInt()])
            curr /= BASE
        } while (curr > 0)
        return sb.reverse().toString()
    }

    fun decode(source: String): Long {
        var result = 0L
        var power = 1L
        for (i in source.length - 1 downTo 0) {
            var digit = source[i].code
            digit = when {
                digit >= '0'.code && digit <= '9'.code -> digit - '0'.code
                digit >= 'A'.code && digit <= 'Z'.code -> digit - 'A'.code + 10
                digit >= 'a'.code && digit <= 'z'.code -> digit - 'a'.code + 36
                else -> throw IllegalArgumentException("Invalid character in Base62 string: ${source[i]}")
            }
            result += digit * power
            power *= BASE
        }
        return result
    }
}
