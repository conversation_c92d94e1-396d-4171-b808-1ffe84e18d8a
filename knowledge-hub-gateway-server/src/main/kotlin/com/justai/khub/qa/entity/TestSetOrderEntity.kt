package com.justai.khub.qa.entity

import com.justai.khub.project.entity.ProjectEntity
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.*
import org.hibernate.annotations.Type

@Entity
@Table(name = "test_set_order")
class TestSetOrderEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = -1

    @OneToOne
    lateinit var project: ProjectEntity

    @Type(JsonType::class)
    @Column(columnDefinition = "jsonb")
    var orderedIds: List<Long> = listOf()
}
