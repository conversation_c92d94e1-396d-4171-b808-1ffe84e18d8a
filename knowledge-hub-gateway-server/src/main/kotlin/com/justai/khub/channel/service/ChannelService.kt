package com.justai.khub.channel.service

import com.justai.khub.api.fe.model.ApiKeyCreateRequest
import com.justai.khub.api.fe.model.ChannelDTO
import com.justai.khub.api.fe.model.ChannelType
import com.justai.khub.channel.dto.JaicpProjectSettings
import com.justai.khub.channel.entity.ChannelEntity
import com.justai.khub.channel.enumeration.ChannelStatus
import com.justai.khub.channel.mapper.ChannelMapper.getJaicpChannelStatus
import com.justai.khub.channel.mapper.ChannelMapper.toEntity
import com.justai.khub.channel.repository.ChannelRepository
import com.justai.khub.common.configuration.dto.IntegrationProperties
import com.justai.khub.common.configuration.dto.KHubUser
import com.justai.khub.common.connector.JaicpServiceConnector
import com.justai.khub.common.entity.CCUserEntity
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.common.util.Base62Encoder
import com.justai.khub.project.entity.ApiKeyEntity
import com.justai.khub.project.entity.ProjectEntity
import com.justai.khub.project.entity.ProjectVersionEntity
import com.justai.khub.project.mapper.ApiKeyMapper
import com.justai.khub.project.service.ApiKeyService
import com.justai.khub.project.service.ProjectVersionService
import com.justai.khub.user.enumeration.ApiKeyStatus
import org.slf4j.LoggerFactory
import org.springframework.data.domain.*
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import java.time.LocalDateTime

@Service
class ChannelService(
    private val channelRepository: ChannelRepository,
    private val projectVersionService: ProjectVersionService,
    private val apiKeyService: ApiKeyService,
    private val jaicpServiceConnector: JaicpServiceConnector,
    private val integrationProperties: IntegrationProperties,
    private val apiKeyMapper: ApiKeyMapper,
    private val transactionTemplate: TransactionTemplate,
) {

    private val log = LoggerFactory.getLogger(this::class.java)

    val DEFAULT_API_KEY_PAGEABLE = PageRequest.of(0, 100, Sort.by(Sort.Direction.DESC, "createdAt"))
    val DEFAULT_CHANNEL_PAGEABLE = PageRequest.of(0, 100, Sort.by(Sort.Direction.DESC, "createdAt"))


    @Transactional(rollbackFor = [Exception::class])
    fun createChannel(
        createRequest: ChannelDTO,
        projectId: Long,
        versionName: String,
        currentUser: KHubUser
    ): ChannelEntity {
        val version = projectVersionService.getVersion(projectId, versionName, currentUser.accountId)
        val jaicpApiKey = selectApiKey(createRequest, projectId, version, currentUser)
        log.info("Create channel $createRequest in project ${version.name} for account ${currentUser.accountId}")
        val channelEntity = when (createRequest.type) {
            ChannelType.JAICP -> {
                val botProject = jaicpServiceConnector.createProjectFromKhubTemplate(
                    jaicpProjectName = createRequest.name,
                    khubApiToken = "${Base62Encoder.encode(jaicpApiKey.id)}.${jaicpApiKey.value!!}",
                    user = currentUser
                )
                botProject.toEntity(version, jaicpApiKey)
            }
        }
        val createdChannel = channelRepository.save(channelEntity)
        return createdChannel
    }

    fun getChannels(
        projectId: Long,
        versionName: String,
        currentUser: KHubUser,
        pageable: Pageable
    ): Page<ChannelEntity> {
        val channels = transactionTemplate.execute {
            val version = projectVersionService.getVersion(projectId, versionName, currentUser.accountId)
            channelRepository.findAllByVersionIdAndStatusIn(
                version.id,
                listOf(ChannelStatus.ACTIVE, ChannelStatus.FAILED),
                pageable
            )
        }!!
        log.info("Get channels in project ${projectId} for account ${currentUser.accountId}")
        val channelsByType = channels.groupBy {
            when (it.settings) {
                is JaicpProjectSettings -> ChannelType.JAICP
            }
        }
        val onlyJaicpChannels = channelsByType[ChannelType.JAICP]?.let { khubJaicpChannels ->
            fetchJaicpChannels(currentUser, khubJaicpChannels)
        } ?: emptyList()
        return PageImpl(onlyJaicpChannels) // plus other channels by type
    }

    fun deleteAvailableChannels(
        projectId: Long,
        versionName: String,
        currentUser: KHubUser
    ) {
        val channels = transactionTemplate.execute {
            val version = projectVersionService.getVersion(projectId, versionName, currentUser.accountId)
            channelRepository.findAllByVersionIdAndStatusIn(
                version.id,
                listOf(ChannelStatus.ACTIVE, ChannelStatus.FAILED),
                DEFAULT_CHANNEL_PAGEABLE
            )
        }!!
        log.info("Delete channels: ${channels} in project ${projectId} for account ${currentUser.accountId}")
        channels.forEach { channel ->
            try {
                val settings = channel.settings
                when (settings) {
                    is JaicpProjectSettings -> {
                        jaicpServiceConnector.deleteProject(
                            user = currentUser,
                            settings.projectId
                        )
                    }
                }
            } catch (kex: KhubException) {
                if (kex.errors.firstOrNull { it.error == ApiErrorCode.CHANNEL_NOT_FOUND } != null) {
                    log.warn("Channel not found: $channel", kex)
                } else {
                    throw kex
                }
            }
        }
    }

    fun blockJaicpChannel(
        projectId: Long,
        versionName: String,
        channelId: Long,
        jaicpChannel: Long,
        currentUser: KHubUser
    ) {
        val channel = transactionTemplate.execute {
            val version = projectVersionService.getVersion(projectId, versionName, currentUser.accountId)
            channelRepository.findByIdAndVersionId(channelId, version.id)
        } ?: throw KhubException(ApiErrorCode.EXTERNAL_CHANNEL_NOT_FOUND)
        val settings = channel.settings
        if (settings is JaicpProjectSettings) {
            log.info("Block jaicp channel ${jaicpChannel} in khub channel ${channel.id} for account ${currentUser.accountId}")
            jaicpServiceConnector.blockProjectChannel(currentUser, jaicpChannel)
        } else {
            throw IllegalStateException("Channel $channelId is not jaicp project")
        }
    }

    fun unblockJaicpChannel(
        projectId: Long,
        versionName: String,
        channelId: Long,
        jaicpChannel: Long,
        currentUser: KHubUser
    ) {
        val channel = transactionTemplate.execute {
            val version = projectVersionService.getVersion(projectId, versionName, currentUser.accountId)
            channelRepository.findByIdAndVersionId(channelId, version.id)
        } ?: throw KhubException(ApiErrorCode.EXTERNAL_CHANNEL_NOT_FOUND)
        val settings = channel.settings
        if (settings is JaicpProjectSettings) {
            log.info("Unblock jaicp channel ${jaicpChannel} in khub channel ${channel.id}, jaicp-integration info ${channel.settings} for account ${currentUser.accountId}")
            jaicpServiceConnector.unblockProjectChannel(currentUser, jaicpChannel)
        } else {
            throw IllegalStateException("Channel $channelId is not jaicp project")
        }
    }


    private fun fetchJaicpChannels(currentUser: KHubUser, originKhubJaicpChannels: Iterable<ChannelEntity>): List<ChannelEntity> {
        val khubProjects = jaicpServiceConnector.getKhubProjects(
            user = currentUser
        )
        val khubProjectByIds = khubProjects.groupBy { it.id }
        val deleted = mutableListOf<ChannelEntity>()
        val updated = mutableListOf<ChannelEntity>()
        val fetched = mutableListOf<ChannelEntity>()
        originKhubJaicpChannels.forEach { khubJaicpChannel ->
            val jaicpProjectId = (khubJaicpChannel.settings as JaicpProjectSettings).projectId
            val actualProject = khubProjectByIds[jaicpProjectId]
            if (actualProject != null) {
                val settings = khubJaicpChannel.settings as JaicpProjectSettings
                val actualBotConfigs = actualProject.firstOrNull()?.botConfigs?.filter { it.channelType != "TEST_WIDGET" } ?: emptyList()
                if (settings.channels.toSet() != actualBotConfigs.toSet()) {
                    khubJaicpChannel.settings = settings.copy(
                        channels = actualBotConfigs
                            .onEach { it.actualStatus = getJaicpChannelStatus(it) }
                            .sortedBy { it.botserverBotId }
                    )
                    khubJaicpChannel.updatedAt = LocalDateTime.now()
                    khubJaicpChannel.updatedBy = CCUserEntity().also { it.accountId = currentUser.accountId; it.userId = currentUser.userId }
                    updated.add(khubJaicpChannel)
                }
                fetched.add(khubJaicpChannel)
            } else {
                deleted.add(khubJaicpChannel)
            }
        }
        if (updated.isNotEmpty() || deleted.isNotEmpty()) {
            log.info("Fetching result for account ${currentUser.accountId}: deleted ${deleted.size}, updated ${updated.size}")
            transactionTemplate.execute {
                channelRepository.saveAll(updated)
                channelRepository.deleteAll(deleted)
            }
        }
        return fetched
    }

    private fun selectApiKey(
        createRequest: ChannelDTO,
        projectId: Long,
        version: ProjectVersionEntity,
        currentUser: KHubUser
    ): ApiKeyEntity {
        val apiKeys = apiKeyService.getApiKeys(true, projectId, currentUser.accountId, DEFAULT_API_KEY_PAGEABLE)
            .filter { it.value != null && it.status == ApiKeyStatus.ACTIVE }
        return apiKeys.firstOrNull { it.expiredAt == null }
            ?: apiKeys.firstOrNull()
            ?: createNewApiKey(
                createRequest,
                currentUser,
                version.project
            ) // we should crete api key for jaicp integration if no one exists
    }

    private fun createNewApiKey(
        createRequest: ChannelDTO,
        currentUser: KHubUser,
        project: ProjectEntity
    ): ApiKeyEntity {
        log.info("Create new api key for channel $createRequest in project $project for account ${currentUser.accountId}")
        val newEntity =
            apiKeyMapper.toEntity(ApiKeyCreateRequest(name = "${createRequest.name}_generated"), project, currentUser)
        if (apiKeyService.checkActiveApiKeyByName(project.id, currentUser.accountId, newEntity.name)) {
            throw KhubException(ApiErrorCode.DUPLICATE_API_KEY_NAME)
        }
        return apiKeyService.createApiKey(newEntity)
    }
}
