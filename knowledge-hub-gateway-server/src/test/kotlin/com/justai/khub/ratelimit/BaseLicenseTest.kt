package com.justai.khub.ratelimit

import com.justai.khub.BaseProjectIT
import com.justai.khub.api.fe.model.ChatDTO
import org.junit.jupiter.api.BeforeEach
import org.springframework.test.web.servlet.ResultActions

class BaseLicenseTest : BaseProjectIT() {

    protected lateinit var chat: ChatDTO
    protected lateinit var apiKey: String

    val billableEndpoints: List<() -> ResultActions> = listOf(
        // retrieve
        { chatPublicHelper.retrieveByChat(chat.id, "Test message", apiKey = apiKey) },
        { chatPublicHelper.retrieveByQuery("Test message", apiKey = apiKey) },
        { retrieveHelper.retrieveChunks(project.id, "Test message") },
        { retrieveHelper.retrieveChunksForDefaultChat(project.id, "Test message") },
        // generate internal
        { chatHelper.makeRequestToDefaultChat(project.id, "Test message") },
        { chatHelper.makeRequest(project.id, chat.id, "Test message") },
        // generate public
        { chatPublicHelper.makeRequest(chat.id, "Test message", apiKey = apiKey) },
        { chatPublicHelper.makeRequestAsync(chat.id, "Test message", apiKey = apiKey) },
        { chatPublicHelper.generateQueryAnswer(chat.id, "Test message", apiKey = apiKey) },
        { chatPublicHelper.asyncGenerateQueryAnswer(chat.id, "Test message", apiKey = apiKey) },
    )

    @BeforeEach
    fun init() {
        chat = chatHelper.createChat(project.id)
        apiKey = apiKeyHelper.createApiKey(project.id).key!!
    }
}
