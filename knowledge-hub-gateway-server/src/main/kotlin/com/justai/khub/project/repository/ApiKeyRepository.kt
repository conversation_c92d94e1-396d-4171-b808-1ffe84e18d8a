package com.justai.khub.project.repository

import com.justai.khub.project.entity.ApiKeyEntity
import com.justai.khub.user.enumeration.ApiKeyStatus
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.EntityGraph
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface ApiKeyRepository : JpaRepository<ApiKeyEntity, Long> {
    @EntityGraph(attributePaths = ["project"])
    @Query("SELECT k FROM ApiKeyEntity k WHERE k.createdBy.accountId = :accountId AND k.project.id = :projectId")
    fun findAllByAccountIdAndProjectId(accountId: Long, projectId: Long, pageable: Pageable): Page<ApiKeyEntity>

    @EntityGraph(attributePaths = ["project"])
    @Query("SELECT k FROM ApiKeyEntity k WHERE k.createdBy.accountId = :accountId AND k.project.id = :projectId AND k.status = :status AND (k.expiredAt is null or k.expiredAt > :now)")
    fun findActiveByAccountIdAndProjectId(
        accountId: Long,
        projectId: Long,
        status: ApiKeyStatus,
        now: LocalDateTime,
        pageable: Pageable
    ): Page<ApiKeyEntity>

    fun findByHash(hash: String): ApiKeyEntity?

    @EntityGraph(attributePaths = ["project"])
    @Query("SELECT k FROM ApiKeyEntity k WHERE k.createdBy.accountId = :accountId AND k.project.id = :projectId AND k.status = :status AND k.name = :name")
    fun findActiveByAccountIdAndProjectIdAndName(
        accountId: Long,
        projectId: Long,
        name: String,
        status: ApiKeyStatus
    ): List<ApiKeyEntity>
}
