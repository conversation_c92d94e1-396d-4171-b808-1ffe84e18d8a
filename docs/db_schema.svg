<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.43.0 (0)
 -->
<!-- Title: g Pages: 1 -->
<svg width="1849pt" height="755pt"
 viewBox="0.00 0.00 1849.00 755.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 751)">
<title>g</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-751 1845,-751 1845,4 -4,4"/>
<!-- api_key -->
<g id="node1" class="node">
<title>api_key</title>
<text text-anchor="start" x="19" y="-220.5" font-family="Times,serif" font-size="10.00"> </text>
<polygon fill="#e5e5e5" stroke="transparent" points="24,-214.5 24,-231.5 292,-231.5 292,-214.5 24,-214.5"/>
<polygon fill="none" stroke="black" points="24,-214.5 24,-231.5 292,-231.5 292,-214.5 24,-214.5"/>
<text text-anchor="start" x="139" y="-220.5" font-family="Times,serif" font-size="10.00"> api_key </text>
<text text-anchor="start" x="26" y="-204.5" font-family="Times,serif" font-size="10.00"> id </text>
<text text-anchor="start" x="135" y="-204.5" font-family="Times,serif" font-size="10.00"> bigserial </text>
<text text-anchor="start" x="263" y="-204.5" font-family="Times,serif" font-size="10.00"> PK </text>
<text text-anchor="start" x="285" y="-204.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="294" y="-204.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="26" y="-189.5" font-family="Times,serif" font-size="10.00"> name </text>
<text text-anchor="start" x="135" y="-189.5" font-family="Times,serif" font-size="10.00"> character varying(255) </text>
<text text-anchor="start" x="263" y="-189.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="285" y="-189.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="294" y="-189.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="26" y="-174.5" font-family="Times,serif" font-size="10.00"> status </text>
<text text-anchor="start" x="135" y="-174.5" font-family="Times,serif" font-size="10.00"> character varying(255) </text>
<text text-anchor="start" x="263" y="-174.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="285" y="-174.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="294" y="-174.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="26" y="-159.5" font-family="Times,serif" font-size="10.00"> prefix </text>
<text text-anchor="start" x="135" y="-159.5" font-family="Times,serif" font-size="10.00"> character varying </text>
<text text-anchor="start" x="263" y="-159.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="285" y="-159.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="294" y="-159.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="26" y="-144.5" font-family="Times,serif" font-size="10.00"> hash </text>
<text text-anchor="start" x="135" y="-144.5" font-family="Times,serif" font-size="10.00"> character varying </text>
<text text-anchor="start" x="263" y="-144.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="285" y="-144.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="294" y="-144.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="26" y="-129.5" font-family="Times,serif" font-size="10.00"> owner_login </text>
<text text-anchor="start" x="135" y="-129.5" font-family="Times,serif" font-size="10.00"> character varying </text>
<text text-anchor="start" x="263" y="-129.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="285" y="-129.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="294" y="-129.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="26" y="-114.5" font-family="Times,serif" font-size="10.00"> created_at </text>
<text text-anchor="start" x="135" y="-114.5" font-family="Times,serif" font-size="10.00"> timestamp without time zone </text>
<text text-anchor="start" x="263" y="-114.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="285" y="-114.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="294" y="-114.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="26" y="-99.5" font-family="Times,serif" font-size="10.00"> expired_at </text>
<text text-anchor="start" x="135" y="-99.5" font-family="Times,serif" font-size="10.00"> timestamp without time zone </text>
<text text-anchor="start" x="263" y="-99.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="285" y="-99.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="294" y="-99.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="26" y="-84.5" font-family="Times,serif" font-size="10.00"> last_used_at </text>
<text text-anchor="start" x="135" y="-84.5" font-family="Times,serif" font-size="10.00"> timestamp without time zone </text>
<text text-anchor="start" x="263" y="-84.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="285" y="-84.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="294" y="-84.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="26" y="-69.5" font-family="Times,serif" font-size="10.00"> created_by_account_id </text>
<text text-anchor="start" x="135" y="-69.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="263" y="-69.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="285" y="-69.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="294" y="-69.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="26" y="-54.5" font-family="Times,serif" font-size="10.00"> created_by_user_id </text>
<text text-anchor="start" x="135" y="-54.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="263" y="-54.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="285" y="-54.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="294" y="-54.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="26" y="-39.5" font-family="Times,serif" font-size="10.00"> updated_at </text>
<text text-anchor="start" x="135" y="-39.5" font-family="Times,serif" font-size="10.00"> timestamp without time zone </text>
<text text-anchor="start" x="263" y="-39.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="285" y="-39.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="294" y="-39.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="26" y="-24.5" font-family="Times,serif" font-size="10.00"> updated_by_account_id </text>
<text text-anchor="start" x="135" y="-24.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="263" y="-24.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="285" y="-24.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="294" y="-24.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="26" y="-9.5" font-family="Times,serif" font-size="10.00"> updated_by_user_id </text>
<text text-anchor="start" x="135" y="-9.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="263" y="-9.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="285" y="-9.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="294" y="-9.5" font-family="Times,serif" font-size="10.00"> </text>
<polygon fill="none" stroke="black" points="16,-4 16,-233 300,-233 300,-4 16,-4"/>
</g>
<!-- chat -->
<g id="node2" class="node">
<title>chat</title>
<text text-anchor="start" x="508.5" y="-340.5" font-family="Times,serif" font-size="10.00"> </text>
<polygon fill="#e5e5e5" stroke="transparent" points="513.5,-334.5 513.5,-351.5 794.5,-351.5 794.5,-334.5 513.5,-334.5"/>
<polygon fill="none" stroke="black" points="513.5,-334.5 513.5,-351.5 794.5,-351.5 794.5,-334.5 513.5,-334.5"/>
<text text-anchor="start" x="643" y="-340.5" font-family="Times,serif" font-size="10.00"> chat </text>
<text text-anchor="start" x="515.5" y="-324.5" font-family="Times,serif" font-size="10.00"> id </text>
<text text-anchor="start" x="624.5" y="-324.5" font-family="Times,serif" font-size="10.00"> bigserial </text>
<text text-anchor="start" x="752.5" y="-324.5" font-family="Times,serif" font-size="10.00"> PK </text>
<text text-anchor="start" x="774.5" y="-324.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-324.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-309.5" font-family="Times,serif" font-size="10.00"> name </text>
<text text-anchor="start" x="624.5" y="-309.5" font-family="Times,serif" font-size="10.00"> character varying </text>
<text text-anchor="start" x="752.5" y="-309.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-309.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-309.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-294.5" font-family="Times,serif" font-size="10.00"> search_settings </text>
<text text-anchor="start" x="624.5" y="-294.5" font-family="Times,serif" font-size="10.00"> jsonb </text>
<text text-anchor="start" x="752.5" y="-294.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-294.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-294.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-279.5" font-family="Times,serif" font-size="10.00"> version_id </text>
<text text-anchor="start" x="624.5" y="-279.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="752.5" y="-279.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-279.5" font-family="Times,serif" font-size="10.00"> FK </text>
<text text-anchor="start" x="796.5" y="-279.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-264.5" font-family="Times,serif" font-size="10.00"> created_at </text>
<text text-anchor="start" x="624.5" y="-264.5" font-family="Times,serif" font-size="10.00"> timestamp without time zone </text>
<text text-anchor="start" x="752.5" y="-264.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-264.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-264.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-249.5" font-family="Times,serif" font-size="10.00"> created_by_account_id </text>
<text text-anchor="start" x="624.5" y="-249.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="752.5" y="-249.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-249.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-249.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-234.5" font-family="Times,serif" font-size="10.00"> created_by_user_id </text>
<text text-anchor="start" x="624.5" y="-234.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="752.5" y="-234.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-234.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-234.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-219.5" font-family="Times,serif" font-size="10.00"> updated_at </text>
<text text-anchor="start" x="624.5" y="-219.5" font-family="Times,serif" font-size="10.00"> timestamp without time zone </text>
<text text-anchor="start" x="752.5" y="-219.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-219.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-219.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-204.5" font-family="Times,serif" font-size="10.00"> updated_by_account_id </text>
<text text-anchor="start" x="624.5" y="-204.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="752.5" y="-204.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-204.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-204.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-189.5" font-family="Times,serif" font-size="10.00"> updated_by_user_id </text>
<text text-anchor="start" x="624.5" y="-189.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="752.5" y="-189.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-189.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-189.5" font-family="Times,serif" font-size="10.00"> </text>
<polygon fill="none" stroke="black" points="505,-184 505,-353 802,-353 802,-184 505,-184"/>
</g>
<!-- project_version -->
<g id="node7" class="node">
<title>project_version</title>
<text text-anchor="start" x="1026.5" y="-490.5" font-family="Times,serif" font-size="10.00"> </text>
<polygon fill="#e5e5e5" stroke="transparent" points="1031.5,-484.5 1031.5,-501.5 1312.5,-501.5 1312.5,-484.5 1031.5,-484.5"/>
<polygon fill="none" stroke="black" points="1031.5,-484.5 1031.5,-501.5 1312.5,-501.5 1312.5,-484.5 1031.5,-484.5"/>
<text text-anchor="start" x="1137.5" y="-490.5" font-family="Times,serif" font-size="10.00"> project_version </text>
<text text-anchor="start" x="1033.5" y="-474.5" font-family="Times,serif" font-size="10.00"> id </text>
<text text-anchor="start" x="1142.5" y="-474.5" font-family="Times,serif" font-size="10.00"> bigserial </text>
<text text-anchor="start" x="1270.5" y="-474.5" font-family="Times,serif" font-size="10.00"> PK </text>
<text text-anchor="start" x="1292.5" y="-474.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1314.5" y="-474.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1033.5" y="-459.5" font-family="Times,serif" font-size="10.00"> name </text>
<text text-anchor="start" x="1142.5" y="-459.5" font-family="Times,serif" font-size="10.00"> character varying(255) </text>
<text text-anchor="start" x="1270.5" y="-459.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1292.5" y="-459.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1314.5" y="-459.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1033.5" y="-444.5" font-family="Times,serif" font-size="10.00"> project_id </text>
<text text-anchor="start" x="1142.5" y="-444.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="1270.5" y="-444.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1292.5" y="-444.5" font-family="Times,serif" font-size="10.00"> FK </text>
<text text-anchor="start" x="1314.5" y="-444.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1033.5" y="-429.5" font-family="Times,serif" font-size="10.00"> ingest_settings </text>
<text text-anchor="start" x="1142.5" y="-429.5" font-family="Times,serif" font-size="10.00"> jsonb </text>
<text text-anchor="start" x="1270.5" y="-429.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1292.5" y="-429.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1314.5" y="-429.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1033.5" y="-414.5" font-family="Times,serif" font-size="10.00"> search_settings </text>
<text text-anchor="start" x="1142.5" y="-414.5" font-family="Times,serif" font-size="10.00"> jsonb </text>
<text text-anchor="start" x="1270.5" y="-414.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1292.5" y="-414.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1314.5" y="-414.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1033.5" y="-399.5" font-family="Times,serif" font-size="10.00"> index_name </text>
<text text-anchor="start" x="1142.5" y="-399.5" font-family="Times,serif" font-size="10.00"> character varying </text>
<text text-anchor="start" x="1270.5" y="-399.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1292.5" y="-399.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1314.5" y="-399.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1033.5" y="-384.5" font-family="Times,serif" font-size="10.00"> created_at </text>
<text text-anchor="start" x="1142.5" y="-384.5" font-family="Times,serif" font-size="10.00"> timestamp without time zone </text>
<text text-anchor="start" x="1270.5" y="-384.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1292.5" y="-384.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1314.5" y="-384.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1033.5" y="-369.5" font-family="Times,serif" font-size="10.00"> created_by_account_id </text>
<text text-anchor="start" x="1142.5" y="-369.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="1270.5" y="-369.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1292.5" y="-369.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1314.5" y="-369.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1033.5" y="-354.5" font-family="Times,serif" font-size="10.00"> created_by_user_id </text>
<text text-anchor="start" x="1142.5" y="-354.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="1270.5" y="-354.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1292.5" y="-354.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1314.5" y="-354.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1033.5" y="-339.5" font-family="Times,serif" font-size="10.00"> updated_at </text>
<text text-anchor="start" x="1142.5" y="-339.5" font-family="Times,serif" font-size="10.00"> timestamp without time zone </text>
<text text-anchor="start" x="1270.5" y="-339.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1292.5" y="-339.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1314.5" y="-339.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1033.5" y="-324.5" font-family="Times,serif" font-size="10.00"> updated_by_account_id </text>
<text text-anchor="start" x="1142.5" y="-324.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="1270.5" y="-324.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1292.5" y="-324.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1314.5" y="-324.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1033.5" y="-309.5" font-family="Times,serif" font-size="10.00"> updated_by_user_id </text>
<text text-anchor="start" x="1142.5" y="-309.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="1270.5" y="-309.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1292.5" y="-309.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1314.5" y="-309.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1033.5" y="-294.5" font-family="Times,serif" font-size="10.00"> deleted_at </text>
<text text-anchor="start" x="1142.5" y="-294.5" font-family="Times,serif" font-size="10.00"> timestamp without time zone </text>
<text text-anchor="start" x="1270.5" y="-294.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1292.5" y="-294.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1314.5" y="-294.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1033.5" y="-279.5" font-family="Times,serif" font-size="10.00"> deleted_by_account_id </text>
<text text-anchor="start" x="1142.5" y="-279.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="1270.5" y="-279.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1292.5" y="-279.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1314.5" y="-279.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1033.5" y="-264.5" font-family="Times,serif" font-size="10.00"> deleted_by_user_id </text>
<text text-anchor="start" x="1142.5" y="-264.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="1270.5" y="-264.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1292.5" y="-264.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1314.5" y="-264.5" font-family="Times,serif" font-size="10.00"> </text>
<polygon fill="none" stroke="black" points="1023,-258.5 1023,-502.5 1320,-502.5 1320,-258.5 1023,-258.5"/>
</g>
<!-- chat&#45;&gt;project_version -->
<g id="edge1" class="edge">
<title>chat:rtcol4&#45;&gt;project_version:ltcol1</title>
<path fill="none" stroke="black" d="M802.5,-282.5C817.34,-282.5 973.34,-442.65 1014.65,-472.68"/>
<polygon fill="black" stroke="black" points="1013.04,-475.79 1023.5,-477.5 1016.39,-469.64 1013.04,-475.79"/>
<text text-anchor="middle" x="912.5" y="-460.3" font-family="Times,serif" font-size="14.00">chat_version_id_fkey</text>
</g>
<!-- chat_history -->
<g id="node3" class="node">
<title>chat_history</title>
<text text-anchor="start" x="11" y="-401.5" font-family="Times,serif" font-size="10.00"> </text>
<polygon fill="#e5e5e5" stroke="transparent" points="16,-395.5 16,-412.5 300,-412.5 300,-395.5 16,-395.5"/>
<polygon fill="none" stroke="black" points="16,-395.5 16,-412.5 300,-412.5 300,-395.5 16,-395.5"/>
<text text-anchor="start" x="130" y="-401.5" font-family="Times,serif" font-size="10.00"> chat_history </text>
<text text-anchor="start" x="18" y="-385.5" font-family="Times,serif" font-size="10.00"> id </text>
<text text-anchor="start" x="130" y="-385.5" font-family="Times,serif" font-size="10.00"> bigserial </text>
<text text-anchor="start" x="258" y="-385.5" font-family="Times,serif" font-size="10.00"> PK </text>
<text text-anchor="start" x="280" y="-385.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="302" y="-385.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="18" y="-370.5" font-family="Times,serif" font-size="10.00"> request </text>
<text text-anchor="start" x="130" y="-370.5" font-family="Times,serif" font-size="10.00"> character varying </text>
<text text-anchor="start" x="258" y="-370.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="280" y="-370.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="302" y="-370.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="18" y="-355.5" font-family="Times,serif" font-size="10.00"> response </text>
<text text-anchor="start" x="130" y="-355.5" font-family="Times,serif" font-size="10.00"> character varying </text>
<text text-anchor="start" x="258" y="-355.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="280" y="-355.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="302" y="-355.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="18" y="-340.5" font-family="Times,serif" font-size="10.00"> status </text>
<text text-anchor="start" x="130" y="-340.5" font-family="Times,serif" font-size="10.00"> character varying </text>
<text text-anchor="start" x="258" y="-340.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="280" y="-340.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="302" y="-340.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="18" y="-325.5" font-family="Times,serif" font-size="10.00"> chat_id </text>
<text text-anchor="start" x="130" y="-325.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="258" y="-325.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="280" y="-325.5" font-family="Times,serif" font-size="10.00"> FK </text>
<text text-anchor="start" x="302" y="-325.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="18" y="-310.5" font-family="Times,serif" font-size="10.00"> created_at </text>
<text text-anchor="start" x="130" y="-310.5" font-family="Times,serif" font-size="10.00"> timestamp without time zone </text>
<text text-anchor="start" x="258" y="-310.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="280" y="-310.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="302" y="-310.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="18" y="-295.5" font-family="Times,serif" font-size="10.00"> updated_at </text>
<text text-anchor="start" x="130" y="-295.5" font-family="Times,serif" font-size="10.00"> timestamp without time zone </text>
<text text-anchor="start" x="258" y="-295.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="280" y="-295.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="302" y="-295.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="18" y="-280.5" font-family="Times,serif" font-size="10.00"> used_completion_tokens </text>
<text text-anchor="start" x="130" y="-280.5" font-family="Times,serif" font-size="10.00"> integer </text>
<text text-anchor="start" x="258" y="-280.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="280" y="-280.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="302" y="-280.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="18" y="-265.5" font-family="Times,serif" font-size="10.00"> used_prompt_tokens </text>
<text text-anchor="start" x="130" y="-265.5" font-family="Times,serif" font-size="10.00"> integer </text>
<text text-anchor="start" x="258" y="-265.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="280" y="-265.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="302" y="-265.5" font-family="Times,serif" font-size="10.00"> </text>
<polygon fill="none" stroke="black" points="8,-259.5 8,-413.5 308,-413.5 308,-259.5 8,-259.5"/>
</g>
<!-- chat_history&#45;&gt;chat -->
<g id="edge2" class="edge">
<title>chat_history:rtcol5&#45;&gt;chat:ltcol1</title>
<path fill="none" stroke="black" d="M308,-327.5C392.18,-327.5 415.81,-327.5 495.02,-327.5"/>
<polygon fill="black" stroke="black" points="495.5,-331 505.5,-327.5 495.5,-324 495.5,-331"/>
<text text-anchor="middle" x="406.5" y="-332.3" font-family="Times,serif" font-size="14.00">chat_history_chat_id_fkey</text>
</g>
<!-- ingestion_job -->
<g id="node4" class="node">
<title>ingestion_job</title>
<text text-anchor="start" x="508.5" y="-730.5" font-family="Times,serif" font-size="10.00"> </text>
<polygon fill="#e5e5e5" stroke="transparent" points="513.5,-724.5 513.5,-741.5 794.5,-741.5 794.5,-724.5 513.5,-724.5"/>
<polygon fill="none" stroke="black" points="513.5,-724.5 513.5,-741.5 794.5,-741.5 794.5,-724.5 513.5,-724.5"/>
<text text-anchor="start" x="623" y="-730.5" font-family="Times,serif" font-size="10.00"> ingestion_job </text>
<text text-anchor="start" x="515.5" y="-714.5" font-family="Times,serif" font-size="10.00"> id </text>
<text text-anchor="start" x="624.5" y="-714.5" font-family="Times,serif" font-size="10.00"> bigserial </text>
<text text-anchor="start" x="752.5" y="-714.5" font-family="Times,serif" font-size="10.00"> PK </text>
<text text-anchor="start" x="774.5" y="-714.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-714.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-699.5" font-family="Times,serif" font-size="10.00"> reason </text>
<text text-anchor="start" x="624.5" y="-699.5" font-family="Times,serif" font-size="10.00"> character varying </text>
<text text-anchor="start" x="752.5" y="-699.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-699.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-699.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-684.5" font-family="Times,serif" font-size="10.00"> status </text>
<text text-anchor="start" x="624.5" y="-684.5" font-family="Times,serif" font-size="10.00"> character varying(255) </text>
<text text-anchor="start" x="752.5" y="-684.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-684.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-684.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-669.5" font-family="Times,serif" font-size="10.00"> version_id </text>
<text text-anchor="start" x="624.5" y="-669.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="752.5" y="-669.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-669.5" font-family="Times,serif" font-size="10.00"> FK </text>
<text text-anchor="start" x="796.5" y="-669.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-654.5" font-family="Times,serif" font-size="10.00"> created_at </text>
<text text-anchor="start" x="624.5" y="-654.5" font-family="Times,serif" font-size="10.00"> timestamp without time zone </text>
<text text-anchor="start" x="752.5" y="-654.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-654.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-654.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-639.5" font-family="Times,serif" font-size="10.00"> created_by_account_id </text>
<text text-anchor="start" x="624.5" y="-639.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="752.5" y="-639.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-639.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-639.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-624.5" font-family="Times,serif" font-size="10.00"> created_by_user_id </text>
<text text-anchor="start" x="624.5" y="-624.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="752.5" y="-624.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-624.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-624.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-609.5" font-family="Times,serif" font-size="10.00"> updated_at </text>
<text text-anchor="start" x="624.5" y="-609.5" font-family="Times,serif" font-size="10.00"> timestamp without time zone </text>
<text text-anchor="start" x="752.5" y="-609.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-609.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-609.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-594.5" font-family="Times,serif" font-size="10.00"> updated_by_account_id </text>
<text text-anchor="start" x="624.5" y="-594.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="752.5" y="-594.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-594.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-594.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-579.5" font-family="Times,serif" font-size="10.00"> updated_by_user_id </text>
<text text-anchor="start" x="624.5" y="-579.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="752.5" y="-579.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-579.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-579.5" font-family="Times,serif" font-size="10.00"> </text>
<polygon fill="none" stroke="black" points="505,-574 505,-743 802,-743 802,-574 505,-574"/>
</g>
<!-- ingestion_job&#45;&gt;project_version -->
<g id="edge3" class="edge">
<title>ingestion_job:rtcol4&#45;&gt;project_version:ltcol1</title>
<path fill="none" stroke="black" d="M802.5,-672.5C817.34,-672.5 973.34,-512.35 1014.65,-482.32"/>
<polygon fill="black" stroke="black" points="1016.39,-485.36 1023.5,-477.5 1013.04,-479.21 1016.39,-485.36"/>
<text text-anchor="middle" x="912.5" y="-655.3" font-family="Times,serif" font-size="14.00">ingestion_job_version_id_fkey</text>
</g>
<!-- project -->
<g id="node5" class="node">
<title>project</title>
<text text-anchor="start" x="1552" y="-460.5" font-family="Times,serif" font-size="10.00"> </text>
<polygon fill="#e5e5e5" stroke="transparent" points="1557,-454.5 1557,-471.5 1825,-471.5 1825,-454.5 1557,-454.5"/>
<polygon fill="none" stroke="black" points="1557,-454.5 1557,-471.5 1825,-471.5 1825,-454.5 1557,-454.5"/>
<text text-anchor="start" x="1674" y="-460.5" font-family="Times,serif" font-size="10.00"> project </text>
<text text-anchor="start" x="1559" y="-444.5" font-family="Times,serif" font-size="10.00"> id </text>
<text text-anchor="start" x="1668" y="-444.5" font-family="Times,serif" font-size="10.00"> bigserial </text>
<text text-anchor="start" x="1796" y="-444.5" font-family="Times,serif" font-size="10.00"> PK </text>
<text text-anchor="start" x="1818" y="-444.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1827" y="-444.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1559" y="-429.5" font-family="Times,serif" font-size="10.00"> repository_id </text>
<text text-anchor="start" x="1668" y="-429.5" font-family="Times,serif" font-size="10.00"> character varying(255) </text>
<text text-anchor="start" x="1796" y="-429.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1818" y="-429.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1827" y="-429.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1559" y="-414.5" font-family="Times,serif" font-size="10.00"> name </text>
<text text-anchor="start" x="1668" y="-414.5" font-family="Times,serif" font-size="10.00"> character varying(255) </text>
<text text-anchor="start" x="1796" y="-414.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1818" y="-414.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1827" y="-414.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1559" y="-399.5" font-family="Times,serif" font-size="10.00"> status </text>
<text text-anchor="start" x="1668" y="-399.5" font-family="Times,serif" font-size="10.00"> character varying(255) </text>
<text text-anchor="start" x="1796" y="-399.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1818" y="-399.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1827" y="-399.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1559" y="-384.5" font-family="Times,serif" font-size="10.00"> created_at </text>
<text text-anchor="start" x="1668" y="-384.5" font-family="Times,serif" font-size="10.00"> timestamp without time zone </text>
<text text-anchor="start" x="1796" y="-384.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1818" y="-384.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1827" y="-384.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1559" y="-369.5" font-family="Times,serif" font-size="10.00"> created_by_account_id </text>
<text text-anchor="start" x="1668" y="-369.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="1796" y="-369.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1818" y="-369.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1827" y="-369.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1559" y="-354.5" font-family="Times,serif" font-size="10.00"> created_by_user_id </text>
<text text-anchor="start" x="1668" y="-354.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="1796" y="-354.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1818" y="-354.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1827" y="-354.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1559" y="-339.5" font-family="Times,serif" font-size="10.00"> updated_at </text>
<text text-anchor="start" x="1668" y="-339.5" font-family="Times,serif" font-size="10.00"> timestamp without time zone </text>
<text text-anchor="start" x="1796" y="-339.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1818" y="-339.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1827" y="-339.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1559" y="-324.5" font-family="Times,serif" font-size="10.00"> updated_by_account_id </text>
<text text-anchor="start" x="1668" y="-324.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="1796" y="-324.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1818" y="-324.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1827" y="-324.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1559" y="-309.5" font-family="Times,serif" font-size="10.00"> updated_by_user_id </text>
<text text-anchor="start" x="1668" y="-309.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="1796" y="-309.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1818" y="-309.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1827" y="-309.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1559" y="-294.5" font-family="Times,serif" font-size="10.00"> deleted_at </text>
<text text-anchor="start" x="1668" y="-294.5" font-family="Times,serif" font-size="10.00"> timestamp without time zone </text>
<text text-anchor="start" x="1796" y="-294.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1818" y="-294.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1827" y="-294.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1559" y="-279.5" font-family="Times,serif" font-size="10.00"> deleted_by_account_id </text>
<text text-anchor="start" x="1668" y="-279.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="1796" y="-279.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1818" y="-279.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1827" y="-279.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="1559" y="-264.5" font-family="Times,serif" font-size="10.00"> deleted_by_user_id </text>
<text text-anchor="start" x="1668" y="-264.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="1796" y="-264.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1818" y="-264.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="1827" y="-264.5" font-family="Times,serif" font-size="10.00"> </text>
<polygon fill="none" stroke="black" points="1549,-258.5 1549,-472.5 1833,-472.5 1833,-258.5 1549,-258.5"/>
</g>
<!-- project_file -->
<g id="node6" class="node">
<title>project_file</title>
<text text-anchor="start" x="508.5" y="-535.5" font-family="Times,serif" font-size="10.00"> </text>
<polygon fill="#e5e5e5" stroke="transparent" points="513.5,-529.5 513.5,-546.5 794.5,-546.5 794.5,-529.5 513.5,-529.5"/>
<polygon fill="none" stroke="black" points="513.5,-529.5 513.5,-546.5 794.5,-546.5 794.5,-529.5 513.5,-529.5"/>
<text text-anchor="start" x="628" y="-535.5" font-family="Times,serif" font-size="10.00"> project_file </text>
<text text-anchor="start" x="515.5" y="-519.5" font-family="Times,serif" font-size="10.00"> id </text>
<text text-anchor="start" x="624.5" y="-519.5" font-family="Times,serif" font-size="10.00"> bigserial </text>
<text text-anchor="start" x="752.5" y="-519.5" font-family="Times,serif" font-size="10.00"> PK </text>
<text text-anchor="start" x="774.5" y="-519.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-519.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-504.5" font-family="Times,serif" font-size="10.00"> relative_path </text>
<text text-anchor="start" x="624.5" y="-504.5" font-family="Times,serif" font-size="10.00"> character varying </text>
<text text-anchor="start" x="752.5" y="-504.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-504.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-504.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-489.5" font-family="Times,serif" font-size="10.00"> status </text>
<text text-anchor="start" x="624.5" y="-489.5" font-family="Times,serif" font-size="10.00"> character varying(255) </text>
<text text-anchor="start" x="752.5" y="-489.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-489.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-489.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-474.5" font-family="Times,serif" font-size="10.00"> version_id </text>
<text text-anchor="start" x="624.5" y="-474.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="752.5" y="-474.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-474.5" font-family="Times,serif" font-size="10.00"> FK </text>
<text text-anchor="start" x="796.5" y="-474.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-459.5" font-family="Times,serif" font-size="10.00"> created_at </text>
<text text-anchor="start" x="624.5" y="-459.5" font-family="Times,serif" font-size="10.00"> timestamp without time zone </text>
<text text-anchor="start" x="752.5" y="-459.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-459.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-459.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-444.5" font-family="Times,serif" font-size="10.00"> created_by_account_id </text>
<text text-anchor="start" x="624.5" y="-444.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="752.5" y="-444.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-444.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-444.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-429.5" font-family="Times,serif" font-size="10.00"> created_by_user_id </text>
<text text-anchor="start" x="624.5" y="-429.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="752.5" y="-429.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-429.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-429.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-414.5" font-family="Times,serif" font-size="10.00"> updated_at </text>
<text text-anchor="start" x="624.5" y="-414.5" font-family="Times,serif" font-size="10.00"> timestamp without time zone </text>
<text text-anchor="start" x="752.5" y="-414.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-414.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-414.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-399.5" font-family="Times,serif" font-size="10.00"> updated_by_account_id </text>
<text text-anchor="start" x="624.5" y="-399.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="752.5" y="-399.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-399.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-399.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="515.5" y="-384.5" font-family="Times,serif" font-size="10.00"> updated_by_user_id </text>
<text text-anchor="start" x="624.5" y="-384.5" font-family="Times,serif" font-size="10.00"> bigint </text>
<text text-anchor="start" x="752.5" y="-384.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="774.5" y="-384.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="796.5" y="-384.5" font-family="Times,serif" font-size="10.00"> </text>
<polygon fill="none" stroke="black" points="505,-379 505,-548 802,-548 802,-379 505,-379"/>
</g>
<!-- project_file&#45;&gt;project_version -->
<g id="edge4" class="edge">
<title>project_file:rtcol4&#45;&gt;project_version:ltcol1</title>
<path fill="none" stroke="black" d="M802.5,-477.5C897.27,-477.5 923.46,-477.5 1013.41,-477.5"/>
<polygon fill="black" stroke="black" points="1013.5,-481 1023.5,-477.5 1013.5,-474 1013.5,-481"/>
<text text-anchor="middle" x="912.5" y="-481.3" font-family="Times,serif" font-size="14.00">project_file_version_id_fkey</text>
</g>
<!-- project_version&#45;&gt;project -->
<g id="edge5" class="edge">
<title>project_version:rtcol3&#45;&gt;project:ltcol1</title>
<path fill="none" stroke="black" d="M1320.5,-447.5C1418.58,-447.5 1445.62,-447.5 1538.85,-447.5"/>
<polygon fill="black" stroke="black" points="1539,-451 1549,-447.5 1539,-444 1539,-451"/>
<text text-anchor="middle" x="1434.5" y="-451.3" font-family="Times,serif" font-size="14.00">project_version_project_id_fkey</text>
</g>
<!-- schema_version -->
<g id="node8" class="node">
<title>schema_version</title>
<text text-anchor="start" x="37" y="-596.5" font-family="Times,serif" font-size="10.00"> </text>
<polygon fill="#e5e5e5" stroke="transparent" points="42,-590.5 42,-607.5 274,-607.5 274,-590.5 42,-590.5"/>
<polygon fill="none" stroke="black" points="42,-590.5 42,-607.5 274,-607.5 274,-590.5 42,-590.5"/>
<text text-anchor="start" x="123" y="-596.5" font-family="Times,serif" font-size="10.00"> schema_version </text>
<text text-anchor="start" x="44" y="-580.5" font-family="Times,serif" font-size="10.00"> installed_rank </text>
<text text-anchor="start" x="117" y="-580.5" font-family="Times,serif" font-size="10.00"> integer </text>
<text text-anchor="start" x="245" y="-580.5" font-family="Times,serif" font-size="10.00"> PK </text>
<text text-anchor="start" x="267" y="-580.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="276" y="-580.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="44" y="-565.5" font-family="Times,serif" font-size="10.00"> version </text>
<text text-anchor="start" x="117" y="-565.5" font-family="Times,serif" font-size="10.00"> character varying(50) </text>
<text text-anchor="start" x="245" y="-565.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="267" y="-565.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="276" y="-565.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="44" y="-550.5" font-family="Times,serif" font-size="10.00"> description </text>
<text text-anchor="start" x="117" y="-550.5" font-family="Times,serif" font-size="10.00"> character varying(200) </text>
<text text-anchor="start" x="245" y="-550.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="267" y="-550.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="276" y="-550.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="44" y="-535.5" font-family="Times,serif" font-size="10.00"> type </text>
<text text-anchor="start" x="117" y="-535.5" font-family="Times,serif" font-size="10.00"> character varying(20) </text>
<text text-anchor="start" x="245" y="-535.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="267" y="-535.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="276" y="-535.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="44" y="-520.5" font-family="Times,serif" font-size="10.00"> script </text>
<text text-anchor="start" x="117" y="-520.5" font-family="Times,serif" font-size="10.00"> character varying(1000) </text>
<text text-anchor="start" x="245" y="-520.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="267" y="-520.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="276" y="-520.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="44" y="-505.5" font-family="Times,serif" font-size="10.00"> checksum </text>
<text text-anchor="start" x="117" y="-505.5" font-family="Times,serif" font-size="10.00"> integer </text>
<text text-anchor="start" x="245" y="-505.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="267" y="-505.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="276" y="-505.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="44" y="-490.5" font-family="Times,serif" font-size="10.00"> installed_by </text>
<text text-anchor="start" x="117" y="-490.5" font-family="Times,serif" font-size="10.00"> character varying(100) </text>
<text text-anchor="start" x="245" y="-490.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="267" y="-490.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="276" y="-490.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="44" y="-475.5" font-family="Times,serif" font-size="10.00"> installed_on </text>
<text text-anchor="start" x="117" y="-475.5" font-family="Times,serif" font-size="10.00"> timestamp without time zone </text>
<text text-anchor="start" x="245" y="-475.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="267" y="-475.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="276" y="-475.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="44" y="-460.5" font-family="Times,serif" font-size="10.00"> execution_time </text>
<text text-anchor="start" x="117" y="-460.5" font-family="Times,serif" font-size="10.00"> integer </text>
<text text-anchor="start" x="245" y="-460.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="267" y="-460.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="276" y="-460.5" font-family="Times,serif" font-size="10.00"> </text>
<text text-anchor="start" x="44" y="-445.5" font-family="Times,serif" font-size="10.00"> success </text>
<text text-anchor="start" x="117" y="-445.5" font-family="Times,serif" font-size="10.00"> boolean </text>
<text text-anchor="start" x="245" y="-445.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="267" y="-445.5" font-family="Times,serif" font-size="10.00"> &#160;</text>
<text text-anchor="start" x="276" y="-445.5" font-family="Times,serif" font-size="10.00"> </text>
<polygon fill="none" stroke="black" points="34,-440 34,-609 282,-609 282,-440 34,-440"/>
</g>
</g>
</svg>
