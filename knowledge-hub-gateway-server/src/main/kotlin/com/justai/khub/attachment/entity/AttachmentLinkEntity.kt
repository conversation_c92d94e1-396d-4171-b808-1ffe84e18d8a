package com.justai.khub.attachment.entity

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import java.time.LocalDateTime

@Entity
@Table(name = "attachment_link")
class AttachmentLinkEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = -1

    @ManyToOne
    lateinit var attachment: AttachmentEntity

    @Column
    lateinit var publicId: String

    @Column
    lateinit var expiredAt: LocalDateTime

    fun isExpired(): Boolean {
        return expiredAt.isBefore(LocalDateTime.now())
    }
}
