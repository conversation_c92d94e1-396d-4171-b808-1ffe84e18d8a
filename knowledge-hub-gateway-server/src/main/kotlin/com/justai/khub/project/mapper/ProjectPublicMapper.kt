package com.justai.khub.project.mapper

import com.justai.khub.api.public.model.*
import com.justai.khub.integration.dto.CloudConfluenceIntegrationSettings
import com.justai.khub.integration.dto.ConfluenceIntegrationSettings
import com.justai.khub.integration.dto.MinervaIntegrationSettings
import com.justai.khub.integration.entity.IntegrationEntity
import org.springframework.data.domain.Page

object ProjectPublicMapper {

    fun toPublicResponse(page: Page<IntegrationEntity>): ProjectIntegrations {
        return ProjectIntegrations(integrations = page.content.map { toPublicResponse(it) })
    }

    fun toPublicResponse(integration: IntegrationEntity): IntegrationDTO {
        return IntegrationDTO(
            id = integration.id,
            name = integration.name,
            autoSync = integration.autoSync,
            syncIntervalMinutes = integration.checkIntervalMinutes,
            settings = toResponseSettings(integration.settings),
            lastError = integration.lastError,
            status = integration.status.asPublicStatus()
        )
    }

    private fun toResponseSettings(settings: com.justai.khub.integration.dto.IntegrationSettings): IntegrationSettings {
        return when (settings) {
            is ConfluenceIntegrationSettings -> ConfluenceSettings(
                baseUrl = settings.baseUrl.trim().removeSuffix("/"),
                token = settings.token,
                space = settings.space,
            )
            is CloudConfluenceIntegrationSettings -> CloudConfluenceSettings(
                baseUrl = settings.baseUrl.trim().removeSuffix("/"),
                space = settings.space,
                siteUrl = settings.siteUrl,
                authBaseUrl = settings.authBaseUrl.trim().removeSuffix("/"),
            )
            is MinervaIntegrationSettings -> MinervaSettings(
                baseUrl = settings.baseUrl.trim().removeSuffix("/"),
                token = settings.token,
                spaceId = settings.spaceId,
                segment = settings.segment,
            )
        }
    }

    fun toInternalCreateIntegrationRequest(request: IntegrationCreateRequest): com.justai.khub.api.fe.model.IntegrationDTO {
        return com.justai.khub.api.fe.model.IntegrationDTO(
            name = request.name,
            autoIngest = true,
            autoSync = request.autoSync,
            checkIntervalMinutes = request.syncIntervalMinutes,
            type = toInternalType(request.settings),
            settings = toInternalSettings(request.settings)
        )
    }

    private fun toInternalType(settings: IntegrationSettings): com.justai.khub.api.fe.model.IntegrationDTO.Type {
        return when (settings) {
            is ConfluenceSettings -> com.justai.khub.api.fe.model.IntegrationDTO.Type.CONFLUENCE
            is CloudConfluenceSettings -> com.justai.khub.api.fe.model.IntegrationDTO.Type.CLOUD_CONFLUENCE
            is MinervaSettings -> com.justai.khub.api.fe.model.IntegrationDTO.Type.MINERVA
        }
    }

    private fun toInternalSettings(settings: IntegrationSettings): com.justai.khub.integration.dto.IntegrationSettings {
        return when (settings) {
            is ConfluenceSettings -> ConfluenceIntegrationSettings(
                baseUrl = settings.baseUrl,
                space = settings.space,
                token = settings.token,
            )
            is CloudConfluenceSettings -> CloudConfluenceIntegrationSettings(
                space = settings.space,
                siteUrl = settings.siteUrl,
            )
            is MinervaSettings -> MinervaIntegrationSettings(
                baseUrl = settings.baseUrl,
                token = settings.token,
                spaceId = settings.spaceId,
                segment = settings.segment,
            )
        }
    }

    private fun com.justai.khub.integration.enumeration.IntegrationStatus.asPublicStatus(): IntegrationStatus {
        return when (this) {
            com.justai.khub.integration.enumeration.IntegrationStatus.ACTIVE -> IntegrationStatus.ACTIVE
            com.justai.khub.integration.enumeration.IntegrationStatus.CHECKING -> IntegrationStatus.CHECKING
            com.justai.khub.integration.enumeration.IntegrationStatus.AWAITING_ACTION -> IntegrationStatus.AWAITING_ACTION
            com.justai.khub.integration.enumeration.IntegrationStatus.DISABLED -> IntegrationStatus.DISABLED
            com.justai.khub.integration.enumeration.IntegrationStatus.FAILED -> IntegrationStatus.FAILED
        }
    }
}
