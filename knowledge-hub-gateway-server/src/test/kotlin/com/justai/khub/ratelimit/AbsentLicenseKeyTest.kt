package com.justai.khub.ratelimit

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.test.context.TestPropertySource
import org.springframework.test.web.servlet.result.MockMvcResultMatchers

@TestPropertySource(properties = ["test-license-key.enabled=false"])
@Disabled("TODO fix and restore")
class AbsentLicenseKeyTest : BaseLicenseTest() {

    @Test
    fun testErrorReturnedForBillableRequestsIfLicenseKeyIsMissing() {
        billableEndpoints.forEach { endpoint ->
            endpoint().andExpect(MockMvcResultMatchers.status().isPaymentRequired)
        }
    }
}
