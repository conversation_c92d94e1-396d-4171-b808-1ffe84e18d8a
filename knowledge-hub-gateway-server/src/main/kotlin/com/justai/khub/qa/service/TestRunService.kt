package com.justai.khub.qa.service

import com.justai.khub.common.dto.rag.QueryResponse
import com.justai.khub.qa.entity.TestEntity
import com.justai.khub.qa.entity.TestRunEntity
import com.justai.khub.qa.entity.TestSetRunEntity
import com.justai.khub.qa.mapper.QAMapper
import com.justai.khub.qa.repository.TestRunRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class TestRunService(
    private val qaMapper: QAMapper,
    private val testRunRepository: TestRunRepository,
) {

    @Transactional(readOnly = true)
    fun findTestIds(testSetRunId: Long): Set<Long> {
        return testRunRepository.findTestIdsByTestSetRunId(testSetRunId).toSet()
    }

    @Transactional(rollbackFor = [Exception::class])
    fun createSuccessfulTest(
        testSetRun: TestSetRunEntity,
        test: TestEntity,
        runResult: QueryResponse,
        runDuration: Long
    ): TestRunEntity {
        val newTestRun = qaMapper.initSuccessfulTestRun(testSetRun, test, runResult, runDuration)
        return testRunRepository.saveAndFlush(newTestRun)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun createFailedTest(
        testSetRun: TestSetRunEntity,
        test: TestEntity,
        errorCode: String,
        runDuration: Long
    ): TestRunEntity {
        val testRun = qaMapper.initFailedTest(testSetRun, test, errorCode, runDuration)
        return testRunRepository.saveAndFlush(testRun)
    }

    @Transactional(readOnly = true)
    fun getTestRuns(testSetRunId: Long): List<TestRunEntity> {
        return testRunRepository.findByTestSetRunId(testSetRunId).sortedBy { it.test.id }
    }
}
