package com.justai.khub.common.dto.rag

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.justai.khub.qa.dto.GeneratedTest
import com.justai.khub.qa.dto.TokensUsage

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class TestGenerateResponse(
    val tests: List<GeneratedTest>,
    val usage: TokensUsage? = null,
    val error: String? = null
)
