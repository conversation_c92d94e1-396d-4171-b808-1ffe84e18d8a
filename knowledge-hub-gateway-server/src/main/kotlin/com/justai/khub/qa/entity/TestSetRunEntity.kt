package com.justai.khub.qa.entity

import com.justai.khub.project.dto.SearchPipelineConfig
import com.justai.khub.qa.enumeration.TestSetRunStatus
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.*
import org.hibernate.annotations.Type
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime


@Entity
@Table(name = "test_set_run")
@EntityListeners(AuditingEntityListener::class)
class TestSetRunEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = -1

    @ManyToOne
    @JoinColumn(name = "test_set_id", nullable = false)
    lateinit var testSet: TestSetEntity

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    lateinit var status: TestSetRunStatus

    @Column(nullable = false)
    var autoEvaluate: Boolean = true

    @Column
    lateinit var evaluationModel: String

    @Column
    var meanScore: Double? = null

    @Column(nullable = false)
    var finishedTests: Int = 0

    @Column
    var processingTimeMs: Long? = null

    @Type(JsonType::class)
    @Column(columnDefinition = "jsonb")
    var searchSettings: SearchPipelineConfig? = null

    @Column(nullable = false)
    var usedCompletionTokens: Int = 0

    @Column(nullable = false)
    var usedPromptTokens: Int = 0

    @Column(nullable = false)
    @CreatedDate
    lateinit var createdAt: LocalDateTime

    @Column(nullable = false)
    @LastModifiedDate
    lateinit var updatedAt: LocalDateTime

    @Column(name = "last_error")
    var lastError: String? = null
}
