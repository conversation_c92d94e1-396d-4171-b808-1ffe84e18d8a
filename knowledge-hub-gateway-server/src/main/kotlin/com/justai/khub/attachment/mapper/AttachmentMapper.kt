package com.justai.khub.attachment.mapper

import com.justai.khub.attachment.entity.AttachmentEntity
import com.justai.khub.attachment.entity.AttachmentLinkEntity
import com.justai.khub.project.entity.ProjectFileEntity
import java.time.Duration
import java.time.LocalDateTime
import java.util.*

object AttachmentMapper {
    fun toAttachmentEntity(file: ProjectFileEntity, externalAttachmentId: String): AttachmentEntity {
        return AttachmentEntity().also {
            it.file = file
            it.externalId = externalAttachmentId
        }
    }

    fun toAttachmentLinkEntity(
        attachment: AttachmentEntity,
        attachmentExtension: String,
        availabilityDuration: Duration
    ): AttachmentLinkEntity {
        return AttachmentLinkEntity().also {
            it.attachment = attachment
            it.publicId = "${UUID.randomUUID()}$attachmentExtension"
            it.expiredAt = LocalDateTime.now().plus(availabilityDuration)
        }
    }
}
