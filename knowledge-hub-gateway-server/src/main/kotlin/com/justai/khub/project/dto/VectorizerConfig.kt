package com.justai.khub.project.dto

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
enum class DistanceStrategy {
    cosine, dot_product
}

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class VectorizerConfig(
    val model: String? = null,
    val embeddingsDimension: Int? = null,
    val maxSequenceLength: Int? = null,
    val distanceStrategy: DistanceStrategy? = null,
)
