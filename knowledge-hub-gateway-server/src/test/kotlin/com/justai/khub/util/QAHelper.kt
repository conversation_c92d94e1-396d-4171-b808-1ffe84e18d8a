package com.justai.khub.util

import com.justai.khub.api.fe.model.TestSetDTO
import com.justai.khub.api.fe.model.TestSetRunDTO
import com.justai.khub.api.fe.model.TestSetRunRequest
import com.justai.khub.common.util.JSON
import com.justai.khub.util.WebUtils.parseTo
import com.justai.security.session.auth.JustSessionAuthentication
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.mock.web.MockMultipartFile
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors
import org.springframework.stereotype.Component
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.ResultActions
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@Component
class QAHelper {

    @Autowired
    protected lateinit var mvc: MockMvc

    @Autowired
    protected lateinit var defaultAuth: JustSessionAuthentication

    fun uploadTestSetAndExpectOk(
        projectId: Long,
        testSetName: String = "testSet.xlsx",
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): TestSetDTO {
        return uploadTestSet(projectId, testSetName, ccAuth, apiKey).andExpect(status().isOk).andReturn().parseTo<TestSetDTO>()
    }

    fun uploadTestSet(
        projectId: Long,
        testSetName: String = "testSet.xlsx",
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ResultActions {
        val file = TestDataFactory.testFile(testSetName)
        val mockFile = MockMultipartFile(
            "file",
            testSetName,
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            file.inputStream
        )
        return mvc.perform(
            multipart("/api/khub/projects/$projectId/testSet/upload")
                .file(mockFile)
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
    }

    fun getTestSetAndExpectOk(
        projectId: Long,
        testSetId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): TestSetDTO {
        return getTestSet(projectId, testSetId).andExpect(status().isOk).andReturn().parseTo<TestSetDTO>()
    }

    fun getTestSet(
        projectId: Long,
        testSetId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ResultActions {
        return mvc.perform(
            get("/api/khub/projects/${projectId}/testSet/${testSetId}")
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
    }

    fun runTestSetAndExpectOk(
        projectId: Long,
        testSetId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): TestSetRunDTO {
        return runTestSet(projectId, testSetId, ccAuth, apiKey).andExpect(status().isOk).andReturn().parseTo<TestSetRunDTO>()
    }

    fun runTestSet(
        projectId: Long,
        testSetId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ResultActions {
        val request = TestSetRunRequest()
        return mvc.perform(
            post("/api/khub/projects/${projectId}/testSet/${testSetId}/run")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
    }

}
