package com.justai.khub.qa.entity

import jakarta.persistence.*
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

@Entity
@Table(name = "test_evaluation")
@EntityListeners(AuditingEntityListener::class)
class TestEvaluationEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = -1

    @ManyToOne
    @JoinColumn(name = "test_run_id", nullable = false)
    lateinit var testRun: TestRunEntity

    @Column
    var comment: String? = null

    @Column
    var lastError: String? = null

    @Column
    var score: Double? = null

    @Column(nullable = false)
    var usedCompletionTokens: Int = 0

    @Column(nullable = false)
    var usedPromptTokens: Int = 0

    @Column(nullable = false)
    @CreatedDate
    lateinit var createdAt: LocalDateTime

    @Column(nullable = false)
    @LastModifiedDate
    lateinit var updatedAt: LocalDateTime
}
