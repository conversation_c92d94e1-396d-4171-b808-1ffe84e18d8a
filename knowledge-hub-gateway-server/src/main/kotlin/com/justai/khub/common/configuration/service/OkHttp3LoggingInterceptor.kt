package com.justai.khub.common.configuration.service

import com.justai.khub.common.configuration.dto.LoggingProperties
import com.justai.khub.common.enumeration.IntegrationName
import okhttp3.Interceptor
import okhttp3.Response
import okhttp3.ResponseBody.Companion.toResponseBody
import org.slf4j.LoggerFactory
import kotlin.math.min

class OkHttp3LoggingInterceptor(
    private val integrationName: IntegrationName,
    private val loggingProperties: LoggingProperties
) : Interceptor {
    private val log = LoggerFactory.getLogger(this::class.java)

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        var requestLog = "Request to $integrationName: ${request.method} ${request.url}"

        // Log request body if it is JSON and not empty
        if (request.body?.contentType()?.subtype == "json") {
            val buffer = okio.Buffer()
            request.body!!.writeTo(buffer)
            val bodyString = buffer.readUtf8()
            if (bodyString.isNotEmpty()) {
                requestLog += "\n\tRequest body: ${bodyString.substring(0, min(bodyString.length, loggingProperties.loggableBodySize))}"
            }
        }
        log.info(requestLog)

        val startedAt = System.currentTimeMillis()
        val response: Response = chain.proceed(request)
        val processingTime = System.currentTimeMillis() - startedAt

        // Log response details
        var responseLog = "Response from ${integrationName}: ${request.method} ${request.url}" +
            "\n\tstatus = ${response.code} ${response.message}" +
            "\n\tprocessing time = ${processingTime}ms"

        // Log response body if it is JSON and not empty
        var responseBody = response.body
        if (responseBody?.contentType()?.subtype == "json") {
            val responseBodyString = responseBody.string()
            responseLog += "\n\tResponse body: ${responseBodyString.substring(0, min(responseBodyString.length, loggingProperties.loggableBodySize))}"
            responseBody = responseBodyString.toResponseBody(responseBody.contentType())
        }
        log.info(responseLog)

        // Recreate the response body before returning because response.body.string() consumes the original response
        return response.newBuilder().body(responseBody).build()
    }
}
