package com.justai.khub.qa.repository

import com.justai.khub.qa.entity.TestSetScheduleEntity
import jakarta.persistence.QueryHint
import org.hibernate.jpa.HibernateHints.HINT_NATIVE_LOCK_MODE
import org.springframework.data.jpa.repository.EntityGraph
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.jpa.repository.QueryHints
import org.springframework.stereotype.Repository
import java.time.LocalDateTime
import java.time.ZonedDateTime

@Repository
interface TestSetScheduleRepository : JpaRepository<TestSetScheduleEntity, Long> {

    fun findByTestSetId(testSetId: Long): TestSetScheduleEntity?

    fun deleteByTestSetId(testSetId: Long)

    @EntityGraph(attributePaths = ["testSet", "testSet.project"])
    @Query("SELECT tss FROM TestSetScheduleEntity tss WHERE tss.scheduledAt <= :now AND tss.testSet.project.deletedAt IS NULL")
    fun findScheduledTasksForActiveProjects(now: Long): List<TestSetScheduleEntity>

    // DO NOT use @Lock(LockModeType.PESSIMISTIC_WRITE) here, as it would override any lock mode specified by query hints
    @QueryHints(value = [QueryHint(name = HINT_NATIVE_LOCK_MODE, value = "upgrade-skiplocked")])
    @Query("SELECT tsse FROM TestSetScheduleEntity tsse WHERE tsse.id = :id")
    fun findForUpdateSkipLocked(id: Long): TestSetScheduleEntity?

    @Modifying
    @Query("DELETE FROM TestSetScheduleEntity tss WHERE tss.testSet.project.id = :projectId")
    fun deleteByProjectId(projectId: Long): Int
}
