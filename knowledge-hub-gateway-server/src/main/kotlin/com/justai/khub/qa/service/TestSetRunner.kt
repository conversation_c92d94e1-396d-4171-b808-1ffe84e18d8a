package com.justai.khub.qa.service

import com.justai.khub.common.configuration.dto.KHubUser
import com.justai.khub.common.connector.EvaluationServiceConnector
import com.justai.khub.common.connector.RagServiceConnector
import com.justai.khub.common.dto.rag.QueryResponse
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.service.MetricsService
import com.justai.khub.common.service.PromptsService
import com.justai.khub.common.util.CommonUtils.extractErrorCode
import com.justai.khub.common.util.LoggingUtils
import com.justai.khub.project.entity.ProjectVersionEntity
import com.justai.khub.project.service.ProjectVersionService
import com.justai.khub.qa.entity.TestEntity
import com.justai.khub.qa.entity.TestEvaluationEntity
import com.justai.khub.qa.entity.TestSetRunEntity
import com.justai.khub.qa.mapper.QAMapper
import org.slf4j.LoggerFactory
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Service
import org.springframework.transaction.support.TransactionTemplate
import java.util.*
import java.util.function.BooleanSupplier
import kotlin.time.measureTimedValue

@Service
class TestSetRunner(
    private val projectVersionService: ProjectVersionService,
    private val testService: TestService,
    private val testSetRunService: TestSetRunService,
    private val testEvaluationService: TestEvaluationService,
    private val testRunService: TestRunService,
    private val metricsService: MetricsService,
    private val ragServiceConnector: RagServiceConnector,
    private val evaluationServiceConnector: EvaluationServiceConnector,
    private val transactionTemplate: TransactionTemplate,
    private val promptsService: PromptsService,
    private val qaMapper: QAMapper
) {

    private val log = LoggerFactory.getLogger(this::class.java)

    fun runTestSetInBackground(testSetRun: TestSetRunEntity) {
        var lockedTestSetRun = testSetRun
        try {
            SecurityContextHolder.getContext().authentication = KHubUser(accountId = lockedTestSetRun.testSet.createdBy.accountId)
            LoggingUtils.addToMDC(
                accountId = lockedTestSetRun.testSet.createdBy.accountId,
                requestId = UUID.randomUUID().toString()
            )
            val updated = testSetRunService.startTestSetRun(lockedTestSetRun)
            if (!updated) {
                log.info("Skip test set run: {}: already started or cancelled", lockedTestSetRun.id)
                return
            }
            lockedTestSetRun = testSetRunService.findTestSetRun(lockedTestSetRun.id, lockedTestSetRun.testSet.createdBy.accountId)
            log.info("Started test run: {}", lockedTestSetRun.id)
            val (isFullyGenerated, duration) = measureTimedValue {
                metricsService.testSetRunTimer().record(BooleanSupplier { runTestSet(lockedTestSetRun) })
            }
            if (isFullyGenerated) {
                testSetRunService.finishTestSetRun(lockedTestSetRun, duration.inWholeMilliseconds)
                log.info("Finished test set run: {}. processing time = {}ms", lockedTestSetRun.id, duration.inWholeMilliseconds)
            } else {
                log.info("Cancelled test set run: {}", lockedTestSetRun.id)
            }

        } catch (ex: Exception) {
            handleTestSetRunException(lockedTestSetRun, ex)
        } finally {
            testSetRunService.updateMeanScore(testSetRun)
            SecurityContextHolder.clearContext()
            LoggingUtils.clearAll()
        }
    }

    private fun runTestSet(testSetRun: TestSetRunEntity): Boolean {
        val defaultVersion = projectVersionService.getDefaultVersion(testSetRun.testSet.project.id)
        testSetRunService.ensureSearchSettingsSaved(testSetRun, defaultVersion)
        val alreadyFinishedTestIds = testRunService.findTestIds(testSetRun.id)
        val testsToRun = testService.findAllByTestSetId(testSetRun.testSet.id)
            .filterNot { alreadyFinishedTestIds.contains(it.id) }

        // TODO run in parallel in separate thread pool
        testsToRun.forEach {
            runTest(testSetRun, defaultVersion, it)
            if (!testSetRunService.isActive(testSetRun.id)) return false
        }
        return true
    }

    private fun runTest(testSetRun: TestSetRunEntity, version: ProjectVersionEntity, test: TestEntity) {
        val startedAt = System.currentTimeMillis()
        try {
            val response = ragServiceConnector.query(
                test.question,
                test.segment,
                test.conversationId?.let { "test-${testSetRun.id}-${it}" } ?: "test-${UUID.randomUUID()}",
                testSetRun.searchSettings!!,
                version.ingestSettings,
            )
            val evaluation = evaluateIfNeed(testSetRun, test, response)
            transactionTemplate.execute {
                val testRun = testRunService.createSuccessfulTest(
                    testSetRun,
                    test,
                    response,
                    System.currentTimeMillis() - startedAt,
                )
                evaluation?.let {
                    it.testRun = testRun
                    testEvaluationService.createEvaluation(it)
                }
                val usedPromptTokens = (response.promptTokens ?: 0) + (evaluation?.usedPromptTokens ?: 0)
                val usedCompletionTokens = (response.completionTokens ?: 0) + (evaluation?.usedCompletionTokens ?: 0)
                testSetRunService.addFinishedTestsAndUsage(testSetRun, 1, usedPromptTokens, usedCompletionTokens)
            }

        } catch (ex: Exception) {
            val errorCode = ex.extractErrorCode(ApiErrorCode.TEST_RUN_ERROR.code)
            testRunService.createFailedTest(testSetRun, test, errorCode, System.currentTimeMillis() - startedAt)
            log.warn("Failed to run test {}", test.id, ex)
        }
    }

    private fun evaluateIfNeed(testSetRun: TestSetRunEntity, test: TestEntity, testResult: QueryResponse): TestEvaluationEntity? {
        if (!testSetRun.autoEvaluate) {
            return null
        }
        try {
            val evaluationModel = testSetRun.evaluationModel
            val evaluationResult = evaluationServiceConnector.evaluateAnswer(
                test = test,
                actualAnswer = testResult.answer,
                usedChunks = testResult.usedChunks,
                llm = qaMapper.toLLMConfig(evaluationModel),
                prompt = testSetRun.testSet.properties.evaluationPrompt ?: promptsService.testEvaluationPrompt()
            )
            return qaMapper.initLLMTestEvaluation(evaluationResult)
        } catch (ex: Exception) {
            val errorCode = ex.extractErrorCode(ApiErrorCode.TEST_EVALUATION_ERROR.code)
            log.warn("Failed to evaluate test {}", test.id, ex)
            return qaMapper.initFailedLLMTestEvaluation(errorCode)
        }
    }

    private fun handleTestSetRunException(testSetRun: TestSetRunEntity, ex: Exception) {
        log.error("Error running test set {}", testSetRun.testSet.id, ex)
        val errorCode = ex.extractErrorCode(ApiErrorCode.TEST_RUN_ERROR.code)
        metricsService.incrementTestSetRunErrorsCounter()
        testSetRunService.failTestSetRun(testSetRun, errorCode)
    }
}
