package com.justai.khub.common.configuration

import com.google.common.util.concurrent.ThreadFactoryBuilder
import com.justai.khub.common.configuration.dto.IngestProperties
import com.justai.khub.common.configuration.dto.IntegrationProperties
import com.justai.khub.common.configuration.dto.QAProperties
import com.justai.khub.common.configuration.dto.RagProperties
import com.justai.khub.common.service.MetricsService
import io.micrometer.core.instrument.binder.jvm.ExecutorServiceMetrics
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.scheduling.annotation.EnableScheduling
import java.util.concurrent.ExecutorService
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit

@Configuration
@EnableScheduling
class SchedulingConfiguration {

    @Bean
    @ConditionalOnProperty(value = ["scheduling.rag.enabled"], havingValue = "true", matchIfMissing = true)
    fun chatQueryProcessingThreadPool(properties: RagProperties): ThreadPoolExecutor {
        return ThreadPoolExecutor(
            properties.chatQueryProcessingJobThreadPoolSize,
            properties.chatQueryProcessingJobThreadPoolSize,
            0,
            TimeUnit.MILLISECONDS,
            LinkedBlockingQueue(),
            ThreadFactoryBuilder().setNameFormat("chat-query-processor-%d").build()
        )
    }

    @Bean
    @ConditionalOnProperty(value = ["scheduling.rag.enabled"], havingValue = "true", matchIfMissing = true)
    fun chatQueryProcessingExecutor(chatQueryProcessingThreadPool: ThreadPoolExecutor, metricsService: MetricsService): ExecutorService {
        return ExecutorServiceMetrics.monitor(
            metricsService.meterRegistry,
            chatQueryProcessingThreadPool,
            "chat-query-processing-executor"
        )
    }

    @Bean
    @ConditionalOnProperty(value = ["scheduling.ingest.enabled"], havingValue = "true", matchIfMissing = true)
    fun ingestProcessingThreadPool(properties: IngestProperties): ThreadPoolExecutor {
        return ThreadPoolExecutor(
            properties.ingestJobThreadPoolSize, properties.ingestJobThreadPoolSize, 0, TimeUnit.MILLISECONDS,
            LinkedBlockingQueue(),
            ThreadFactoryBuilder().setNameFormat("ingest-processor-%d").build()
        )
    }

    @Bean
    @ConditionalOnProperty(value = ["scheduling.ingest.enabled"], havingValue = "true", matchIfMissing = true)
    fun ingestProcessingExecutor(
        ingestProcessingThreadPool: ThreadPoolExecutor,
        metricsService: MetricsService
    ): ExecutorService {
        return ExecutorServiceMetrics.monitor(
            metricsService.meterRegistry,
            ingestProcessingThreadPool,
            "ingest-processing-executor"
        )
    }

    @Bean
    @ConditionalOnProperty(value = ["scheduling.enabled"], havingValue = "true", matchIfMissing = true)
    fun integrationCheckThreadPool(properties: IntegrationProperties): ThreadPoolExecutor {
        return ThreadPoolExecutor(
            properties.checkIntegrationsJobPoolSize, properties.checkIntegrationsJobPoolSize, 0, TimeUnit.MILLISECONDS,
            LinkedBlockingQueue(),
            ThreadFactoryBuilder().setNameFormat("integration-checker-%d").build()
        )
    }

    @Bean
    @ConditionalOnProperty(value = ["scheduling.enabled"], havingValue = "true", matchIfMissing = true)
    fun integrationCheckExecutor(
        integrationCheckThreadPool: ThreadPoolExecutor,
        metricsService: MetricsService
    ): ExecutorService {
        return ExecutorServiceMetrics.monitor(
            metricsService.meterRegistry,
            integrationCheckThreadPool,
            "check-integrations-executor"
        )
    }

    @Bean
    @ConditionalOnProperty(value = ["scheduling.enabled"], havingValue = "true", matchIfMissing = true)
    fun testSetRunThreadPool(properties: QAProperties): ThreadPoolExecutor {
        return ThreadPoolExecutor(
            properties.testSetRunThreadPoolSize, properties.testSetRunThreadPoolSize, 0, TimeUnit.MILLISECONDS,
            LinkedBlockingQueue(),
            ThreadFactoryBuilder().setNameFormat("test-runner-%d").build()
        )
    }

    @Bean
    @ConditionalOnProperty(value = ["scheduling.enabled"], havingValue = "true", matchIfMissing = true)
    fun testSetRunExecutor(
        testSetRunThreadPool: ThreadPoolExecutor,
        metricsService: MetricsService
    ): ExecutorService {
        return ExecutorServiceMetrics.monitor(
            metricsService.meterRegistry,
            testSetRunThreadPool,
            "test-run-executor"
        )
    }

    @Bean
    @ConditionalOnProperty(value = ["scheduling.enabled"], havingValue = "true", matchIfMissing = true)
    fun testSetGenerationThreadPool(properties: QAProperties): ThreadPoolExecutor {
        return ThreadPoolExecutor(
            properties.testSetGenerationThreadPoolSize, properties.testSetGenerationThreadPoolSize, 0, TimeUnit.MILLISECONDS,
            LinkedBlockingQueue(),
            ThreadFactoryBuilder().setNameFormat("testset-generator-%d").build()
        )
    }

    @Bean
    @ConditionalOnProperty(value = ["scheduling.enabled"], havingValue = "true", matchIfMissing = true)
    fun testSetGenerationExecutor(
        testSetGenerationThreadPool: ThreadPoolExecutor,
        metricsService: MetricsService
    ): ExecutorService {
        return ExecutorServiceMetrics.monitor(
            metricsService.meterRegistry,
            testSetGenerationThreadPool,
            "testset-generation-executor"
        )
    }
}
