CREATE TABLE public.attachment
(
    id          BIGINT <PERSON><PERSON><PERSON><PERSON>D ALWAYS AS IDENTITY PRIMARY KEY,
    external_id VARCHAR UNIQUE NOT NULL,
    file_id     BIGINT         NOT NULL REFERENCES public.project_file (id) ON DELETE CASCADE
);
CREATE INDEX idx_attachment_file_id ON public.attachment (file_id);

CREATE TABLE public.attachment_link
(
    id            BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    public_id     VARCHAR UNIQUE NOT NULL,
    attachment_id BIGINT         NOT NULL REFERENCES public.attachment (id) ON DELETE CASCADE,
    expired_at    TIMESTAMP      NOT NULL
);
CREATE INDEX idx_attachment_link_expired_at ON public.attachment_link (expired_at);
