package com.justai.khub.common.endpoint

import com.justai.e2echeck.E2ECheck.*
import com.justai.khub.api.fe.SystemApiService
import com.justai.khub.api.fe.model.E2ECheckResult
import com.justai.khub.common.mapper.CommonMapper.toResponse
import com.justai.khub.common.service.E2EAndVersionsService
import com.justai.khub.common.service.e2e.SelfE2EChecker
import com.justai.khub.common.util.JSON.toObjectNode
import org.slf4j.LoggerFactory
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import java.util.*

@Service
class SystemEndpoint(
    private val e2EAndVersionsService: E2EAndVersionsService,
    private val seflE2EChecker: SelfE2EChecker
) : SystemApiService {
    @Volatile
    private var running = false

    private val log = LoggerFactory.getLogger(this::class.java)


    override fun healthCheckResponseEntity(): ResponseEntity<String> {
        return if (running) {
            ResponseEntity.ok("Ok")
        } else {
            ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body("Service Unavailable")
        }
    }

    override fun version() = seflE2EChecker.selfVersion.toObjectNode()

    override fun getVersion() = seflE2EChecker.selfVersion.toObjectNode()

    override fun e2ECheck(zRequestId: String?, loggingEnabledE2ECheck: Boolean): E2ECheckResult {
        val requestId = zRequestId ?: UUID.randomUUID().toString()
        val startedAt = System.currentTimeMillis()
        if (loggingEnabledE2ECheck) log.debug("E2E check started")

        val e2eChecks = e2EAndVersionsService.performE2E(requestId, loggingEnabledE2ECheck)
        val hasFailedChecks = e2eChecks.any { it.status == STATUS_FAIL }
        val hasWarnChecks = e2eChecks.any { it.status == STATUS_WARN }

        val processingTime = System.currentTimeMillis() - startedAt
        if (loggingEnabledE2ECheck) log.debug("E2E check finished, processing time = {}ms ", processingTime)

        return E2ECheckResult(
            totalTime = processingTime,
            totalStatus = if (hasFailedChecks) STATUS_FAIL else if (hasWarnChecks) STATUS_WARN else STATUS_OK,
            e2EComponents = e2eChecks.toResponse()
        )
    }

    override fun versions(): List<Any> {
        return e2EAndVersionsService.getVersions()
    }

    @EventListener(classes = [ApplicationReadyEvent::class])
    fun onReady() {
        running = true
    }
}
