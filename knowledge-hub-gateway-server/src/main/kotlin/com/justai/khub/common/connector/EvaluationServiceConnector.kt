package com.justai.khub.common.connector

import com.fasterxml.jackson.databind.node.ObjectNode
import com.justai.khub.api.fe.model.E2ECheckResult
import com.justai.khub.api.fe.model.E2EComponent
import com.justai.khub.common.configuration.dto.IntegrationProperties
import com.justai.khub.common.dto.ServiceVersionResponse
import com.justai.khub.common.dto.WithIntegrationTimer
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.enumeration.ApiErrorCode.Companion.SOURCE_ERROR_CODE_ARG
import com.justai.khub.common.enumeration.IntegrationName
import com.justai.khub.common.exception.KhubException
import com.justai.khub.common.util.JSON
import com.justai.khub.common.dto.rag.ChunkResponse
import com.justai.khub.project.dto.LLMConfig
import com.justai.khub.project.mapper.EvaluationServiceMapper
import com.justai.khub.qa.dto.TestEvaluationResponse
import com.justai.khub.qa.entity.TestEntity
import com.justai.loadbalancer.annotations.HttpInternal
import org.slf4j.LoggerFactory
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod.GET
import org.springframework.http.MediaType
import org.springframework.stereotype.Service
import org.springframework.web.client.RestClientResponseException
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.postForObject

@Service
@WithIntegrationTimer(integrationName = IntegrationName.evaluationService)
class EvaluationServiceConnector(
    @HttpInternal private val evaluationServiceRestTemplate: RestTemplate,
    integrationProperties: IntegrationProperties
) {

    private val log = LoggerFactory.getLogger(this::class.java)

    val baseUrl = integrationProperties.evaluator.baseUrl

    fun getVersion(): ServiceVersionResponse {
        return evaluationServiceRestTemplate.getForObject("${baseUrl}/version", ServiceVersionResponse::class.java)!!
    }

    fun performE2E(requestId: String, loggingEnabledE2ECheck: Boolean): List<E2EComponent> {
        val request = HttpEntity(
            null,
            HttpHeaders().apply {
                set("Z-RequestId", requestId)
                set("loggingEnabledE2ECheck", "$loggingEnabledE2ECheck")
            }
        )
        val result = evaluationServiceRestTemplate.exchange("${baseUrl}/e2e-check", GET, request, E2ECheckResult::class.java)
        return result.body?.e2EComponents ?: listOf()
    }

    fun evaluateAnswer(
        test: TestEntity,
        actualAnswer: String,
        prompt: String,
        usedChunks: List<ChunkResponse>,
        llm: LLMConfig
    ): TestEvaluationResponse {
        val request = EvaluationServiceMapper.toEvaluationRequest(test, actualAnswer, usedChunks, prompt, llm)
        try {
            return evaluationServiceRestTemplate.postForObject(
                "${baseUrl}/api/v1/evaluate-single",
                request,
                TestEvaluationResponse::class
            )
        } catch (ex: Exception) {
            handleException(ex)
            throw ex
        }
    }

    private fun handleException(ex: Exception) {
        val errorCode =
            if (ex is RestClientResponseException && ex.responseHeaders?.contentType?.isCompatibleWith(MediaType.APPLICATION_JSON) == true) {
                (JSON.parse(ex.responseBodyAsString) as ObjectNode)["code"]?.asText()
                    ?: ApiErrorCode.EVALUATOR_INTEGRATION_ERROR.code
            } else {
                null
            }
        throw KhubException(ApiErrorCode.EVALUATOR_INTEGRATION_ERROR, mapOf(SOURCE_ERROR_CODE_ARG to errorCode), ex)
    }

}
