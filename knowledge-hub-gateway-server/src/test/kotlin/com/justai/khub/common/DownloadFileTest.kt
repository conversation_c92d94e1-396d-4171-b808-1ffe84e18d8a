package com.justai.khub.common

import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.common.util.WebUtils
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertArrayEquals
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.*
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.http.client.ClientHttpResponse
import org.springframework.web.client.RequestCallback
import org.springframework.web.client.ResponseExtractor
import org.springframework.web.client.RestTemplate
import java.io.ByteArrayInputStream
import java.io.IOException
import java.io.InputStream
import java.net.URI
import java.nio.file.Files
import java.nio.file.Path

class DownloadFileTest {

    private lateinit var mockRestTemplate: RestTemplate
    private lateinit var mockResponse: ClientHttpResponse
    private lateinit var tempFile: Path
    private val maxFileSize = 10L * 1024 * 1024

    @BeforeEach
    fun setUp() {
        mockRestTemplate = mock(RestTemplate::class.java)
        mockResponse = mock(ClientHttpResponse::class.java)
        tempFile = Files.createTempFile("upload", ".tmp")
    }

    @AfterEach
    fun tearDown() {
        Files.deleteIfExists(tempFile)
    }

    @Test
    fun testSuccessfulDownloadWithinSizeLimit() {
        val testData = "This is test content".toByteArray()
        val testUri = URI("https://example.com/test.txt")
        val mockHeaders = HttpHeaders()
        mockHeaders.contentLength = testData.size.toLong()

        `when`(mockResponse.headers).thenReturn(mockHeaders)
        `when`(mockResponse.body).thenReturn(ByteArrayInputStream(testData))
        `when`(mockResponse.statusCode).thenReturn(HttpStatus.OK)
        setupResponse(testUri, mockRestTemplate, mockResponse)

        WebUtils.downloadFileFromUrl(mockRestTemplate, testUri, maxFileSize, tempFile)
        val downloadedContent = Files.readAllBytes(tempFile)
        assertArrayEquals(testData, downloadedContent)
    }

    @Test
    fun testFileExceedingContentLengthHeaderLimit() {
        val testUri = URI("https://example.com/large-file.bin")
        val mockHeaders = HttpHeaders().apply { contentLength = maxFileSize + 1000 } // Exceeds max size by 1KB

        `when`(mockResponse.headers).thenReturn(mockHeaders)
        setupResponse(testUri, mockRestTemplate, mockResponse)

        val exception = assertThrows<KhubException> {
            WebUtils.downloadFileFromUrl(mockRestTemplate, testUri, maxFileSize, tempFile)
        }
        assertEquals(ApiErrorCode.TOO_LARGE_FILE_ERROR, exception.errors.first().error)
    }

    @Test
    fun testFileExceedingActualSizeLimitDuringStreaming() {
        val testUri = URI("https://example.com/sneaky-large-file.bin")
        val mockHeaders = HttpHeaders().apply { contentLength = -1 } // No content length provided
        val largeData = ByteArray((maxFileSize + 1024).toInt())

        `when`(mockResponse.headers).thenReturn(mockHeaders)
        `when`(mockResponse.body).thenReturn(ByteArrayInputStream(largeData))
        `when`(mockResponse.statusCode).thenReturn(HttpStatus.OK)
        setupResponse(testUri, mockRestTemplate, mockResponse)

        val exception = assertThrows<KhubException> {
            WebUtils.downloadFileFromUrl(mockRestTemplate, testUri, maxFileSize, tempFile)
        }
        assertEquals(ApiErrorCode.TOO_LARGE_FILE_ERROR, exception.errors.first().error)
    }

    @Test
    fun testFileWithIncorrectContentLengthHeader() {
        val testUri = URI("https://example.com/wrong-header.bin")
        val mockHeaders = HttpHeaders().apply { contentLength = 100 } // Reported as 100 bytes }

        // But actually larger than max size
        val largeData = ByteArray((maxFileSize + 1024).toInt())

        `when`(mockResponse.headers).thenReturn(mockHeaders)
        `when`(mockResponse.body).thenReturn(ByteArrayInputStream(largeData))
        `when`(mockResponse.statusCode).thenReturn(HttpStatus.OK)
        setupResponse(testUri, mockRestTemplate, mockResponse)

        val exception = assertThrows<KhubException> {
            WebUtils.downloadFileFromUrl(mockRestTemplate, testUri, maxFileSize, tempFile)
        }
        assertEquals(ApiErrorCode.TOO_LARGE_FILE_ERROR, exception.errors.first().error)
    }

    @Test
    fun testFileWithNetworkError() {
        val testUri = URI("https://example.com/error-file.txt")

        `when`(mockRestTemplate.execute(eq(testUri), eq(HttpMethod.GET), any(RequestCallback::class.java), any(ResponseExtractor::class.java)))
            .thenThrow(RuntimeException("Network error"))

        val exception = assertThrows<KhubException> {
            WebUtils.downloadFileFromUrl(mockRestTemplate, testUri, maxFileSize, tempFile)
        }
        assertEquals(ApiErrorCode.FILE_DOWNLOAD_ERROR, exception.errors.first().error)
    }

    @Test
    fun testFileWithZeroContentLength() {
        val testData = ByteArray(0)
        val testUri = URI("https://example.com/empty.txt")
        val mockHeaders = HttpHeaders().apply { contentLength = 0 }

        `when`(mockResponse.headers).thenReturn(mockHeaders)
        `when`(mockResponse.body).thenReturn(ByteArrayInputStream(testData))
        `when`(mockResponse.statusCode).thenReturn(HttpStatus.OK)
        setupResponse(testUri, mockRestTemplate, mockResponse)

        WebUtils.downloadFileFromUrl(mockRestTemplate, testUri, maxFileSize, tempFile)
        assertEquals(0, Files.size(tempFile))
    }

    @Test
    fun testFileWithExactlyMaxSizeLimit() {
        val testData = ByteArray(maxFileSize.toInt())
        val testUri = URI("https://example.com/exactly-max.bin")
        val mockHeaders = HttpHeaders().apply { contentLength = maxFileSize }

        `when`(mockResponse.headers).thenReturn(mockHeaders)
        `when`(mockResponse.body).thenReturn(ByteArrayInputStream(testData))
        `when`(mockResponse.statusCode).thenReturn(HttpStatus.OK)
        setupResponse(testUri, mockRestTemplate, mockResponse)

        WebUtils.downloadFileFromUrl(mockRestTemplate, testUri, maxFileSize, tempFile)
        assertEquals(maxFileSize, Files.size(tempFile))
    }

    @Test
    fun testHandlingOfClosedInputStream() {
        val testUri = URI("https://example.com/closed-stream.txt")
        val mockInputStream = mock(InputStream::class.java)
        `when`(mockInputStream.read(any())).thenThrow(IOException("Stream closed"))
        `when`(mockInputStream.read(any(), anyInt(), anyInt())).thenThrow(IOException("Stream closed"))

        `when`(mockResponse.headers).thenReturn(HttpHeaders())
        `when`(mockResponse.body).thenReturn(mockInputStream)
        `when`(mockResponse.statusCode).thenReturn(HttpStatus.OK)
        setupResponse(testUri, mockRestTemplate, mockResponse)

        val exception = assertThrows<KhubException> {
            WebUtils.downloadFileFromUrl(mockRestTemplate, testUri, maxFileSize, tempFile)
        }
        assertEquals(ApiErrorCode.FILE_DOWNLOAD_ERROR, exception.errors.first().error)
    }

    private fun setupResponse(testUri: URI, mockRestTemplate: RestTemplate, mockResponse: ClientHttpResponse) {
        `when`(mockRestTemplate.execute(eq(testUri), eq(HttpMethod.GET), any(RequestCallback::class.java), any(ResponseExtractor::class.java)))
            .thenAnswer { invocation ->
                val responseExtractor = invocation.arguments[3] as ResponseExtractor<*>
                responseExtractor.extractData(mockResponse)
                null
            }
    }
}
