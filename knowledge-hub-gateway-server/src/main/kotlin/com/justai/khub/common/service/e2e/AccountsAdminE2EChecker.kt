package com.justai.khub.common.service.e2e

import com.justai.e2echeck.E2ECheck.STATUS_FAIL
import com.justai.e2echeck.E2ECheck.STATUS_OK
import com.justai.e2echeck.E2EComponentDTO
import com.justai.khub.common.connector.AccountsAdminConnector
import com.justai.khub.common.dto.ServiceVersion
import com.justai.khub.common.mapper.CommonMapper.toDTO
import org.springframework.stereotype.Component
import java.time.Instant

@Component
class AccountsAdminE2EChecker(
    private val accountsAdminConnector: AccountsAdminConnector
) : E2EChecker(NAME) {
    override fun performE2E(requestId: String, loggingEnabledE2ECheck: Boolean): List<E2EComponentDTO> {
        val startedAt = Instant.now()
        val isReady = accountsAdminConnector.healthCheck()
        val processingTime = Instant.now().toEpochMilli() - startedAt.toEpochMilli()
        return if (isReady) {
            listOf(E2EComponentDTO(formatE2ECheckName(), STATUS_OK, startedAt.toString(), processingTime))
        } else {
            listOf(E2EComponentDTO(formatE2ECheckName(), STATUS_FAIL, startedAt.toString(), processingTime))
        }
    }

    override fun getVersion(): List<ServiceVersion> {
        return listOf(accountsAdminConnector.getVersion().toDTO(NAME))
    }

    companion object {
        const val NAME = "accountsadmin"
    }
}
