package com.justai.khub.common.dto

data class ServiceVersion(
    val name: String,
    val version: String,
    val status: String,
    val buildBranch: String? = null,
    val buildNumber: String? = null,
    val buildDate: String? = null,
    val buildChangeSet: String? = null,
    val projectArtifactId: String? = null,
    val projectVersion: String? = null
) {
    companion object {
        const val STATUS_OK = "ok"
        const val STATUS_ERROR = "error"
        const val STATUS_UNAVAILABLE = "unavailable"
        const val UNKNOWN_VERSION = "unknown"
    }
}
