openapi: 3.0.1

info:
  title: Knowledge Hub service API
  version: 1.0.0

servers:
  - url: http://localhost:9333
    description: Development server

tags:
  - name: System
    description: The API provides the app system information.
  - name: User
    description: Users related API.
  - name: Project
    description: Projects related API.
  - name: Ingest
    description: Ingest data related API.
  - name: ProjectFiles
    description: Files related API.
  - name: ApiKey
    description: Api keys related API.
  - name: Report
    description: Reports keys related API.
  - name: Integration
    description: Integration related API.
  - name: Attachments
    description: File attachments related API.
  - name: Channels
    description: External channels related API.
  - name: QA
    description: Quality analysis related API.

paths:
  # *******************************************************************************
  #                           System Service
  # *******************************************************************************
  /healthCheck:
    get:
      summary: Service health check
      operationId: healthCheck
      security: [ ]
      tags:
        - System
      responses:
        '200':
          description: Service health check
          content:
            application/json:
              schema:
                type: string
  /version:
    get:
      summary: Service version
      operationId: version
      security: [ ]
      tags:
        - System
      responses:
        '200':
          description: Service version
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ObjectNode'
  /api/khub/version:
    get:
      summary: Service version
      operationId: getVersion
      security: [ ]
      tags:
        - System
      responses:
        '200':
          description: Service version
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ObjectNode'
  /api/khub/versions:
    get:
      summary: Service versions
      operationId: versions
      security: [ ]
      tags:
        - System
      responses:
        '200':
          description: Service versions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ArrayNode'
  # *******************************************************************************
  #                           E2ECheck
  # *******************************************************************************

  /api/khub/e2e-check:
    get:
      tags:
        - System
      description: e2e check
      operationId: e2ECheck
      parameters:
        - $ref: '#/components/parameters/Z-requestIdHeaderOptional'
        - $ref: '#/components/parameters/loggingEnabledE2ECheckHeaderOptional'
      responses:
        '200':
          description: Ok.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/E2ECheckResult'

  # *******************************************************************************
  #                           Users Service
  # *******************************************************************************
  /api/khub/web/users/me:
    get:
      tags:
        - User
      operationId: getMe
      summary: 'Get current user'
      responses:
        '200':
          description: UserInfoDTO
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserInfoDTO'
  /api/khub/web/users/me/balance:
    get:
      tags:
        - User
      operationId: getCurrentUserBalance
      summary: 'Get balance of current user'
      responses:
        '200':
          description: UserBalanceDTO
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserBalanceDTO'

  # *******************************************************************************
  #                           Projects Service
  # *******************************************************************************
  /api/khub/projects:
    get:
      tags:
        - Projects
      operationId: getProjects
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/pageNum'
        - $ref: '#/components/parameters/pageSize'

      responses:
        '200':
          description: ProjectsPage
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectsPage'
    post:
      tags:
        - Projects
      operationId: createProject
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProjectCreateRequest'
      responses:
        '200':
          description: ProjectDTO
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectDTO'
  /api/khub/projects/{projectId}:
    get:
      tags:
        - Projects
      operationId: getProject
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectDTO'
    put:
      tags:
        - Projects
      operationId: updateProject
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProjectUpdateRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectDTO'
    delete:
      tags:
        - Projects
      operationId: deleteProject
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
      responses:
        '200':
          description: OK
  /api/khub/projects/{projectId}/ingest:
    post:
      tags:
        - Ingest
      operationId: ingest
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: 'Run ingest pipeline for new files'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/version'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectDTO'

  /api/khub/projects/{projectId}/api-keys:
    post:
      tags:
        - ApiKey
      operationId: createApiKey
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: 'Create api key'
      parameters:
        - $ref: '#/components/parameters/projectId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApiKeyCreateRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiKeyDTO'
    get:
      tags:
        - ApiKey
      operationId: getApiKeys
      security:
        - cookieAuth: [ ]
      summary: 'Get api keys'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/onlyActive'
        - $ref: '#/components/parameters/pageNum'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiKeysPage'

  /api/khub/projects/{projectId}/api-keys/{apiKeyId}/request-samples:
    get:
      tags:
        - ApiKey
      operationId: apiRequestSamples
      security:
        - cookieAuth: [ ]
      summary: 'Api request samples'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/apiKeyId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiRequestSamples'

  /api/khub/projects/{projectId}/api-keys/{apiKeyId}/revoke:
    post:
      tags:
        - ApiKey
      operationId: revokeApiKey
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: 'Revoke api key'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/apiKeyId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiKeyDTO'

  /api/khub/projects/{projectId}/retrieve:
    post:
      tags:
        - Retrieve
      operationId: retrieveChunks
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: 'Retrieve chunks'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/version'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RetrieveChunksRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RetrievedChunksDTO'

  /api/khub/projects/{projectId}/versions/default/settings:
    get:
      tags:
        - Projects
      operationId: getDefaultVersionSettings
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectSettingsDTO'
    post:
      tags:
        - Projects
      operationId: setDefaultVersionSettings
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProjectSettingsDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectSettingsDTO'

  /api/khub/projects/{projectId}/ingest/cancel:
    post:
      tags:
        - Ingest
      operationId: cancelIngest
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: 'Cancel ingest pipeline'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/version'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CancelIngestRequest'
      responses:
        '200':
          description: OK

  # *******************************************************************************
  #                           Project Sources Service
  # *******************************************************************************
  /api/khub/projects/{projectId}/files:
    post:
      tags:
        - ProjectFiles
      operationId: addFile
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                version:
                  type: string
                metadata:
                  type: string
                file:
                  type: string
                  format:
                    binary
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceDTO'
    get:
      tags:
        - ProjectFiles
      operationId: getFiles
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/version'
        - $ref: '#/components/parameters/pageNum'
        - $ref: '#/components/parameters/pageSize'
        - $ref: '#/components/parameters/name'
        - $ref: '#/components/parameters/fileSort'
        - $ref: '#/components/parameters/direction'
        - $ref: '#/components/parameters/statusFilter'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectSourcesPage'
  /api/khub/projects/{projectId}/files/summary:
    get:
      tags:
        - ProjectFiles
      operationId: getProjectFilesSummary
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/version'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectFilesSummary'
  /api/khub/projects/{projectId}/files/{fileId}:
    delete:
      tags:
        - ProjectFiles
      operationId: deleteFile
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/fileId'
      responses:
        '200':
          description: OK

  /api/khub/projects/{projectId}/files/{fileId}/content:
    get:
      tags:
        - ProjectFiles
      operationId: downloadFile
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/fileId'
      responses:
        '200':
          description: OK
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
          headers:
            Content-Disposition:
              schema:
                type: string
              example: attachment; filename="example.pdf"

  /api/khub/projects/{projectId}/files/{fileId}/parsed-content:
    get:
      tags:
        - ProjectFiles
      operationId: downloadParsedFile
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/fileId'
      responses:
        '200':
          description: OK
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
          headers:
            Content-Disposition:
              schema:
                type: string
              example: attachment; filename="example.zip"
  /api/khub/projects/{projectId}/files/{fileId}/chunks:
    get:
      tags:
        - ProjectFiles
      operationId: downloadChunks
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/fileId'
      responses:
        '200':
          description: OK
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
          headers:
            Content-Disposition:
              schema:
                type: string
              example: attachment; filename="example.zip"

  # *******************************************************************************
  #                           Chat Service
  # *******************************************************************************
  /api/khub/projects/{projectId}/chat:
    get:
      tags:
        - Chat
      operationId: getChats
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: 'Get chats'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/version'
        - $ref: '#/components/parameters/pageNum'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatsPage'
    post:
      tags:
        - Chat
      operationId: createChat
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: 'Create chat'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/version'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatCreateRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatDTO'

  /api/khub/projects/{projectId}/chat/default:
    post:
      tags:
        - Chat
      operationId: makeRequestToDefaultChat
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: 'Make request to default chat'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/version'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserQuery'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatHistoryRecordDTO'
  /api/khub/projects/{projectId}/chat/default/settings:
    get:
      tags:
        - Chat
      operationId: getDefaultChatSettings
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: 'Get default chat settings'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/version'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatSettingsDTO'
    post:
      tags:
        - Chat
      operationId: setDefaultChatSettings
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: 'Set default chat settings'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/version'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatSettingsDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatSettingsDTO'
  /api/khub/projects/{projectId}/chat/default/retrieve:
    post:
      tags:
        - Retrieve
      operationId: retrieveChunksForDefaultChat
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: 'Retrieve chunks using default chat settings'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/version'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RetrieveChunksRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RetrievedChunksDTO'
  /api/khub/projects/{projectId}/chat/{chatId}/records/{recordId}/chunks:
    get:
      tags:
        - Retrieve
      operationId: retrieveChunksForRecord
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: 'Get chat record used chunks by id'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/chatId'
        - $ref: '#/components/parameters/recordId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RetrievedChunksDTO'
        '204':
          description: No used chunks found
  /api/khub/projects/{projectId}/chat/default/history:
    get:
      tags:
        - Chat
      operationId: getDefaultChatHistory
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: 'Get chat history'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/version'
        - $ref: '#/components/parameters/pageNum'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatHistoryPage'
  /api/khub/projects/{projectId}/chat/default/history/clear:
    post:
      tags:
        - Chat
      operationId: clearDefaultChatHistory
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: 'Clear default chat history'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/version'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatHistoryPage'
  /api/khub/projects/{projectId}/chat/{chatId}/settings:
    get:
      tags:
        - Chat
      operationId: getChatSettings
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: 'Get chat settings'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/chatId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatSettingsDTO'
    post:
      tags:
        - Chat
      operationId: setChatSettings
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: 'Set chat settings'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/chatId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatSettingsDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatSettingsDTO'
  /api/khub/projects/{projectId}/chat/{chatId}/history:
    get:
      tags:
        - Chat
      operationId: getChatHistory
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: 'Get chat history'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/chatId'
        - $ref: '#/components/parameters/pageNum'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatHistoryPage'
  /api/khub/projects/{projectId}/chat/{chatId}/history/clear:
    post:
      tags:
        - Chat
      operationId: clearChatHistory
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: 'Clear chat history'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/chatId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatHistoryPage'
  /api/khub/projects/{projectId}/chat/{chatId}:
    post:
      tags:
        - Chat
      operationId: makeRequest
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: 'Make request to chat'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/chatId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserQuery'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatHistoryRecordDTO'

  /api/khub/projects/{projectId}/chat/{chatId}/records/{recordId}:
    get:
      tags:
        - Chat
      operationId: getChatRecord
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: 'Get chat record by id'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/chatId'
        - $ref: '#/components/parameters/recordId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatHistoryRecordDTO'
  /api/khub/projects/{projectId}/chat/{chatId}/records/{recordId}/cancel:
    post:
      tags:
        - Chat
      operationId: cancelRecordProcessing
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: 'Cancel processing chat record'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/chatId'
        - $ref: '#/components/parameters/recordId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatHistoryRecordDTO'
  # *******************************************************************************
  #                           Reports Service
  # *******************************************************************************
  /api/khub/internal/report:
    get:
      tags:
        - Report
      operationId: createAccountReport
      summary: 'Create report for account'
      security:
        - basicAuth: []
      parameters:
        - $ref: '#/components/parameters/accountIdQueryParam'
        - $ref: '#/components/parameters/period'
        - $ref: '#/components/parameters/dateFrom'
        - $ref: '#/components/parameters/dateTo'
      responses:
        '200':
          description: OK
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
          headers:
            Content-Disposition:
              schema:
                type: string
              example: attachment; filename="example.csv"
  # *******************************************************************************
  #                           Integration Service
  # *******************************************************************************
  /api/khub/projects/{projectId}/integrations:
    get:
      tags:
        - Integration
      operationId: getIntegrations
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/version'
        - $ref: '#/components/parameters/pageNum'
        - $ref: '#/components/parameters/pageSize'
        - $ref: '#/components/parameters/name'
        - $ref: '#/components/parameters/integrationSort'
        - $ref: '#/components/parameters/direction'
        - $ref: '#/components/parameters/statusFilter'

      responses:
        '200':
          description: IntegrationsPage
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntegrationsPage'
    post:
      tags:
        - Integration
      operationId: createIntegration
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/version'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IntegrationDTO'
      responses:
        '200':
          description: IntegrationDTO
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntegrationFull'
  /api/khub/projects/{projectId}/integrations/{integrationId}:
    get:
      tags:
        - Integration
      operationId: getIntegration
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/integrationId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntegrationFull'
    put:
      tags:
        - Integration
      operationId: updateIntegration
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/integrationId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IntegrationDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntegrationFull'
    delete:
      tags:
        - Integration
      operationId: deleteIntegration
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/integrationId'
      responses:
        '200':
          description: OK
  /api/khub/projects/{projectId}/integrations/{integrationId}/sync:
    get:
      tags:
        - Integration
      operationId: syncIntegration
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/integrationId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntegrationShort'
  /api/khub/integrations/atlassian/oauth2:
    get:
      tags:
        - Integration
      operationId: authenticateAtlassianOAuth2
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/state'
        - $ref: '#/components/parameters/code'
      responses:
        '200':
          description: OK
          content:
            text/html:
              schema:
                type: string

  # *******************************************************************************
  #                           Channels Service
  # *******************************************************************************
  /api/khub/projects/{projectId}/channels:
    get:
      tags:
        - Channels
      operationId: getChannels
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/version'
        - $ref: '#/components/parameters/pageNum'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: ChannelsPage
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChannelsPage'
    post:
      tags:
        - Channels
      operationId: createChannel
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/version'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChannelDTO'
      responses:
        '200':
          description: ChannelDTO
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChannelInfoDTO'
  /api/khub/projects/{projectId}/channels/{channelId}/jaicp/{jaicpChannelId}/block:
    put:
      tags:
        - Channels
      operationId: blockJaicpChannel
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/version'
        - $ref: '#/components/parameters/channelId'
        - $ref: '#/components/parameters/jaicpChannelId'
      responses:
        '200':
          description: Ok
  /api/khub/projects/{projectId}/channels/{channelId}/jaicp/{jaicpChannelId}/unblock:
    put:
      tags:
        - Channels
      operationId: unblockJaicpChannel
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/version'
        - $ref: '#/components/parameters/channelId'
        - $ref: '#/components/parameters/jaicpChannelId'
      responses:
        '200':
          description: Ok
  # *******************************************************************************
  #                           Attachments Service
  # *******************************************************************************
  /api/khub/public/attachments/{attachmentId}/{linkId}:
    get:
      tags:
        - Attachments
      operationId: downloadFileAttachment
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/attachmentId'
        - $ref: '#/components/parameters/linkId'
      responses:
        '200':
          description: OK
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
          headers:
            Content-Disposition:
              schema:
                type: string
              example: attachment; filename="example.png"

  # *******************************************************************************
  #                           QA Service
  # *******************************************************************************
  /api/khub/projects/{projectId}/testSet:
    get:
      tags:
        - QA
      operationId: getTestSetsPage
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
      responses:
        '200':
          description: Not archived testSets page
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestSetsPage'
  /api/khub/projects/{projectId}/testSet/archived:
    get:
      tags:
        - QA
      operationId: getArchivedTestSetsPage
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/pageNum'
        - $ref: '#/components/parameters/pageSize'
      responses:
        '200':
          description: Archived testSets page
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestSetsPage'
  /api/khub/projects/{projectId}/testSet/settings:
    get:
      tags:
        - QA
      operationId: getDefaultTestSetSettings
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
      responses:
        '200':
          description: Default test set settings
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestSetSettingsDTO'
  /api/khub/projects/{projectId}/testSet/sample:
    get:
      tags:
        - QA
      operationId: downloadTestSetSample
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
      responses:
        '200':
          description: OK
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
          headers:
            Content-Disposition:
              schema:
                type: string
              example: attachment; filename="testSet.xlsx"
  /api/khub/projects/{projectId}/testSet/models:
    get:
      tags:
        - QA
      operationId: getTestSetGenerationModels
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
      responses:
        '200':
          description: List of models that can be used for test set generation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NamedValuesList'
  /api/khub/projects/{projectId}/testSet/order:
    post:
      tags:
        - QA
      operationId: setTestSetsOrder
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TestSetOrderDTO'
      responses:
        '200':
          description: OK
  /api/khub/projects/{projectId}/testSet/generate:
    post:
      tags:
        - QA
      operationId: generateTestSet
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerateTestSetRequest'
      responses:
        '200':
          description: TestSet
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestSetDTO'
  /api/khub/projects/{projectId}/testSet/upload:
    post:
      tags:
        - QA
      operationId: uploadTestSet
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format:
                    binary
                name:
                  type: string
      responses:
        '200':
          description: TestSet
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestSetDTO'

  /api/khub/projects/{projectId}/testSet/{testSetId}/archive:
    post:
      tags:
        - QA
      operationId: archiveTestSet
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/testSetId'
      responses:
        '200':
          description: Ok
  /api/khub/projects/{projectId}/testSet/{testSetId}/cancel:
    post:
      tags:
        - QA
      operationId: cancelGeneration
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/testSetId'
      responses:
        '200':
          description: Ok
  /api/khub/projects/{projectId}/testSet/{testSetId}/unarchive:
    post:
      tags:
        - QA
      operationId: unArchiveTestSet
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/testSetId'
      responses:
        '200':
          description: Ok
  /api/khub/projects/{projectId}/testSet/{testSetId}:
    get:
      tags:
        - QA
      operationId: getTestSet
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/testSetId'
      responses:
        '200':
          description: TestSet
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestSetDTO'
    put:
      tags:
        - QA
      operationId: updateTestSet
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/testSetId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TestSetUpdateRequest'
      responses:
        '200':
          description: TestSet
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestSetDTO'
    delete:
      tags:
        - QA
      operationId: deleteTestSet
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/testSetId'
      responses:
        '200':
          description: OK
  /api/khub/projects/{projectId}/testSet/{testSetId}/schedule:
    get:
      tags:
        - QA
      operationId: getTestSetSchedule
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/testSetId'
      responses:
        '200':
          description: TestSetSchedule
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestSetScheduleDTO'
        '204':
          description: No Content
    post:
      tags:
        - QA
      operationId: setTestSetSchedule
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/testSetId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TestSetScheduleDTO'
      responses:
        '200':
          description: TestSetSchedule
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestSetScheduleDTO'
    delete:
      tags:
        - QA
      operationId: deleteTestSetSchedule
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/testSetId'
      responses:
        '200':
          description: OK

  /api/khub/projects/{projectId}/testSet/{testSetId}/run:
    post:
      tags:
        - QA
      operationId: runTestSet
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/testSetId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TestSetRunRequest'
      responses:
        '200':
          description: TestRun
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestSetRunDTO'
  /api/khub/projects/{projectId}/testSet/{testSetId}/content:
    get:
      tags:
        - QA
      operationId: downloadTestSet
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/testSetId'
      responses:
        '200':
          description: OK
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
          headers:
            Content-Disposition:
              schema:
                type: string
              example: attachment; filename="testset.xlsx"
  /api/khub/projects/{projectId}/testSetRun:
    get:
      tags:
        - QA
      operationId: getTestSetRunsPage
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/pageNum'
        - $ref: '#/components/parameters/pageSize'
        - $ref: '#/components/parameters/testSetIdsFilter'
        - $ref: '#/components/parameters/dateFrom'
        - $ref: '#/components/parameters/dateTo'
      responses:
        '200':
          description: TestSet runs page
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestSetRunsPage'
  /api/khub/projects/{projectId}/testSetRun/{testSetRunId}:
    get:
      tags:
        - QA
      operationId: getTestSetRun
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/testSetRunId'
      responses:
        '200':
          description: TestRun
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestSetRunDTO'
  /api/khub/projects/{projectId}/testSetRun/{testSetRunId}/cancel:
    post:
      tags:
        - QA
      operationId: cancelTestSetRun
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/testSetRunId'
      responses:
        '200':
          description: Ok
  /api/khub/projects/{projectId}/testSetRun/{testSetRunId}/report:
    get:
      tags:
        - QA
      operationId: getTestSetRunReport
      security:
        - bearerAuth: [ ]
        - cookieAuth: [ ]
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/testSetRunId'
      responses:
        '200':
          description: OK
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
          headers:
            Content-Disposition:
              schema:
                type: string
              example: attachment; filename="report.xlsx"

components:
  schemas:
    E2ECheckResult:
      properties:
        totalStatus:
          type: string
        totalTime:
          type: integer
          format: int64
        e2EComponents:
          type: array
          items:
            $ref: '#/components/schemas/E2EComponent'

    E2EComponent:
      properties:
        name:
          type: string
        status:
          type: string
        startTime:
          type: string
        elapsedTime:
          type: integer
          format: int64
    LongId:
      type: integer
      format: int64

    AuditDTO:
      required:
        - createdAt
        - createdBy
        - updatedAt
        - updatedBy
      type: object
      properties:
        createdAt:
          type: string
          format: date-time
        createdBy:
          $ref: '#/components/schemas/LongId'
        updatedAt:
          type: string
          format: date-time
        updatedBy:
          $ref: '#/components/schemas/LongId'

    SourceDTO:
      required:
        - id
        - name
        - audit
        - type
        - status
        - version
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        name:
          type: string
        segment:
          type: string
        status:
          type: string
        lastError:
          type: string
        audit:
          $ref: '#/components/schemas/AuditDTO'
        type:
          $ref: '#/components/schemas/SourceType'
        version:
          type: string
        sizeBytes:
          type: integer
        sizeChars:
          type: integer

    SourceType:
      type: string
      enum:
        - FILE
        - CLOUD_CONFLUENCE
        - CONFLUENCE
        - JIRA
        - GIT
        - S3
        - MINERVA

    ChannelType:
      type: string
      enum:
        - jaicp

    ArrayNode:
      type: array
      items:
        type: object
        description: JSON object.

    ObjectNode:
      type: object
      description: JSON object. Cannot be a array or literal.

    PagingResponse:
      required:
        - pageNum
        - pageSize
        - totalCount
      type: object
      properties:
        totalCount:
          type: integer
          format: int64
        pageNum:
          type: integer
          format: int32
        pageSize:
          type: integer
          format: int32

    UserInfoDTO:
      required:
        - accountId
        - login
        - email
        - name
        - ingestSettings
      properties:
        accountId:
          $ref: '#/components/schemas/LongId'
        login:
          type: string
        email:
          type: string
        name:
          type: string
        features:
          type: array
          items:
            type: string
        localFeatures:
          type: array
          items:
            type: string
        ingestSettings:
          $ref: '#/components/schemas/IngestSettingsDTO'
        analytics:
          $ref: '#/components/schemas/AnalyticsDTO'

    IngestSettingsDTO:
      required:
        - supportedExtensions
      properties:
        supportedExtensions:
          type: array
          items:
            type: string

    AnalyticsDTO:
      properties:
        gtm:
          type: string

    UserBalanceDTO:
      required:
        - availableRequestsByTariff
        - usedRequestsByTariff
        - availableRequestsByPackages
        - usedRequestsByPackages
        - operationsSuspended
      properties:
        availableRequestsByTariff:
          type: integer
          format: int64
        usedRequestsByTariff:
          type: integer
          format: int64
        availableRequestsByPackages:
          type: integer
          format: int64
        usedRequestsByPackages:
          type: integer
          format: int64
        operationsSuspended:
          type: boolean
        updatedAt:
          type: string
          format: date-time

    ProjectsPage:
      required:
        - content
        - paging
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/ProjectDTO'
        paging:
          $ref: '#/components/schemas/PagingResponse'

    IntegrationsPage:
      required:
        - content
        - paging
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/IntegrationShort'
        paging:
          $ref: '#/components/schemas/PagingResponse'

    ChannelsPage:
      required:
        - content
        - paging
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/ChannelInfoDTO'
        paging:
          $ref: '#/components/schemas/PagingResponse'

    JaicpChannels:
      required:
        - content
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/JaicpChannelInfoDTO'

    ProjectSourcesPage:
      required:
        - content
        - paging
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/SourceDTO'
        paging:
          $ref: '#/components/schemas/PagingResponse'
    ChatHistoryPage:
      required:
        - content
        - paging
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/ChatHistoryRecordDTO'
        paging:
          $ref: '#/components/schemas/PagingResponse'
    ChatsPage:
      required:
        - content
        - paging
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/ChatDTO'
        paging:
          $ref: '#/components/schemas/PagingResponse'
    ApiRequestSample:
      required:
        - content
        - hint
      type: object
      properties:
        content:
          type: string
        hint:
          type: string
    ApiRequestSamples:
      required:
        - content
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/ApiRequestSample'
    ApiKeysPage:
      required:
        - content
        - paging
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/ApiKeyDTO'
        paging:
          $ref: '#/components/schemas/PagingResponse'
    TestSetsPage:
      required:
        - content
        - paging
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/TestSetDTO'
        paging:
          $ref: '#/components/schemas/PagingResponse'
    TestSetRunsPage:
      required:
        - content
        - paging
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/TestSetRunDTO'
        paging:
          $ref: '#/components/schemas/PagingResponse'
    ProjectDTO:
      type: object
      required:
        - id
        - name
        - status
        - audit
        - sourcesCount
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        name:
          type: string
        status:
          type: string
        sourcesCount:
          type: integer
        lastIngestedAt:
          type: string
          format: date-time
        audit:
          $ref: '#/components/schemas/AuditDTO'

    ProjectCreateRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
    ProjectUpdateRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
    CancelIngestRequest:
      type: object
      properties:
        reason:
          type: string
    UserQuery:
      type: object
      required:
        - message
      properties:
        message:
          type: string
        segment:
          type: string
    RetrieveChunksRequest:
      type: object
      required:
        - query
      properties:
        query:
          type: string
        topK:
          type: integer
          format: int32
    RetrievedChunksDTO:
      type: object
      required:
        - chunks
      properties:
        chunks:
          type: array
          items:
            $ref: '#/components/schemas/ChunkWithScoreDTO'
    RelevantSourceDTO:
      type: object
      required:
        - id
        - path
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        path:
          type: string
        externalLink:
          type: string
    ChunkWithScoreDTO:
      type: object
      required:
        - score
        - content
        - source
      properties:
        score:
          type: number
          format: double
        content:
          type: string
        summary:
          type: string
        questions:
          type: array
          items:
            type: string
        keywords:
          type: array
          items:
            type: string
        docId:
          deprecated: true
          description: Deprecated. Use source instead.
          type: string
        source:
          $ref: '#/components/schemas/RelevantSourceDTO'
    ChatCreateRequest:
      type: object
      properties:
        name:
          type: string
    ChatDTO:
      type: object
      required:
        - id
        - name
        - audit
        - settings
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        name:
          type: string
        audit:
          $ref: '#/components/schemas/AuditDTO'
    ChatHistoryDTO:
      type: object
      required:
        - records
      properties:
        records:
          type: array
          items:
            $ref: '#/components/schemas/ChatHistoryRecordDTO'
    ChatHistoryRecordDTO:
      type: object
      required:
        - id
        - chatId
        - request
        - status
        - createdAt
        - updatedAt
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        chatId:
          $ref: '#/components/schemas/LongId'
        request:
          type: string
        response:
          type: string
        status:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        relevantSources:
          type: array
          items:
            $ref: '#/components/schemas/RelevantSourceDTO'
    ChatSettingsDTO:
      type: object
      additionalProperties: true
    ProjectSettingsDTO:
      type: object
      additionalProperties: true
    ApiKeyCreateRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
        expiredAt:
          type: string
          format: date-time
    ApiKeyDTO:
      type: object
      required:
        - id
        - name
        - prefix
        - ownerLogin
        - status
        - createdAt
        - updatedAt
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        projectId:
          $ref: '#/components/schemas/LongId'
        name:
          type: string
        prefix:
          type: string
        key:
          type: string
        status:
          type: string
        ownerLogin:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        expiredAt:
          type: string
          format: date-time
        lastUsedAt:
          type: string
          format: date-time

    ProjectFilesSummary:
      type: object
      required:
        - totalFiles
        - totalFilesSizeBytes
      properties:
        totalFiles:
          type: integer
        totalFilesSizeBytes:
          type: integer
        totalFilesSizeChars:
          type: integer

    IntervalUnits:
      type: string
      enum:
        - minutes
        - hours
        - days


    IntegrationDTO:
      type: object
      required:
        - name
        - type
        - settings
      properties:
        name:
          type: string
        autoIngest:
          type: boolean
        autoSync:
          type: boolean
        checkIntervalMinutes:
          type: integer
        checkIntervalUnits:
          $ref: "#/components/schemas/IntervalUnits"
        type:
          type: string
          enum:
            - confluence
            - cloud_confluence
            - minerva
        settings:
          type: object
    JaicpChannelInfoDTO:
      type: object
      required:
        - id
        - name
        - type
        - status
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        name:
          type: string
        type:
          type: string
        status:
          type: string
        externalLink:
          type: string
        description:
          type: string
    ChannelDTO:
      type: object
      required:
        - name
        - type
      properties:
        name:
          type: string
        type:
          $ref: '#/components/schemas/ChannelType'
        settings:
          type: object
    ChannelInfoDTO:
      type: object
      required:
        - id
        - name
        - type
        - status
        - audit
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        name:
          type: string
        type:
          $ref: '#/components/schemas/ChannelType'
        status:
          type: string
        externalLink:
          type: string
        settings:
          type: object
        audit:
          $ref: '#/components/schemas/AuditDTO'
        lastError:
          type: string
    IntegrationShort:
      type: object
      required:
        - id
        - name
        - type
        - status
        - audit
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        name:
          type: string
        type:
          type: string
        status:
          type: string
        audit:
          $ref: '#/components/schemas/AuditDTO'
        lastError:
          type: string
        errorsCount:
          type: integer
    IntegrationFull:
      type: object
      required:
        - id
        - name
        - type
        - status
        - autoIngest
        - autoSync
        - checkIntervalMinutes
        - audit
        - settings
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        name:
          type: string
        type:
          type: string
        status:
          type: string
        audit:
          $ref: '#/components/schemas/AuditDTO'
        settings:
          type: object
        autoIngest:
          type: boolean
        autoSync:
          type: boolean
        checkIntervalMinutes:
          type: integer
        checkIntervalUnits:
          $ref: "#/components/schemas/IntervalUnits"
        lastError:
          type: string
        errorsCount:
          type: integer

    NamedValuesList:
      type: array
      items:
        $ref: '#/components/schemas/NamedValue'
    NamedValue:
      type: object
      required:
        - title
        - value
      properties:
        title:
          type: string
        value:
          type: string
    TestSetOrderDTO:
      type: object
      required:
        - ids
      properties:
        ids:
          type: array
          items:
            type: integer
            format: int64

    GenerateTestSetRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
        generationModel:
          type: string
        generationPrompt:
          type: string
        evaluationModel:
          type: string
        evaluationPrompt:
          type: string
        description:
          type: string
        testsPerDoc:
          type: integer
        maxTests:
          type: integer
    TestSetUpdateRequest:
      type: object
      properties:
        name:
          type: string
        evaluationModel:
          type: string
        evaluationPrompt:
          type: string
    TestSetDTO:
      type: object
      required:
        - id
        - name
        - type
        - status
        - totalTests
        - audit
        - settings
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        name:
          type: string
        description:
          type: string
        type:
          type: string
          enum:
            - manual
            - generated
        status:
          type: string
          enum:
            - READY_TO_PROCESS
            - PROCESSING
            - FAILED
            - CANCELLED
            - ACTIVE
        totalTests:
          type: integer
        maxTests:
          type: integer
        schedule:
          type: string
        settings:
          $ref: '#/components/schemas/TestSetSettingsDTO'
        audit:
          $ref: '#/components/schemas/AuditDTO'
        activeTestSetRun:
          $ref: '#/components/schemas/TestSetRunDTO'
        testSetRunsCount:
          type: integer
    TestSetSettingsDTO:
      type: object
      properties:
        testsPerDocument:
          type: integer
        maxTests:
          type: integer
        generationModel:
          type: string
        generationPrompt:
          type: string
        evaluationModel:
          type: string
        evaluationPrompt:
          type: string
    TestSetRunRequest:
      type: object
      properties:
        autoEvaluate:
          type: boolean
    TestSetRunDTO:
      type: object
      required:
        - id
        - testSetId
        - status
        - totalTests
        - finishedTests
        - evaluationModel
        - createdAt
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        testSetId:
          $ref: '#/components/schemas/LongId'
        totalTests:
          type: integer
        finishedTests:
          type: integer
        totalRequests:
          type: integer
        meanScore:
          type: number
          format: double
        evaluationModel:
          type: string
        processingTimeMs:
          type: integer
          format: int64
        lastError:
          type: string
        status:
          type: string
          enum:
            - READY_TO_PROCESS
            - PROCESSING
            - FINISHED
            - FAILED
            - CANCELLED
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    TestSetScheduleDTO:
      type: object
      required:
        - schedule
        - runOnSettingsChange
        - runOnSourcesChange
      properties:
        schedule:
          type: string
        runOnSettingsChange:
          type: boolean
        runOnSourcesChange:
          type: boolean
  parameters:
    loggingEnabledE2ECheckHeaderOptional:
      name: loggingEnabledE2ECheck
      in: header
      description: enable/disable logging for e2e check
      required: false
      schema:
        type: boolean
        default: false
    Z-requestIdHeaderOptional:
      name: Z-requestId
      in: header
      description: Request id header
      required: false
      schema:
        type: string
    pageNum:
      name: pageNum
      in: query
      required: false
      schema:
        type: integer
        format: int32
        default: 0
    pageSize:
      name: pageSize
      in: query
      required: true
      schema:
        type: integer
        format: int32
        default: 20
    fileSort:
      name: sort
      in: query
      required: false
      schema:
        type: string
        enum:
          - relativePath
          - createdAt
          - status
          - sizeBytes
    integrationSort:
      name: sort
      in: query
      required: false
      schema:
        type: string
        enum:
          - name
          - createdAt
          - status
    direction:
      name: direction
      in: query
      required: false
      schema:
        type: string
        enum:
          - ASC
          - DESC
    statusFilter:
      name: status
      in: query
      required: false
      schema:
        type: string
    testSetIdsFilter:
      name: testSetIds
      in: query
      required: false
      schema:
        type: string
    name:
      name: name
      in: query
      required: false
      schema:
        type: string
    integrationId:
      name: integrationId
      in: path
      description: Integration identifier.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'
    apiKeyId:
      name: apiKeyId
      in: path
      description: Api key identifier.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'
    chatId:
      name: chatId
      in: path
      description: Project identifier.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'
    fileId:
      name: fileId
      in: path
      description: File identifier.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'
    attachmentId:
      name: attachmentId
      in: path
      description: Attachment identifier.
      required: true
      schema:
        type: string
    linkId:
      name: linkId
      in: path
      description: Attachment link identifier.
      required: true
      schema:
        type: string
    version:
      name: version
      in: query
      description: Project data version.
      required: false
      schema:
        type: string
    recordId:
      name: recordId
      in: path
      description: History record identifier.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'
    jaicpChannelId:
      name: jaicpChannelId
      in: path
      description: Jaicp channel identifier.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'
    channelId:
      name: channelId
      in: path
      description: Channel identifier.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'
    projectId:
      name: projectId
      in: path
      description: Project identifier.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'
    testSetId:
      name: testSetId
      in: path
      description: Test set identifier.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'
    testSetRunId:
      name: testSetRunId
      in: path
      description: Test set run identifier.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'
    state:
      name: state
      in: query
      description: State value associated with the user and specific integration to prevent attacks.
      required: true
      schema:
        type: string
    code:
      name: code
      in: query
      description: Authorization code, coming from the OAuth provider.
      required: true
      schema:
        type: string
    accountIdQueryParam:
      name: accountId
      in: query
      description: Account id.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'
    period:
      name: period
      in: query
      description: Report period.
      required: true
      schema:
        type: string
        enum:
          - day
          - week
          - month
          - custom
    dateFrom:
      name: dateFrom
      in: query
      description: Date from.
      required: false
      schema:
        type: string
        format: date
    dateTo:
      name: dateTo
      in: query
      description: Date from.
      required: false
      schema:
        type: string
        format: date
    onlyActive:
      name: onlyActive
      in: query
      required: false
      schema:
        type: boolean
        default: true

  securitySchemes:
    cookieAuth:
      type: apiKey
      in: cookie
      name: CC-SESSION-ID
    bearerAuth:
      type: http
      scheme: bearer
    basicAuth:
      type: http
      scheme: basic

