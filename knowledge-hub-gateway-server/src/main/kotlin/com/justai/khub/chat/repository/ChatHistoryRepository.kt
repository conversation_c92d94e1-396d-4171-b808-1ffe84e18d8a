package com.justai.khub.chat.repository

import com.justai.khub.chat.entity.ChatHistoryRecordEntity
import com.justai.khub.chat.enumeration.ChatHistoryRecordStatus
import com.justai.khub.common.dto.AdditionalGenerationInformation
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.EntityGraph
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface ChatHistoryRepository : JpaRepository<ChatHistoryRecordEntity, Long> {
    @EntityGraph(attributePaths = ["chat", "chat.version"])
    @Query("SELECT c FROM ChatHistoryRecordEntity c WHERE c.chat.id = :chatId AND c.archived = false")
    fun findAllNotArchivedByChatId(chatId: Long, pageable: Pageable): Page<ChatHistoryRecordEntity>

    @EntityGraph(attributePaths = ["chat", "chat.version"])
    fun findByIdAndChatId(id: Long, chatId: Long): ChatHistoryRecordEntity?

    @Modifying
    @Query("DELETE FROM ChatHistoryRecordEntity c WHERE c.chat.id = :chatId")
    fun deleteAllByChatId(chatId: Long)

    @Modifying
    @Query(
        "UPDATE ChatHistoryRecordEntity c " +
            "SET c.status=:newStatus, c.updatedAt=:updatedAt, c.lastLockedBy = :lastLockedBy " +
            "WHERE c.id = :id AND c.status=:expectedStatus"
    )
    fun startProcessing(
        id: Long,
        expectedStatus: ChatHistoryRecordStatus,
        newStatus: ChatHistoryRecordStatus,
        lastLockedBy: String,
        updatedAt: LocalDateTime = LocalDateTime.now()
    ): Int

    @Modifying
    @Query(
        "UPDATE ChatHistoryRecordEntity c " +
            "SET c.status=:newStatus, c.response=:response, c.updatedAt=:updatedAt, c.usedPromptTokens=:promptTokens, c.usedCompletionTokens=:completionTokens, c.addInfo=:additionalInformation " +
            "WHERE c.id = :id AND c.status=:expectedStatus"
    )
    fun updateStatusAndResponse(
        id: Long,
        expectedStatus: ChatHistoryRecordStatus,
        newStatus: ChatHistoryRecordStatus,
        response: String,
        promptTokens: Int? = null,
        completionTokens: Int? = null,
        additionalInformation: AdditionalGenerationInformation? = null,
        updatedAt: LocalDateTime = LocalDateTime.now()
    ): Int

    @Modifying
    @Query(
        "UPDATE ChatHistoryRecordEntity c " +
            "SET c.status=:newStatus, c.updatedAt=:updatedAt " +
            "WHERE c.status = :oldStatus AND c.lastLockedBy = :lastLockedBy"
    )
    fun updateByStatusAndLastLockedBy(
        oldStatus: ChatHistoryRecordStatus,
        newStatus: ChatHistoryRecordStatus,
        lastLockedBy: String,
        updatedAt: LocalDateTime = LocalDateTime.now()
    ): Int


    @Modifying
    @Query(
        "UPDATE ChatHistoryRecordEntity c " +
            "SET c.status=:newStatus, c.updatedAt=:updatedAt " +
            "WHERE c.status = :oldStatus AND c.updatedAt = :lastUpdateBefore"
    )
    fun releaseTimedOutRecords(
        oldStatus: ChatHistoryRecordStatus,
        newStatus: ChatHistoryRecordStatus,
        lastUpdateBefore: LocalDateTime,
        updatedAt: LocalDateTime = LocalDateTime.now()
    ): Int

    @EntityGraph(attributePaths = ["chat", "chat.version"])
    fun findAllByStatusAndArchived(status: ChatHistoryRecordStatus, archived: Boolean, pageable: Pageable): List<ChatHistoryRecordEntity>

    @Modifying
    @Query(
        "UPDATE ChatHistoryRecordEntity c " +
            "SET c.archived=true " +
            "WHERE c.chat.id = :chatId"
    )
    fun setArchivedByChatId(chatId: Long)
}
