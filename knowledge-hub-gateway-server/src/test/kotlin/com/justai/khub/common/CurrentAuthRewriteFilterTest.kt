package com.justai.khub.common

import com.justai.khub.common.configuration.dto.KHubUser
import com.justai.khub.common.configuration.filter.CurrentAuthRewriteFilter
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.util.JSON
import com.justai.khub.util.AuthUtils
import com.justai.security.session.auth.JustSessionAuthentication
import com.justai.security.session.constants.XSRF_TOKEN_HEADER_NAME
import jakarta.servlet.FilterChain
import jakarta.servlet.http.Cookie
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.ArgumentCaptor
import org.mockito.ArgumentMatchers.any
import org.mockito.Mock
import org.mockito.Mockito.*
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.junit.jupiter.MockitoSettings
import org.mockito.quality.Strictness
import org.springframework.security.core.context.SecurityContext
import org.springframework.security.core.context.SecurityContextHolder
import java.io.ByteArrayOutputStream
import java.io.PrintWriter
import java.nio.charset.StandardCharsets

@ExtendWith(MockitoExtension::class)
@MockitoSettings(strictness = Strictness.LENIENT)
class CurrentAuthRewriteFilterTest {

    @Mock
    private lateinit var request: HttpServletRequest

    @Mock
    private lateinit var response: HttpServletResponse
    private lateinit var responseOutput: ByteArrayOutputStream

    @Mock
    private lateinit var filterChain: FilterChain

    @Mock
    private lateinit var securityContext: SecurityContext

    private lateinit var justSessionAuthentication: JustSessionAuthentication
    private lateinit var filter: CurrentAuthRewriteFilter

    companion object {
        private const val XSRF_TOKEN_VALUE = "xsrf-token-value"
        private const val REQUEST_URI = "/api/test"
        private const val REQUEST_METHOD = "GET"
    }

    @BeforeEach
    fun setUp() {
        SecurityContextHolder.setContext(securityContext)

        `when`(request.requestURI).thenReturn(REQUEST_URI)
        `when`(request.method).thenReturn(REQUEST_METHOD)

        val xsrfCookie = mock(Cookie::class.java)
        `when`(xsrfCookie.name).thenReturn(XSRF_TOKEN_HEADER_NAME)
        `when`(xsrfCookie.value).thenReturn(XSRF_TOKEN_VALUE)
        `when`(request.cookies).thenReturn(arrayOf(xsrfCookie))

        responseOutput = ByteArrayOutputStream()
        `when`(response.writer).thenReturn(PrintWriter(responseOutput, true))


        justSessionAuthentication = AuthUtils.mockCCUser(
            customFeatures = listOf("feature1", "feature2"),
            customPermissions = listOf("permission1", "permission2")
        )
    }

    @Test
    fun `when no features or permissions required, should pass through filter`() {
        filter = CurrentAuthRewriteFilter(null, null)
        `when`(securityContext.authentication).thenReturn(justSessionAuthentication)

        filter.doFilterInternal(request, response, filterChain)

        verify(filterChain).doFilter(request, response)
        val authCaptor = ArgumentCaptor.forClass(KHubUser::class.java)
        verify(securityContext).authentication = authCaptor.capture()

        val capturedAuth = authCaptor.value
        assertEquals(justSessionAuthentication.userData.accountId, capturedAuth.accountId)
        assertEquals(justSessionAuthentication.userData.userId, capturedAuth.userId)
        assertEquals(justSessionAuthentication.sessionId, capturedAuth.sessionId)
        assertEquals(XSRF_TOKEN_VALUE, capturedAuth.xsrfToken)
    }

    @Test
    fun `when required features present, should pass through filter`() {
        filter = CurrentAuthRewriteFilter("feature1,feature2", null)
        `when`(securityContext.authentication).thenReturn(justSessionAuthentication)

        filter.doFilterInternal(request, response, filterChain)

        verify(filterChain).doFilter(request, response)
    }

    @Test
    fun `when required features missing, should block and return error`() {
        filter = CurrentAuthRewriteFilter("feature1,feature3", null)
        `when`(securityContext.authentication).thenReturn(justSessionAuthentication)

        filter.doFilterInternal(request, response, filterChain)

        verify(filterChain, never()).doFilter(any(), any())

        val capturedErrors = JSON.parseObjectNode(responseOutput.toString(StandardCharsets.UTF_8))
        assertEquals(ApiErrorCode.MISSING_REQUIRED_FEATURES.code, capturedErrors.get("error").textValue())
    }

    @Test
    fun `when required permissions present, should pass through filter`() {
        filter = CurrentAuthRewriteFilter(null, "permission1,permission2")
        `when`(securityContext.authentication).thenReturn(justSessionAuthentication)

        filter.doFilterInternal(request, response, filterChain)

        verify(filterChain).doFilter(request, response)
    }

    @Test
    fun `when required permissions missing, should block and return error`() {
        filter = CurrentAuthRewriteFilter(null, "permission1,permission3")
        `when`(securityContext.authentication).thenReturn(justSessionAuthentication)

        filter.doFilterInternal(request, response, filterChain)

        verify(filterChain, never()).doFilter(any(), any())

        val capturedErrors = JSON.parseObjectNode(responseOutput.toString(StandardCharsets.UTF_8))
        assertEquals(ApiErrorCode.ACCESS_DENIED.code, capturedErrors.get("error").textValue())
    }

    @Test
    fun `when authentication is not JustSessionAuthentication, should pass through filter`() {
        filter = CurrentAuthRewriteFilter("feature1", "permission1")
        val otherAuth = mock(org.springframework.security.core.Authentication::class.java)
        `when`(securityContext.authentication).thenReturn(otherAuth)

        filter.doFilterInternal(request, response, filterChain)

        verify(filterChain).doFilter(request, response)
        verify(securityContext, never()).authentication = any()
    }


    @Test
    fun `should handle empty feature and permission strings correctly`() {
        filter = CurrentAuthRewriteFilter("", "")
        `when`(securityContext.authentication).thenReturn(justSessionAuthentication)

        filter.doFilterInternal(request, response, filterChain)

        verify(filterChain).doFilter(request, response)
    }

    @Test
    fun `should trim whitespace in features and permissions`() {
        filter = CurrentAuthRewriteFilter(" feature1 , feature2 ", " permission1 , permission2 ")
        `when`(securityContext.authentication).thenReturn(justSessionAuthentication)

        filter.doFilterInternal(request, response, filterChain)

        verify(filterChain).doFilter(request, response)
    }

    @Test
    fun `should handle missing XSRF token`() {
        filter = CurrentAuthRewriteFilter(null, null)
        `when`(securityContext.authentication).thenReturn(justSessionAuthentication)
        `when`(request.cookies).thenReturn(arrayOf()) // No cookies

        filter.doFilterInternal(request, response, filterChain)

        val authCaptor = ArgumentCaptor.forClass(KHubUser::class.java)
        verify(securityContext).authentication = authCaptor.capture()
        assertEquals(null, authCaptor.value.xsrfToken)

        verify(filterChain).doFilter(request, response)
    }
}
