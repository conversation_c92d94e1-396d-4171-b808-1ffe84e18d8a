package com.justai.khub.integration

import com.justai.khub.BaseProjectIT
import com.justai.khub.integration.dto.ConfluenceIntegrationSettings
import com.justai.khub.util.TestDataFactory
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.springframework.test.context.TestPropertySource

@TestPropertySource(properties = ["scheduling.integration.enabled=true"])
class IntegrationErrorsIT : BaseProjectIT() {

    @Test
    fun testErrorsCountUpdate() {
        val createdIntegration = integrationHelper.createIntegration(project.id)
        assertEquals(0, createdIntegration.errorsCount)
        integrationHelper.syncIntegration(project.id, createdIntegration.id)
        Thread.sleep(5000)
        val foundIntegration = integrationHelper.getIntegrationAndExpectOk(project.id, createdIntegration.id)
        assertTrue((foundIntegration.errorsCount ?: 0) > 0)
        val updateRequest = TestDataFactory.integrationDTO(foundIntegration.name)
            .copy(settings = ConfluenceIntegrationSettings("unused", "unused", "unused"))
        val updatedIntegration = integrationHelper.updateIntegrationAndExpectOk(project.id, createdIntegration.id, updateRequest)
        assertEquals(0, updatedIntegration.errorsCount)
    }
}
