package com.justai.khub.attachment.repository

import com.justai.khub.attachment.entity.AttachmentEntity
import org.springframework.data.jpa.repository.EntityGraph
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface AttachmentRepository : JpaRepository<AttachmentEntity, Long> {

    @Modifying
    @Query("DELETE FROM AttachmentEntity a WHERE a.file.id = :fileId")
    fun deleteByFileId(fileId: Long)

    fun findByExternalId(attachmentId: String): AttachmentEntity?

    @EntityGraph(attributePaths = ["file", "file.version"])
    @Query("SELECT a FROM AttachmentEntity a WHERE a.externalId in :attachmentIds")
    fun findAllByExternalIds(attachmentIds: Iterable<String>): List<AttachmentEntity>

}
