package com.justai.khub.integration

import com.justai.khub.integration.dto.ConfluenceIntegrationSettings
import com.justai.khub.integration.entity.IntegrationEntity
import com.justai.khub.project.enumeration.ProjectSourceStatus
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.kotlin.clearInvocations
import org.springframework.test.context.TestPropertySource

@TestPropertySource(properties = ["scheduling.ingest.enabled=false"])
class ConfluenceCheckerIT : BaseConfluenceIT() {

    @Test
    fun checkConfluencePageWithAttachments() {
        val integration = doIntegration()
        verifyIntegration(
            integration = integration,
            pageWithAttachments = confluenceTestData.pageWithAttachments,
            pageAddCount = 1,
            pageIngestDeleteCount = 0,
            attachmentsAddNewVersionCount = 1,
            attachmentDeletePreviousVersionCount = 0,
            pagesStatus = ProjectSourceStatus.INGESTED,
            attachmentsStatus = ProjectSourceStatus.INGESTED,
        )
    }

    @Test
    fun checkNoUpdatesForIntegration() {
        val integration = doIntegration()
        repeat(3) {
            poolConfluenceIntegration(integration)
            runBlocking { delay(100) }
        }
        verifyIntegration(
            integration = integration,
            pageWithAttachments = confluenceTestData.pageWithAttachments,
            pageAddCount = 1,
            pageIngestDeleteCount = 0,
            attachmentsAddNewVersionCount = 1,
            attachmentDeletePreviousVersionCount = 0,
            pagesStatus = ProjectSourceStatus.INGESTED,
            attachmentsStatus = ProjectSourceStatus.INGESTED,
        )
    }

    @Test
    fun checkUpdateOnlyPage() {
        val integration = doIntegration()
        clearInvocations(confluenceRestTemplate, lakefsConnector, unbufferedRestTemplate)
        val updatedPageWithAttachments = updatePageContent("simple new text")
        poolConfluenceIntegration(integration)
        verifyIntegration(
            integration = integration,
            pageWithAttachments = updatedPageWithAttachments,
            pageAddCount = 1,
            pageIngestDeleteCount = 1,
            attachmentsAddNewVersionCount = 0,
            attachmentDeletePreviousVersionCount = 0,
            pagesStatus = ProjectSourceStatus.NOT_INGESTED,
            attachmentsStatus = ProjectSourceStatus.INGESTED,
        )
    }

    @Test
    fun checkUpdateAttachments() {
        val integration = doIntegration()
        clearInvocations(confluenceRestTemplate, lakefsConnector, unbufferedRestTemplate)
        val updatedPageWithAttachments = updateAttachments()
        poolConfluenceIntegration(integration)
        verifyIntegration(
            integration = integration,
            pageWithAttachments = updatedPageWithAttachments,
            pageAddCount = 0,
            pageIngestDeleteCount = 1,
            attachmentsAddNewVersionCount = 1,
            attachmentDeletePreviousVersionCount = 1,
            pagesStatus = ProjectSourceStatus.NOT_INGESTED,
            attachmentsStatus = ProjectSourceStatus.NOT_INGESTED,
        )
    }

    private fun doIntegration(): IntegrationEntity {
        val settings = ConfluenceIntegrationSettings(
            baseUrl = confluenceBaseUrl,
            space = confluenceTestData.space.key,
            token = "bestever"
        )
        val createdIntegration = integrationHelper.createIntegration(
            projectId = project.id,
            settings = settings,
            autoIngest = false,
            autoSync = false
        )
        val integrationEntity = integrationRepository.findById(createdIntegration.id).get()
        poolConfluenceIntegration(integrationEntity)
        imitateIngestion(integrationEntity)
        return integrationEntity
    }

    private fun poolConfluenceIntegration(integration: IntegrationEntity) {
        runBlocking {
            confluenceIntegrationChecker.checkIntegration(
                integration,
                integration.settings as ConfluenceIntegrationSettings
            )
        }
    }

}
