package com.justai.khub.project.validator

import com.justai.khub.common.configuration.dto.IngestProperties
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.project.entity.ProjectFileEntity
import com.justai.khub.project.enumeration.ProjectFileType
import com.justai.khub.project.repository.ProjectFileRepository
import org.apache.commons.io.FilenameUtils
import org.springframework.stereotype.Service

@Service
class ProjectFileValidator(
    private val ingestProperties: IngestProperties,
    private val projectFileRepository: ProjectFileRepository,
) {

    fun validate(newFile: ProjectFileEntity) {
        val existedFile = projectFileRepository.findByVersionIdAndRelativePath(newFile.version.id, newFile.relativePath)
        if (existedFile != null && existedFile.id != newFile.id) {
            throw KhubException(ApiErrorCode.DUPLICATE_PROJECT_FILE, mapOf("name" to newFile.relativePath))
        }
        val extension = FilenameUtils.getExtension(newFile.relativePath)
        if (!ingestProperties.supportedExtensions.contains(extension.lowercase()) && newFile.fileType == ProjectFileType.DOCUMENT) {
            throw KhubException(ApiErrorCode.UNSUPPORTED_EXTENSION, mapOf("name" to newFile.relativePath))
        }
        checkMaxCsvFiles(newFile)
    }

    private fun checkMaxCsvFiles(newFile: ProjectFileEntity) {
        if (!newFile.relativePath.endsWith(".csv")) {
            return
        }
        val existedCsvFilesCount = projectFileRepository.countByVersionIdAndExtension(newFile.version.id, ".csv")
        if (existedCsvFilesCount >= CSV_FILES_LIMIT) {
            throw KhubException(ApiErrorCode.TOO_MANY_CSV_FILES, mapOf("limit" to "$CSV_FILES_LIMIT"))
        }
    }

    companion object {
        val CSV_FILES_LIMIT = 10
    }
}
