package com.justai.khub.channel.dto

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(PropertyNamingStrategies.LowerCamelCaseStrategy::class)
data class ProjectSettingsData(
    val language: String = "ru",
    val classificationAlgorithm: String = "sts",
    val spellingCorrection: Boolean = false,
    val shared: Boolean = false,
    val timezone: String = "UTC"
)

@JsonNaming(PropertyNamingStrategies.LowerCamelCaseStrategy::class)
data class BotProjectModificationDto(
    val name: String,
    val templateParams: Map<Any, Any>,
    val defaultBranch: String? = "master",
    val template: String = "khub-default-template",
    val projectSettingsData: ProjectSettingsData? = ProjectSettingsData()
)
