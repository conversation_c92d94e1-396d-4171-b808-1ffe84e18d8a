package com.justai.khub.util

import com.justai.khub.api.public.model.AddLinkRequest
import com.justai.khub.api.public.model.AddTextRequest
import com.justai.khub.api.public.model.ProjectSources
import com.justai.khub.api.public.model.SourceDTO
import com.justai.khub.common.util.JSON
import com.justai.khub.util.WebUtils.parseTo
import org.apache.commons.lang3.RandomStringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.mock.web.MockMultipartFile
import org.springframework.stereotype.Component
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.ResultActions
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import org.springframework.util.LinkedMultiValueMap
import java.nio.charset.StandardCharsets

@Component
class ProjectSourcePublicHelper {

    @Autowired
    protected lateinit var mvc: MockMvc

    fun getProjectSources(params: Map<String, String> = mapOf(), apiKey: String): ProjectSources {
        return mvc.perform(
            get("/api/knowledge-hub/sources")
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization", "Bearer $apiKey")
                .queryParams(LinkedMultiValueMap(params.mapValues { mutableListOf(it.value) }.toMutableMap()))
        ).andExpect(status().isOk).andReturn().parseTo<ProjectSources>()
    }

    fun downloadProjectSource(sourceId: Long, apiKey: String? = ""): String {
        val result = mvc.perform(
            get("/api/knowledge-hub/sources/$sourceId/download")
                .header("Authorization", "Bearer $apiKey")
        )
            .andExpect(status().isOk)
            .andReturn()
        return String(result.response.contentAsByteArray, StandardCharsets.UTF_8)
    }

    fun addLinkSourceAndExpectSuccess(request: AddLinkRequest, apiKey: String): SourceDTO {
        return addLinkSource(request, apiKey)
            .andExpect(status().isOk)
            .andReturn().parseTo<SourceDTO>()
    }

    fun addLinkSource(request: AddLinkRequest, apiKey: String): ResultActions {
        return mvc.perform(
            post("/api/knowledge-hub/sources/links")
                .header("Authorization", "Bearer $apiKey")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
        )
    }

    fun updateLinkSourceAndExpectSuccess(request: AddLinkRequest, apiKey: String): SourceDTO {
        return updateLinkSource(request, apiKey)
            .andExpect(status().isOk)
            .andReturn().parseTo<SourceDTO>()
    }

    fun updateLinkSource(request: AddLinkRequest, apiKey: String): ResultActions {
        return mvc.perform(
            put("/api/knowledge-hub/sources/links")
                .header("Authorization", "Bearer $apiKey")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
        )
    }

    fun addTextSourceAndExpectSuccess(request: AddTextRequest, apiKey: String): SourceDTO {
        return addTextSource(request, apiKey)
            .andExpect(status().isOk)
            .andReturn().parseTo<SourceDTO>()
    }

    fun addTextSource(request: AddTextRequest, apiKey: String): ResultActions {
        return mvc.perform(
            post("/api/knowledge-hub/sources/texts")
                .header("Authorization", "Bearer $apiKey")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
        )
    }

    fun updateTextSourceAndExpectSuccess(request: AddTextRequest, apiKey: String): SourceDTO {
        return updateTextSource(request, apiKey)
            .andExpect(status().isOk)
            .andReturn().parseTo<SourceDTO>()
    }

    fun updateTextSource(request: AddTextRequest, apiKey: String): ResultActions {
        return mvc.perform(
            put("/api/knowledge-hub/sources/texts")
                .header("Authorization", "Bearer $apiKey")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
        )
    }

    fun addFileSourceAndExpectSuccess(
        fileName: String = "test-file-${RandomStringUtils.randomAlphabetic(5)}.txt",
        fileContent: String = RandomStringUtils.randomAlphabetic(20),
        apiKey: String
    ): SourceDTO {
        val mockFile = MockMultipartFile(
            "file", fileName,
            MediaType.TEXT_PLAIN_VALUE, fileContent.toByteArray()
        )
        return mvc.perform(
            multipart("/api/knowledge-hub/sources/files")
                .file(mockFile)
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .header("Authorization", "Bearer $apiKey")
        )
            .andExpect(status().isOk())
            .andReturn().parseTo<SourceDTO>()
    }

    fun updateFileSourceAndExpectSuccess(
        fileName: String = "test-file-${RandomStringUtils.randomAlphabetic(5)}.txt",
        fileContent: String = RandomStringUtils.randomAlphabetic(20),
        apiKey: String
    ): SourceDTO {
        return updateFileSource(fileName, fileContent, apiKey)
            .andExpect(status().isOk())
            .andReturn().parseTo<SourceDTO>()
    }

    fun updateFileSource(
        fileName: String = "test-file-${RandomStringUtils.randomAlphabetic(5)}.txt",
        fileContent: String = RandomStringUtils.randomAlphabetic(20),
        apiKey: String
    ): ResultActions {
        val mockFile = MockMultipartFile(
            "file", fileName,
            MediaType.TEXT_PLAIN_VALUE, fileContent.toByteArray()
        )
        return mvc.perform(
            multipart(HttpMethod.PUT, "/api/knowledge-hub/sources/files")
                .file(mockFile)
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .header("Authorization", "Bearer $apiKey")
        )
    }

    fun deleteProjectSource(sourceId: Long, apiKey: String) {
        mvc.perform(
            delete("/api/knowledge-hub/sources/$sourceId")
                .header("Authorization", "Bearer $apiKey")
        )
            .andExpect(status().isOk)
            .andReturn()
    }
}
