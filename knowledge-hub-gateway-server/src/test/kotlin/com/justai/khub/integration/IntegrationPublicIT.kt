package com.justai.khub.integration

import com.justai.khub.BaseProjectIT
import com.justai.khub.util.TestPublicDataFactory.confluenceIntegrationSettings
import com.justai.khub.util.TestPublicDataFactory.integrationCreateRequest
import org.hamcrest.CoreMatchers.containsString
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

class IntegrationPublicIT : BaseProjectIT() {

    private lateinit var projectApiKey: String

    @BeforeEach
    fun init() {
        projectApiKey = apiKeyHelper.createApiKey(project.id).key!!
    }

    @Test
    fun testConfluenceSettingsValidation() {
        val validSettings = confluenceIntegrationSettings()
        val invalidRequests = mapOf(
            "name" to integrationCreateRequest().copy(name = ""),
            "space" to integrationCreateRequest().copy(settings = validSettings.copy(space = "")),
            "baseUrl" to integrationCreateRequest().copy(settings = validSettings.copy(baseUrl = "")),
            "token" to integrationCreateRequest().copy(settings = validSettings.copy(token = ""))
        )

        invalidRequests.forEach { (missingPropertyName, invalidRequest) ->
            integrationPublicHelper.createIntegration(invalidRequest, apiKey = projectApiKey)
                .andExpect(status().isBadRequest)
                .andExpect(jsonPath("$.message", containsString(missingPropertyName)))
        }
    }

    @Test
    fun testIntegrationAPI() {
        val initialIntegrations = integrationPublicHelper.getAllIntegrations(apiKey = projectApiKey)
        assertTrue(initialIntegrations.isEmpty())

        integrationPublicHelper.createIntegrationAndExpectSuccess(integrationCreateRequest(), apiKey = projectApiKey)
        integrationPublicHelper.createIntegrationAndExpectSuccess(integrationCreateRequest(), apiKey = projectApiKey)
        val integrationsAfterCreate = integrationPublicHelper.getAllIntegrations(apiKey = projectApiKey)
        assertEquals(2, integrationsAfterCreate.size)

        integrationPublicHelper.deleteIntegration(integrationsAfterCreate.first().id, apiKey = projectApiKey)
        val integrationsAfterDelete = integrationPublicHelper.getAllIntegrations(apiKey = projectApiKey)
        assertEquals(1, integrationsAfterDelete.size)

        val integrationById = integrationPublicHelper.getIntegration(integrationsAfterDelete.first().id, apiKey = projectApiKey)
        assertEquals(integrationsAfterDelete.first().id, integrationById.id)
    }
}
