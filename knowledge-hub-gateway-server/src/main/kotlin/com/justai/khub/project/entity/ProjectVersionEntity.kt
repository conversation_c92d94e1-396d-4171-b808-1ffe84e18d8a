package com.justai.khub.project.entity

import com.justai.khub.common.entity.AuditableEntity
import com.justai.khub.common.entity.CCUserEntity
import com.justai.khub.project.dto.IngestPipelineConfig
import com.justai.khub.project.dto.SearchPipelineConfig
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.*
import org.hibernate.annotations.Type
import java.time.LocalDateTime

@Entity
@Table(name = "project_version")
class ProjectVersionEntity : AuditableEntity() {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = -1

    @ManyToOne
    lateinit var project: ProjectEntity

    @Column(nullable = false)
    lateinit var name: String

    @Type(JsonType::class)
    @Column(columnDefinition = "jsonb")
    lateinit var ingestSettings: IngestPipelineConfig

    @Type(JsonType::class)
    @Column(columnDefinition = "jsonb")
    lateinit var searchSettings: SearchPipelineConfig

    @Column
    var settingsUpdatedAt: LocalDateTime? = null

    @Column
    var sourcesUpdatedAt: LocalDateTime? = null

    @Column
    var deletedAt: LocalDateTime? = null

    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "accountId", column = Column(name = "deleted_by_account_id")),
        AttributeOverride(name = "userId", column = Column(name = "deleted_by_user_id"))
    )
    var deletedBy: CCUserEntity? = null
}
