package com.justai.khub.integration.dto

data class MinervaResponse(
    val number: Int,
    val numberOfElements: Int,
    val totalElements: Int,
    val totalPages: Int,
    val size: Int,
    val content: List<ContentItem>
)

data class ContentItem(
    val id: Int,
    val type: String,
    val name: String,
    val description: String,
    val date: Long,
    val folder: Folder,
    val categories: List<List<Category>>,
    val content: String? = null
)

data class Folder(
    val id: Int,
    val name: String
)

data class Category(
    val externalId: String,
    val name: String
)

data class MinervaArticleResponse(
    val id: Int,
    val type: String,
    val name: String,
    val description: String,
    val date: Long,
    val folder: Folder,
    val categories: List<List<Category>>,
    val content: String
)
