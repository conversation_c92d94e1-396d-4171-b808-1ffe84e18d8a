package com.justai.khub.common.service

import com.justai.khub.common.dto.WithIntegrationTimer
import org.aopalliance.intercept.MethodInterceptor
import org.springframework.aop.framework.ProxyFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.config.BeanPostProcessor
import org.springframework.context.annotation.Lazy
import org.springframework.core.annotation.AnnotationUtils
import org.springframework.stereotype.Component
import java.util.function.Supplier

@Component
class WithIntegrationTimerBeanPostProcessor : BeanPostProcessor {
    // WARNING: It is crucial to use lazy initialization to ensure the metrics system is fully initialized.
    // Without this, some metrics (such as system_cpu_usage) would not be exported.
    @Lazy
    @Autowired
    lateinit var metricsService: MetricsService

    override fun postProcessAfterInitialization(bean: Any, beanName: String): Any? {
        val annotation = AnnotationUtils.findAnnotation(bean::class.java, WithIntegrationTimer::class.java) ?: return bean
        return ProxyFactory(bean).apply {
            addAdvice(MethodInterceptor {
                val timer = metricsService.integrationTimer(annotation.integrationName, it.method.name)
                try {
                    timer.record(Supplier { it.proceed() })
                } catch (ex: Exception) {
                    metricsService.incrementIntegrationErrorsCounter(annotation.integrationName, it.method.name)
                    throw ex
                }
            })
        }.proxy
    }
}
