package com.justai.khub.chat.endpoint

import com.justai.khub.api.fe.ChatApiService
import com.justai.khub.api.fe.model.*
import com.justai.khub.chat.mapper.ChatMapper
import com.justai.khub.chat.service.ChatHistoryService
import com.justai.khub.chat.service.ChatService
import com.justai.khub.common.util.JSON
import com.justai.khub.common.util.WebUtils
import com.justai.khub.common.util.WebUtils.getCurrentUser
import com.justai.khub.project.dto.UISettings
import com.justai.khub.project.mapper.ProjectSettingsMapper
import com.justai.khub.project.mapper.ProjectVersionMapper
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service

@Service
class ChatEndpoint(
    private val chatService: ChatService,
    private val chatHistoryService: ChatHistoryService,
    private val projectSettingsMapper: ProjectSettingsMapper,
    private val chatMapper: ChatMapper
) : ChatApiService {

    override fun cancelRecordProcessing(projectId: Long, chatId: Long, recordId: Long): ChatHistoryRecordDTO {
        val currentAccountId = WebUtils.getCurrentAccountId()
        val record = chatHistoryService.cancelRecordProcessing(projectId, chatId, recordId, currentAccountId)
        return chatMapper.run { record.toDTO() }
    }

    override fun getDefaultChatHistory(projectId: Long, pageSize: Int, version: String?, pageNum: Int): ChatHistoryPage {
        val pageable = PageRequest.of(pageNum, pageSize, Sort.by(Sort.Direction.DESC, "createdAt"))
        val currentAccountId = WebUtils.getCurrentAccountId()
        val actualVersion = version ?: ProjectVersionMapper.DEFAULT_VERSION
        val page = chatHistoryService.getDefaultChatHistory(projectId, actualVersion, currentAccountId, pageable)
        return chatMapper.toHistoryPage(page)
    }

    override fun clearDefaultChatHistory(projectId: Long, version: String?): ChatHistoryPage {
        val currentAccountId = WebUtils.getCurrentAccountId()
        val actualVersion = version ?: ProjectVersionMapper.DEFAULT_VERSION
        val page = chatHistoryService.clearDefaultChatHistory(projectId, actualVersion, currentAccountId)
        return chatMapper.toHistoryPage(page)
    }

    override fun getChatHistory(projectId: Long, chatId: Long, pageSize: Int, pageNum: Int): ChatHistoryPage {
        val pageable = PageRequest.of(pageNum, pageSize, Sort.by(Sort.Direction.DESC, "createdAt"))
        val currentAccountId = WebUtils.getCurrentAccountId()
        val page = chatHistoryService.getChatHistory(projectId, chatId, currentAccountId, pageable)
        return chatMapper.toHistoryPage(page)
    }

    override fun clearChatHistory(projectId: Long, chatId: Long): ChatHistoryPage {
        val currentAccountId = WebUtils.getCurrentAccountId()
        val page = chatHistoryService.clearChatHistory(projectId, chatId, currentAccountId)
        return chatMapper.toHistoryPage(page)
    }

    override fun getChatRecord(projectId: Long, chatId: Long, recordId: Long): ChatHistoryRecordDTO {
        val currentAccountId = WebUtils.getCurrentAccountId()
        val record = chatHistoryService.getChatRecord(projectId, chatId, recordId, currentAccountId)
        return chatMapper.run { record.toDTO() }
    }

    override fun getChats(projectId: Long, pageSize: Int, version: String?, pageNum: Int): ChatsPage {
        val pageable = PageRequest.of(pageNum, pageSize, Sort.by(Sort.Direction.ASC, "name"))
        val currentAccountId = WebUtils.getCurrentAccountId()
        val actualVersion = version ?: ProjectVersionMapper.DEFAULT_VERSION
        val page = chatService.getChats(projectId, actualVersion, currentAccountId, pageable)
        return chatMapper.toChatPage(page)
    }

    override fun createChat(projectId: Long, chatCreateRequest: ChatCreateRequest, version: String?): ChatDTO {
        val currentAccountId = WebUtils.getCurrentAccountId()
        val actualVersion = version ?: ProjectVersionMapper.DEFAULT_VERSION
        val chat = chatService.createChat(projectId, actualVersion, chatCreateRequest.name, currentAccountId)
        return chatMapper.run { chat.toDTO() }
    }

    override fun makeRequest(projectId: Long, chatId: Long, userQuery: UserQuery): ChatHistoryRecordDTO {
        val currentAccountId = WebUtils.getCurrentAccountId()
        val record = chatHistoryService.createRecord(
            projectId,
            chatId,
            userQuery.message,
            userQuery.segment,
            currentAccountId
        )
        return chatMapper.run { record.toDTO() }
    }

    override fun makeRequestToDefaultChat(projectId: Long, userQuery: UserQuery, version: String?): ChatHistoryRecordDTO {
        val currentAccountId = WebUtils.getCurrentAccountId()
        val actualVersion = version ?: ProjectVersionMapper.DEFAULT_VERSION

        val record = chatHistoryService.createRecordInDefaultChat(
            projectId,
            actualVersion,
            userQuery.message,
            userQuery.segment,
            currentAccountId
        )
        return chatMapper.run { record.toDTO() }
    }

    override fun getChatSettings(projectId: Long, chatId: Long): Map<String, Any> {
        val currentUser = getCurrentUser()
        val settings = chatService.getChatSettings(projectId, chatId, currentUser.accountId)
        return projectSettingsMapper.toDTO(settings, currentUser)
    }

    override fun getDefaultChatSettings(projectId: Long, version: String?): Map<String, Any> {
        val currentUser = getCurrentUser()
        val actualVersion = version ?: ProjectVersionMapper.DEFAULT_VERSION
        val settings = chatService.getOrCreateDefaultChatSettings(projectId, actualVersion, currentUser.accountId)
        return projectSettingsMapper.toDTO(settings, currentUser)
    }

    override fun setChatSettings(projectId: Long, chatId: Long, requestBody: Map<String, Any>): Map<String, Any> {
        val currentUser = getCurrentUser()
        val settings = chatService.setChatSettings(projectId, chatId, JSON.parse<UISettings>(JSON.toNode(requestBody)), currentUser.accountId)
        return projectSettingsMapper.toDTO(settings, currentUser)
    }


    override fun setDefaultChatSettings(projectId: Long, requestBody: Map<String, Any>, version: String?): Map<String, Any> {
        val currentUser = getCurrentUser()
        val actualVersion = version ?: ProjectVersionMapper.DEFAULT_VERSION
        val settings = chatService.setDefaultChatSettings(projectId, actualVersion, JSON.parse(JSON.toNode(requestBody)), currentUser.accountId)
        return projectSettingsMapper.toDTO(settings, currentUser)
    }
}
