package com.justai.khub.common.connector

import com.justai.khub.api.fe.model.E2ECheckResult
import com.justai.khub.api.fe.model.E2EComponent
import com.justai.khub.common.configuration.dto.IntegrationProperties
import com.justai.khub.common.dto.ServiceVersionResponse
import com.justai.khub.common.dto.WithIntegrationTimer
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.enumeration.IntegrationName
import com.justai.khub.common.exception.KhubException
import com.justai.khub.ingest.dto.ParsingTaskDTO
import com.justai.khub.ingest.mapper.ParsingServiceMapper
import com.justai.khub.project.dto.FileLink
import com.justai.loadbalancer.annotations.HttpInternal
import org.slf4j.LoggerFactory
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod.GET
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.getForObject
import org.springframework.web.client.postForObject

@Service
@WithIntegrationTimer(integrationName = IntegrationName.parsingService)
class ParsingServiceConnector(
    @HttpInternal private val parsingServiceRestTemplate: RestTemplate,
    integrationProperties: IntegrationProperties
) {
    private val log = LoggerFactory.getLogger(this::class.java)

    val baseUrl = integrationProperties.parser.baseUrl

    fun startParsing(fileLink: FileLink, config: Map<String, String> = mapOf()): ParsingTaskDTO {
        val request = ParsingServiceMapper.toStartParsingRequest(fileLink, config)
        return try {
            parsingServiceRestTemplate.postForObject<ParsingTaskDTO>("${baseUrl}/api/parse", request)
        } catch (ex: Exception) {
            throw KhubException(ApiErrorCode.PARSING_ERROR, ex)
        }
    }

    fun getParsingTask(id: String): ParsingTaskDTO? {
        return try {
            parsingServiceRestTemplate.getForObject<ParsingTaskDTO>("${baseUrl}/api/task/${id}")
        } catch (ex: Exception) {
            if (ex is HttpClientErrorException && ex.statusCode == HttpStatus.NOT_FOUND) {
                return null
            }
            throw KhubException(ApiErrorCode.PARSING_ERROR, ex)
        }
    }

    fun waitTaskFinish(taskToWait: ParsingTaskDTO, timeoutSeconds: Int): ParsingTaskDTO {
        if (taskToWait.isFinished()) return taskToWait
        val timeoutMillis = timeoutSeconds * 1000L
        val startTime = System.currentTimeMillis()

        while (System.currentTimeMillis() - startTime < timeoutMillis) {
            val task = getParsingTask(taskToWait.id) ?: throw KhubException(ApiErrorCode.PARSING_TASK_NOT_FOUND)
            if (task.isFinished()) {
                return task
            }
            Thread.sleep(1000)
        }
        val lastState = getParsingTask(taskToWait.id) ?: throw KhubException(ApiErrorCode.PARSING_TASK_NOT_FOUND)
        if (lastState.isFinished()) {
            return lastState
        } else {
            throw KhubException(ApiErrorCode.PARSING_TIMEOUT)
        }
    }

    fun cancelTask(id: String) {
        try {
            parsingServiceRestTemplate.postForObject<String>("${baseUrl}/api/task/${id}/cancel")
        } catch (ex: Exception) {
            log.error("Error cancelling parsing task: {}", ex.message, ex)
        }
    }

    fun performE2E(requestId: String, loggingEnabledE2ECheck: Boolean): List<E2EComponent> {
        val request = HttpEntity(
            null,
            HttpHeaders().apply {
                set("Z-RequestId", requestId)
                set("loggingEnabledE2ECheck", "$loggingEnabledE2ECheck")
            }
        )
        val result = parsingServiceRestTemplate
            .exchange("${baseUrl}/e2e-check", GET, request, E2ECheckResult::class.java)
        return result.body?.e2EComponents ?: listOf()
    }

    fun getVersion(): ServiceVersionResponse {
        return parsingServiceRestTemplate.getForObject("${baseUrl}/version", ServiceVersionResponse::class.java)!!
    }
}
