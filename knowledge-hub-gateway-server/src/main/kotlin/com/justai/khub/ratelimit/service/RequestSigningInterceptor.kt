package com.justai.khub.ratelimit.service

import com.justai.khub.common.util.CustomHeaders
import org.slf4j.LoggerFactory
import org.springframework.http.HttpRequest
import org.springframework.http.client.ClientHttpRequestExecution
import org.springframework.http.client.ClientHttpRequestInterceptor
import org.springframework.http.client.ClientHttpResponse
import org.springframework.stereotype.Service
import java.time.Clock
import java.util.*

@Service
class RequestSigningInterceptor(
    private val clock: Clock,
    private val licensingService: LicensingService,
    private val signingService: SigningService
) : ClientHttpRequestInterceptor {
    private val log = LoggerFactory.getLogger(this::class.java)

    override fun intercept(request: HttpRequest, body: ByteArray, execution: ClientHttpRequestExecution): ClientHttpResponse {
        if (!licensingService.hasActiveKey()) {
            log.info("License key is missing. Skipping request signature")
            return execution.execute(request, body)
        }
        val headersToSign = listOf(
            CustomHeaders.X_LICENSE_TIMESTAMP to "${clock.millis()}",
            CustomHeaders.X_LICENSE_KEY_ID to licensingService.keyId,
            CustomHeaders.X_LICENSE_REQUEST_ID to (request.headers.getFirst(CustomHeaders.REQUEST_ID) ?: UUID.randomUUID().toString()),
        )

        val signature = signingService.signHeaders(headersToSign)

        headersToSign.forEach { request.headers.add(it.first, it.second) }
        request.headers.add(CustomHeaders.X_LICENSE_SIGNATURE, signature)

        log.info(
            "Adding license headers: {}={}, {}={}, {}={}, {}={}",
            CustomHeaders.X_LICENSE_TIMESTAMP, request.headers.getFirst(CustomHeaders.X_LICENSE_TIMESTAMP),
            CustomHeaders.X_LICENSE_KEY_ID, request.headers.getFirst(CustomHeaders.X_LICENSE_KEY_ID),
            CustomHeaders.X_LICENSE_REQUEST_ID, request.headers.getFirst(CustomHeaders.X_LICENSE_REQUEST_ID),
            CustomHeaders.X_LICENSE_SIGNATURE, request.headers.getFirst(CustomHeaders.X_LICENSE_SIGNATURE),
        )
        return execution.execute(request, body)
    }
}
