package com.justai.khub.common.service.e2e

import com.justai.e2echeck.E2ECheck.STATUS_OK
import com.justai.e2echeck.E2EComponentDTO
import com.justai.khub.common.connector.EvaluationServiceConnector
import com.justai.khub.common.dto.ServiceVersion
import com.justai.khub.common.mapper.CommonMapper.toDTO
import com.justai.khub.common.mapper.CommonMapper.toDto
import org.springframework.stereotype.Component
import java.time.Instant

@Component
class EvaluationServiceE2EChecker(
    private val evaluationServiceConnector: EvaluationServiceConnector
) : E2EChecker(NAME) {
    override fun performE2E(requestId: String, loggingEnabledE2ECheck: Boolean): List<E2EComponentDTO> {
        val startedAt = Instant.now()
        val ragServiceChecks = evaluationServiceConnector.performE2E(requestId, loggingEnabledE2ECheck).toDto()
        val processingTime = Instant.now().toEpochMilli() - startedAt.toEpochMilli()
        return ragServiceChecks + E2EComponentDTO(formatE2ECheckName(), STATUS_OK, startedAt.toString(), processingTime)
    }

    override fun getVersion(): List<ServiceVersion> {
        return listOf(evaluationServiceConnector.getVersion().toDTO(NAME))
    }

    companion object {
        const val NAME = "knowledge-hub-evaluator"
    }
}
