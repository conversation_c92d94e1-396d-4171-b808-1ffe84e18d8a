package com.justai.khub.qa.dto

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.justai.khub.common.dto.rag.ChunkResponse
import com.justai.khub.project.dto.LLMConfig

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class TestEvaluationRequest(
    val prompt: String,
    val llm: LLMConfig,
    val question: String,
    val groundTruthAnswer: String,
    val actualAnswer: String,
    val usedChunks: List<ChunkResponse>,
)
