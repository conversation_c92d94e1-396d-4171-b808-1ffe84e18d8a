CREATE TABLE public.channel
(
    id                     BIGINT GE<PERSON>RATED ALWAYS AS IDENTITY PRIMARY KEY,
    version_id             BIGINT       NOT NULL REFERENCES public.project_version (id),
    name                   VARCHAR(512) NOT NULL,
    status                 VARCHAR(255) NOT NULL,
    settings               JSONB        NOT NULL,

    created_at             TIMESTAMP    NOT NULL,
    created_by_account_id  BIGINT       NOT NULL,
    created_by_user_id     BIGINT,
    updated_at             TIMESTAMP    NOT NULL,
    updated_by_account_id  BIGINT       NOT NULL,
    updated_by_user_id     BIGINT
);

ALTER SEQUENCE public.channel_id_seq RESTART WITH 16870;
CREATE INDEX idx_channel_version_id ON public.channel (version_id);
CREATE INDEX idx_channel_version_id_status ON public.channel (version_id, status);
CREATE INDEX idx_channel_created_by_account_id ON public.channel (created_by_account_id);

