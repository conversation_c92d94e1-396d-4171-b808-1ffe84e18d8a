package com.justai.khub.util

import com.justai.khub.api.fe.model.ApiKeyCreateRequest
import com.justai.khub.api.fe.model.GenerateTestSetRequest
import com.justai.khub.api.fe.model.IntegrationDTO
import com.justai.khub.api.fe.model.ProjectCreateRequest
import com.justai.khub.billing.dto.TariffUpdateDto
import com.justai.khub.common.dto.rag.ChunkResponse
import com.justai.khub.common.dto.rag.RetrieveChunksResponse
import com.justai.khub.integration.dto.*
import com.justai.onprem.license.KHubModuleV1
import com.justai.onprem.license.KeyV1
import com.justai.onprem.license.RSAKeyUtil
import com.nimbusds.jose.jwk.gen.RSAKeyGenerator
import org.apache.commons.lang3.RandomStringUtils
import org.junit.Assert
import org.springframework.core.io.ClassPathResource
import java.time.Instant
import java.time.LocalDate
import kotlin.random.Random

object TestDataFactory {
    data class ConfluenceTestData(
        val space: SpaceResponse,
        val pageWithAttachments: Map<PageResponse, List<AttachmentResponse>>
    )
    val TEST_RPM = 10
    val CONFLUENCE_TEST_URL = "https://testconfluence"

    fun confluenceTestData(pageContent: String) = ConfluenceTestData(
        space = SpaceResponse(123, "test", "testspace", "testspace"),
        pageWithAttachments = mapOf(
            confluenceTestPage(
                id = "1",
                title = "TESTOVAYA STRANITSA",
                content = pageContent
            ) to listOf(
                confluenceTestAttachment("2", "image.png"),
                confluenceTestAttachment("3", "document.pdf")
            )

        )
    )

    fun confluenceTestPage(content: String, id: String, title: String, version: Int = 1): PageResponse {
        return PageResponse(
            id = id,
            type = "page",
            status = "OK",
            title = title,
            version = ContentVersion(version),
            ancestors = emptyList(),
            body = ContentBody(
                exportView = ExportView(
                    value = content,
                    representation = "test"
                )
            )
        )
    }

    fun confluenceTestAttachment(id: String, title: String, version: Int = 1): AttachmentResponse {
        return AttachmentResponse(
            id = id,
            type = "picture",
            status = "OK",
            title = title,
            version = ContentVersion(version),
            ancestors = emptyList(),
            _links = mapOf("download" to "/download/attachment/$id")
        )
    }

    fun projectCreateRequest(): ProjectCreateRequest {
        return ProjectCreateRequest(
            "project-name-${RandomStringUtils.randomAlphabetic(8)}"
        )
    }

    fun apiKeyCreateRequest(expiredAt: Instant? = null): ApiKeyCreateRequest {
        return ApiKeyCreateRequest(
            name = "api-key-${RandomStringUtils.randomAlphabetic(8)}",
            expiredAt = expiredAt
        )
    }

    fun testRetrieveChunksResponse(): RetrieveChunksResponse {
        return RetrieveChunksResponse(chunks = listOf(testChunk()))
    }

    fun testChunk(docId: String = "doc1.docx"): ChunkResponse {
        return ChunkResponse(
            id = "chunk-${RandomStringUtils.randomAlphabetic(8)}",
            content = "test content",
            score = 0.5,
            docId = docId
        )
    }

    fun integrationDTO(
        name: String,
        settings: IntegrationSettings = ConfluenceIntegrationSettings(
            baseUrl = "localhost",
            space = "admin",
            token = "admin",
        ),
        autoIngest: Boolean = true,
        autoSync: Boolean = true
        ): IntegrationDTO {
        return IntegrationDTO(
            name = name,
            type = IntegrationDTO.Type.CONFLUENCE,
            checkIntervalMinutes = Random.nextInt(30, 150),
            autoIngest = autoIngest,
            autoSync = autoSync,
            settings = settings
        )
    }

    fun testBalanceUpdate(accountId: Long): TariffUpdateDto {
        return TariffUpdateDto(
            accountId = accountId,
            tariffVersion = 1,
            currentRequestsByTariff = 10,
            totalRequestsByPackages = 20,
            operationsSuspended = false
        )
    }

    fun createTestLicenseKey(): KeyV1 {
        val signatureKey = RSAKeyGenerator(2048).generate()

        return KeyV1(
            version = 1,
            id = "IT-Test-Key",
            test = true,
            notBeforeDate = LocalDate.now(),
            expirationDate = LocalDate.now().plusDays(2),
            signaturePrivateKey = RSAKeyUtil.writePrivateKeyToBase64(signatureKey.toRSAPrivateKey()),
            signaturePublicKey = RSAKeyUtil.writePublicKeyToBase64(signatureKey.toRSAPublicKey()),
            previousSignaturePublicKey = null,
            modules = listOf(
                KHubModuleV1(khubApiRequestsRpm = TEST_RPM)
            )
        )
    }

    fun generateTestSetRequest(): GenerateTestSetRequest {
        return GenerateTestSetRequest(
            name = "test-set-${RandomStringUtils.randomAlphabetic(8)}"
        )
    }

    fun testFile(filePath: String): ClassPathResource {
        return ClassPathResource("/files/$filePath").also {
            if (!it.file.exists()) {
                Assert.fail("Can't load test file: $filePath")
            }
        }
    }
}
