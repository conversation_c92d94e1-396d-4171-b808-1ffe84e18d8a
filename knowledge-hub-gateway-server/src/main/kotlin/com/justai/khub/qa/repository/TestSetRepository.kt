package com.justai.khub.qa.repository

import com.justai.khub.qa.entity.TestSetEntity
import com.justai.khub.qa.enumeration.TestSetStatus
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.jpa.repository.EntityGraph
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface TestSetRepository : JpaRepository<TestSetEntity, Long> {
    @EntityGraph(attributePaths = ["schedule", "project"])
    fun findByProjectIdAndArchived(projectId: Long, archived: Boolean, pageable: PageRequest): Page<TestSetEntity>

    fun findAllByStatus(status: TestSetStatus, pagination: PageRequest): List<TestSetEntity>


    @Modifying(clearAutomatically = true)
    @Query(
        value = "UPDATE test_set " +
            "SET name = :newName, properties = CAST(:newProperties AS JSONB), updated_at = :updatedAt " +
            "WHERE id = :id", nativeQuery = true
    )
    fun updateNameAndProperties(id: Long, newName: String, newProperties: String, updatedAt: LocalDateTime = LocalDateTime.now()): Int

    @Modifying
    @Query(
        "UPDATE TestSetEntity ts " +
            "SET ts.status = :newStatus, ts.lastError = :lastError, ts.updatedAt = :updatedAt " +
            "WHERE ts.id = :id AND ts.status IN :expectedStatuses"
    )
    fun updateStatusAndLastError(
        id: Long,
        expectedStatuses: List<TestSetStatus>,
        newStatus: TestSetStatus,
        lastError: String? = null,
        updatedAt: LocalDateTime = LocalDateTime.now()
    ): Int

    @Modifying
    @Query(
        "UPDATE TestSetEntity ts " +
            "SET ts.totalTests = ts.totalTests + :testsCount, " +
            "ts.usedPromptTokens = ts.usedPromptTokens + :promptTokens, " +
            "ts.usedCompletionTokens = ts.usedCompletionTokens + :completionTokens, " +
            "ts.updatedAt = :updatedAt " +
            "WHERE ts.id = :id"
    )
    fun incrementTestsCountAndUsage(
        id: Long,
        testsCount: Int,
        promptTokens: Int,
        completionTokens: Int,
        updatedAt: LocalDateTime = LocalDateTime.now()
    ): Int

    @Modifying
    @Query("UPDATE TestSetEntity ts SET ts.archived = :archived, ts.updatedAt = :updatedAt WHERE ts.id = :id AND ts.archived != :archived")
    fun changeArchivedStatus(id: Long, archived: Boolean, updatedAt: LocalDateTime = LocalDateTime.now())

    @EntityGraph(attributePaths = ["schedule", "project"])
    fun findByProjectIdAndArchivedFalseAndName(projectId: Long, name: String): List<TestSetEntity>
}
