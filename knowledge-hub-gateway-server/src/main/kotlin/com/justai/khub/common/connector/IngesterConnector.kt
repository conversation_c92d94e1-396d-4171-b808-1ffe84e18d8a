package com.justai.khub.common.connector

import com.fasterxml.jackson.databind.node.ObjectNode
import com.justai.khub.api.fe.model.E2ECheckResult
import com.justai.khub.api.fe.model.E2EComponent
import com.justai.khub.common.configuration.dto.IntegrationProperties
import com.justai.khub.common.dto.ingester.IngestFileResponse
import com.justai.khub.common.dto.ServiceVersionResponse
import com.justai.khub.common.dto.WithIntegrationTimer
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.enumeration.ApiErrorCode.Companion.SOURCE_ERROR_CODE_ARG
import com.justai.khub.common.enumeration.IntegrationName
import com.justai.khub.common.exception.KhubException
import com.justai.khub.common.util.JSON
import com.justai.khub.project.dto.FileLink
import com.justai.khub.project.dto.IngestPipelineConfig
import com.justai.khub.project.entity.ProjectFileEntity
import com.justai.khub.project.mapper.RagServiceMapper
import com.justai.loadbalancer.annotations.HttpInternal
import org.slf4j.LoggerFactory
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod.GET
import org.springframework.http.HttpMethod.POST
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.web.client.RestClientResponseException
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.postForObject

@Service
@WithIntegrationTimer(integrationName = IntegrationName.ingester)
class IngesterConnector(
    private val ragServiceMapper: RagServiceMapper,
    @HttpInternal private val ingesterRestTemplate: RestTemplate,
    integrationProperties: IntegrationProperties
) {
    private val log = LoggerFactory.getLogger(this::class.java)

    val baseUrl = integrationProperties.ingester.baseUrl

    fun ingest(
        file: ProjectFileEntity,
        fileLink: FileLink,
        parsedFileName: String,
        ingestSettings: IngestPipelineConfig
    ): IngestFileResponse {
        val request = ragServiceMapper.toIngestRequest(file, fileLink, parsedFileName, ingestSettings)
        return try {
            ingesterRestTemplate.postForObject<IngestFileResponse>("${baseUrl}/api/ingest/file", request)
        } catch (ex: Exception) {
            handleException(ex)
            throw ex
        }
    }

    fun deleteFile(indexName: String, docId: String) {
        try {
            val request = mapOf("doc_id" to docId)
            ingesterRestTemplate.exchange(
                "${baseUrl}/api/index/${indexName}/doc/delete",
                POST,
                HttpEntity(request),
                Void::class.java
            )
        } catch (ex: Exception) {
            handleException(ex)
        }
    }

    fun deleteAllFiles(indexName: String) {
        try {
            ingesterRestTemplate.delete("${baseUrl}/api/index/${indexName}")
        } catch (ex: Exception) {
            handleException(ex)
        }
    }

    fun performE2E(requestId: String, loggingEnabledE2ECheck: Boolean): List<E2EComponent> {
        val request = HttpEntity(
            null,
            HttpHeaders().apply {
                set("Z-RequestId", requestId)
                set("loggingEnabledE2ECheck", "$loggingEnabledE2ECheck")
            }
        )
        val result = ingesterRestTemplate
            .exchange("${baseUrl}/e2e-check", GET, request, E2ECheckResult::class.java)
        return result.body?.e2EComponents ?: listOf()
    }

    fun getVersion(): ServiceVersionResponse {
        return ingesterRestTemplate.getForObject("${baseUrl}/version", ServiceVersionResponse::class.java)!!
    }

    private fun handleException(ex: Exception) {
        val errorCode =
            if (ex is RestClientResponseException && ex.responseHeaders?.contentType?.isCompatibleWith(MediaType.APPLICATION_JSON) == true) {
                (JSON.parse(ex.responseBodyAsString) as ObjectNode)["code"]?.asText()
                    ?: ApiErrorCode.INGESTER_INTEGRATION_ERROR.code
            } else {
                null
            }
        throw KhubException(ApiErrorCode.INGESTER_INTEGRATION_ERROR, mapOf(SOURCE_ERROR_CODE_ARG to errorCode), ex)
    }

    private fun checkStatus(response: ResponseEntity<*>) {
        if (!response.statusCode.is2xxSuccessful) {
            log.error("Can't connect to to ingester: status code = {}", response.statusCode)
            throw KhubException(ApiErrorCode.INGESTER_INTEGRATION_ERROR)
        }
    }
}
