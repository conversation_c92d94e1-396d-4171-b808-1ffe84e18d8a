package com.justai.khub.report.service

import com.justai.khub.report.dto.ReportEntry
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVPrinter
import org.springframework.stereotype.Service
import java.io.ByteArrayOutputStream
import java.io.OutputStreamWriter
import java.io.PrintWriter
import java.nio.charset.StandardCharsets

@Service
class CSVReportGenerator {
    fun generateReport(reportEntries: List<ReportEntry>): ByteArray {
        val output = ByteArrayOutputStream()
        CSVPrinter(
            PrintWriter(OutputStreamWriter(output, StandardCharsets.UTF_8)),
            CSVFormat.DEFAULT.builder().setHeader("Date", "RecordsCount", "UsedCompletionTokens", "UsedPromptTokens").build()
        ).use { csvPrinter ->
            for (reportEntry in reportEntries) {
                csvPrinter.printRecord(
                    reportEntry.date.toString(),
                    reportEntry.recordsCount,
                    reportEntry.usedCompletionTokens,
                    reportEntry.usedPromptTokens
                )
            }
        }
        return output.toByteArray()
    }
}
