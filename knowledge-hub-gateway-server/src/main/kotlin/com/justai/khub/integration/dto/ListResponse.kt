package com.justai.khub.integration.dto

import com.fasterxml.jackson.annotation.JsonProperty
import org.springframework.http.HttpHeaders

data class ExportView(
    val value: String,
    val representation: String
)

data class ContentBody(
    @JsonProperty("export_view")
    val exportView: ExportView
)

data class ContentVersion(
    val number: Int,
)

data class AncestorResponse(
    val id: String,
    val title: String? = null
)

sealed class ContentResponse(
    open val id: String,
    open val type: String? = null,
    open val status: String? = null,
    open val title: String? = null,
    open val version: ContentVersion,
    open val _links: Map<String, String>? = null
)

data class PageResponse(
    override val id: String,
    override val type: String? = null,
    override val status: String? = null,
    override val title: String? = null,
    override val version: ContentVersion,
    val ancestors: List<AncestorResponse>,
    val body: ContentBody
) : ContentResponse (id, type, status, title, version)

data class AttachmentResponse(
    override val id: String,
    override val type: String? = null,
    override val status: String? = null,
    override val title: String? = null,
    override val version: ContentVersion,
    override val _links: Map<String, String>? = null,
    val ancestors: List<AncestorResponse>? = null
) : ContentResponse (id, type, status, title, version, _links)

data class AttachmentWithLink(
    val attachment: AttachmentResponse,
    val downloadLink: DownloadReference
)

data class DownloadReference(
    val link: String,
    val headers: HttpHeaders? = null
)

data class ConfluencePage(
    val content: PageResponse,
    val attachments: List<AttachmentWithLink> = emptyList()
)

data class SpaceResponse(
    val id: Long,
    val type: String? = null,
    val key: String,
    val name: String? = null,
)

data class ListResponse<T>(
    val start: Int,
    val limit: Int,
    val size: Int,
    val results: List<T>
)

data class ListResponseV2<T>(
    val results: List<T>,
    val _links: LinksV2,
)

data class LinksV2(
    val next: String?,
    val base: String,
)
