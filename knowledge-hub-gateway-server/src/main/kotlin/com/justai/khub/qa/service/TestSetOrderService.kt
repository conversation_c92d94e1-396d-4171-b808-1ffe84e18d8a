package com.justai.khub.qa.service

import com.justai.khub.api.fe.model.TestSetDTO
import com.justai.khub.project.service.ProjectService
import com.justai.khub.qa.entity.TestSetOrderEntity
import com.justai.khub.qa.repository.TestSetOrderRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class TestSetOrderService(
    private val projectService: ProjectService,
    private val testSetOrderRepository: TestSetOrderRepository
) {

    fun sort(projectId: Long, testSets: List<TestSetDTO>): List<TestSetDTO> {
        val customOrderByTestSetId: Map<Long, Int> = getCustomOrder(projectId)
        if (customOrderByTestSetId.isEmpty()) return testSets
        return testSets.sortedWith(TestSetComparator(customOrderByTestSetId))
    }

    private fun getCustomOrder(projectId: Long): Map<Long, Int> {
        val customOrder = testSetOrderRepository.findByProjectId(projectId)?.orderedIds ?: return emptyMap()
        val customOrderByTestSetId = mutableMapOf<Long, Int>()
        customOrder.forEachIndexed { index, id -> customOrderByTestSetId[id] = index }
        return customOrderByTestSetId
    }

    @Transactional(rollbackFor = [Exception::class])
    fun setOrder(
        projectId: Long,
        newOrder: List<Long>,
        accountId: Long
    ) {
        val project = projectService.getProject(projectId, accountId)
        val entity = testSetOrderRepository.findByProjectId(project.id) ?: TestSetOrderEntity().apply { this.project = project }
        entity.orderedIds = newOrder
        testSetOrderRepository.save(entity)
    }

    companion object {
        class TestSetComparator(val customOrderByTestSetId: Map<Long, Int>) : Comparator<TestSetDTO> {
            override fun compare(o1: TestSetDTO, o2: TestSetDTO): Int {
                val o1CustomOrder = customOrderByTestSetId[o1.id]
                val o2CustomOrder = customOrderByTestSetId[o2.id]
                return if (o1CustomOrder != null && o2CustomOrder != null) {
                    o1CustomOrder.compareTo(o2CustomOrder)
                } else if (o1CustomOrder != null) {
                    1
                } else if (o2CustomOrder != null) {
                    -1
                } else {
                    o1.audit.createdAt.compareTo(o2.audit.createdAt)
                }
            }

        }
    }
}
