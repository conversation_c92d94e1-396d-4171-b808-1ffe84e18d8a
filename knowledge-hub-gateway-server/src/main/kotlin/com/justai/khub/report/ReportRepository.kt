package com.justai.khub.report

import com.justai.khub.chat.entity.ChatHistoryRecordEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.time.LocalDate


@Repository
interface ReportRepository : JpaRepository<ChatHistoryRecordEntity, Long> {

    @Query(
        value = "SELECT DATE(ch.created_at) AS date, " +
            "COUNT(ch.*) AS recordsCount, " +
            "SUM(ch.used_completion_tokens) AS usedCompletionTokens, " +
            "SUM(ch.used_prompt_tokens) AS usedPromptTokens " +
            "FROM chat_history ch " +
            "JOIN chat c ON c.id = ch.chat_id " +
            "WHERE c.created_by_account_id = :accountId " +
            "AND ch.request <> '' " +
            "AND ch.status = :status " +
            "AND DATE(ch.created_at) BETWEEN :dateFrom AND :dateTo " +
            "GROUP BY DATE(ch.created_at) " +
            "ORDER BY DATE(ch.created_at)",
        nativeQuery = true
    )
    fun findReportEntries(
        @Param("status") status: String,
        @Param("dateFrom") dateFrom: LocalDate,
        @Param("dateTo") dateTo: LocalDate,
        @Param("accountId") accountId: Long,
    ): List<Array<Any>>
}
