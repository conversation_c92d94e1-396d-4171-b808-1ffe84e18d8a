package com.justai.khub.common.configuration.service

import org.springframework.http.HttpRequest
import org.springframework.http.client.ClientHttpRequestExecution
import org.springframework.http.client.ClientHttpRequestInterceptor
import org.springframework.http.client.ClientHttpResponse
import java.util.*

class BasicAuthInterceptor(
    private val username: String,
    private val password: String
) : ClientHttpRequestInterceptor {
    override fun intercept(request: HttpRequest, body: ByteArray, execution: ClientHttpRequestExecution): ClientHttpResponse {
        request.headers.apply {
            val encodedAuth: ByteArray = Base64.getEncoder().encode("${username}:${password}".encodeToByteArray())
            add("Authorization", "Basic $encodedAuth")
        }
        return execution.execute(request, body)
    }
}
