package com.justai.khub.billing.service

import com.fasterxml.jackson.databind.node.ObjectNode
import com.justai.khub.billing.entity.BillableOperationEntity
import com.justai.khub.billing.enumeration.BillableOperationStatus
import com.justai.khub.billing.properties.BillingProperties
import com.justai.khub.billing.repository.AccountBalanceRepository
import com.justai.khub.billing.repository.BillableOperationRepository
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import org.slf4j.LoggerFactory
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

@Service
class BillingService(
    private val billingProperties: BillingProperties,
    private val accountBalanceRepository: AccountBalanceRepository,
    private val billableOperationRepository: BillableOperationRepository,
    private val operationRequestUsageCalculator: OperationRequestUsageCalculator,
    private val transactionTemplate: TransactionTemplate,
) {
    private val log = LoggerFactory.getLogger(this::class.java)

    private val SKIPPED_OPERATION_ID = -1L
    private val OPERATION_TIMEOUT_MINUTES = 30L

    fun <T> billSuccessfulCall(
        accountId: Long,
        requestsToUse: Long,
        metadata: ObjectNode? = null,
        action: () -> T
    ): T {
        val operationId = startBillableOperation(accountId, requestsToUse, metadata)
        return try {
            action()
                .also { commitBillableOperation(operationId) }
        } catch (ex: Exception) {
            rollbackBillableOperation(operationId, ex.message)
            throw ex
        }
    }

    fun startBillableOperation(
        accountId: Long,
        requestsToUse: Long,
        metadata: ObjectNode? = null
    ): Long {
        if (!billingProperties.enabled) return SKIPPED_OPERATION_ID

        return transactionTemplate.execute {
            val balance = accountBalanceRepository.findForUpdateByAccountId(accountId) ?: throw KhubException(ApiErrorCode.ACCOUNT_BALANCE_NOT_FOUND)
            if (balance.operationsSuspended) throw KhubException(ApiErrorCode.BILLABLE_OPERATIONS_SUSPENDED)

            val (updatedBalance, billableOperation) = operationRequestUsageCalculator.applyUsage(
                balance,
                requestsToUse,
                metadata
            )
            accountBalanceRepository.saveAndFlush(updatedBalance)
            billableOperationRepository.saveAndFlush(billableOperation).id
        }!!

    }

    fun commitBillableOperation(operationId: Long) {
        if (operationId == SKIPPED_OPERATION_ID) return

        transactionTemplate.execute {
            val updatedCount = billableOperationRepository.updateStatus(
                operationId,
                listOf(BillableOperationStatus.STARTED),
                BillableOperationStatus.COMMITED
            )
            if (updatedCount <= 0) {
                log.warn("Failed to commit operation {}: status already changed", operationId)
            }
        }
    }

    fun rollbackBillableOperation(
        operationId: Long,
        reason: String? = null,
        skipLocked: Boolean = false
    ) {
        if (operationId == SKIPPED_OPERATION_ID) return

        transactionTemplate.execute {
            val operation = billableOperationRepository.findByIdOrNull(operationId) ?: return@execute
            val balance = if (skipLocked) {
                accountBalanceRepository.findForUpdateByAccountIdSkipLocked(operation.accountId)
            } else {
                accountBalanceRepository.findForUpdateByAccountId(operation.accountId)
            } ?: return@execute
            operationRequestUsageCalculator.applyRollback(balance, operation)

            accountBalanceRepository.saveAndFlush(balance)
            val updatedCount = billableOperationRepository.updateStatus(
                operationId,
                listOf(BillableOperationStatus.STARTED),
                BillableOperationStatus.ROLLED_BACK,
                reason
            )
            if (updatedCount <= 0) {
                log.warn("Failed to rollback operation {}: status already changed", operationId)
            }
        }
    }

    @Transactional(readOnly = true)
    fun findOutdatedOperations(): List<BillableOperationEntity> {
        val timeout = LocalDateTime.now().minus(OPERATION_TIMEOUT_MINUTES, ChronoUnit.MINUTES)
        return billableOperationRepository.findByStatusAndUpdatedAtBefore(BillableOperationStatus.STARTED, timeout)
    }
}
