package com.justai.khub.project

import com.justai.khub.BaseProjectIT
import com.justai.khub.api.fe.model.ApiKeyCreateRequest
import com.justai.khub.user.enumeration.ApiKeyStatus
import com.justai.khub.util.TestDataFactory
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import java.time.Instant

class ApiKeyIT : BaseProjectIT() {

    @Test
    fun testCRUD() {
        val apiKey = apiKeyHelper.createApiKey(project.id)
        assertNotNull(apiKey.projectId)

        val apiKeyWithExpiration = apiKeyHelper.createApiKey(
            projectId = project.id,
            request = TestDataFactory.apiKeyCreateRequest(Instant.now().plusSeconds(100))
        )
        assertNotNull(apiKeyWithExpiration.expiredAt)

        var activeApiKeysPage = apiKeyHelper.getApiKeysPage(projectId = project.id)
        assertTrue(activeApiKeysPage.content.any { it.id == apiKey.id })
        assertTrue(activeApiKeysPage.content.any { it.id == apiKeyWithExpiration.id })

        val revokedKey = apiKeyHelper.revokeApiKeyAndExpectOk(apiKeyId = apiKey.id, projectId = project.id)
        assertEquals(ApiKeyStatus.REVOKED.toString(), revokedKey.status)

        activeApiKeysPage = apiKeyHelper.getApiKeysPage(projectId = project.id)
        assertFalse(activeApiKeysPage.content.any { it.id == apiKey.id })

        val allApiKeysPage = apiKeyHelper.getApiKeysPage(project.id, false)
        assertTrue(allApiKeysPage.content.any { it.id == apiKey.id })
    }

    @Test
    fun testApiRequestSamples() {
        val apiKey = apiKeyHelper.createApiKey(project.id)

        val requestSamples = apiKeyHelper.apiRequestsSamples(apiKeyId = apiKey.id, projectId = project.id)
        assertTrue(requestSamples.content.isNotEmpty())
    }

    @Test
    fun testAccess() {
        val apiKey = apiKeyHelper.createApiKey(project.id)
        // test no 401 is returned
        userHelper.getMe(ccAuth = null, apiKey = apiKey.key)
        projectHelper.getProjectsPage(ccAuth = null, apiKey = apiKey.key)
        projectFileHelper.getFilesPage(project.id, ccAuth = null, apiKey = apiKey.key)
        chatHelper.getChatsPage(project.id, ccAuth = null, apiKey = apiKey.key)
        // Check that token management is disabled for token-based authorization
        assertThrows(AssertionError::class.java) {
            apiKeyHelper.createApiKey(projectId = project.id, ccAuth = null, apiKey = apiKey.key)
        }
    }

    @Test
    fun testCheckLastUsageTime() {
        val apiKey = apiKeyHelper.createApiKey(project.id)
        assertNull(apiKey.lastUsedAt)

        projectPublicHelper.getProjectInfo(apiKey.key!!)
        val keyInfo = apiKeyHelper.getApiKeysPage(project.id).content.first { it.id == apiKey.id }

        assertNotNull(keyInfo.lastUsedAt)
    }

    @Test
    fun testTokenLosingProjectWhenItsDeleted() {
        val tempProject = projectHelper.createProject()
        val apiKey = apiKeyHelper.createApiKey(tempProject.id)
        assertNotNull(apiKey.projectId)

        projectHelper.deleteProjectAndExpectOk(tempProject.id)
        projectHelper.deleteProjectRepository(tempProject)

        projectPublicHelper.checkProjectNotFound(apiKey.key!!)
    }

    @Test
    fun testDuplicateTokenName() {
        val apiKey = apiKeyHelper.createApiKey(project.id, request = ApiKeyCreateRequest(name = "key-1"))
        assertNotNull(apiKey.projectId)
        assertThrows(AssertionError::class.java) {
            apiKeyHelper.createApiKey(project.id, request = ApiKeyCreateRequest(name = "key-1"))
        }
    }

    @Test
    fun testRevokeToken() {
        val apiKey = apiKeyHelper.createApiKey(project.id)
        assertNotNull(apiKey.projectId)

        apiKeyHelper.revokeApiKeyAndExpectOk(apiKeyId = apiKey.id, projectId = project.id)

        assertThrows(AssertionError::class.java) {
            projectPublicHelper.getProjectInfo(apiKey.key!!)
        }
    }
}
