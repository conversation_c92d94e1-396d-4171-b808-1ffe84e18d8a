package com.justai.khub

import com.justai.zb.migrations.SpringMigrations

object Migrations {
    private fun setDevProps() {
        System.setProperty("spring.profiles.active", "dev,local")
        System.setProperty("spring.config.location", "knowledge-hub-gateway-server/src/main/conf/")
        System.setProperty("logging.config", "knowledge-hub-gateway-server/src/main/conf/logback.xml")
        System.setProperty("sql", "knowledge-hub-gateway-server/src/main/migrations")
    }

    private fun newSpringMigrations(): SpringMigrations {
        return SpringMigrations.ofConfiguration(Migrations::class.java)
    }

    @JvmStatic
    fun main(args: Array<String>) {
        System.setProperty("user.timezone", "UTC")
        System.setProperty("file.encoding", "UTF-8")
        System.setProperty("spring.devtools.restart.enabled", "false")

        newSpringMigrations().run(args.asList())
    }

    object Migrate {
        @JvmStatic
        fun main(args: Array<String>) {
            setDevProps()
            newSpringMigrations().migrate()
        }
    }

    object CleanMigrate {
        @JvmStatic
        fun main(args: Array<String>) {
            setDevProps()
            newSpringMigrations().cleanMigrate()
        }
    }
}
