package com.justai.khub.util

import com.justai.khub.api.fe.model.ApiKeyCreateRequest
import com.justai.khub.api.fe.model.ApiKeyDTO
import com.justai.khub.api.fe.model.ApiKeysPage
import com.justai.khub.api.fe.model.ApiRequestSamples
import com.justai.khub.common.util.JSON
import com.justai.khub.util.WebUtils.parseTo
import com.justai.security.session.auth.JustSessionAuthentication
import org.junit.jupiter.api.Assertions
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors
import org.springframework.stereotype.Component
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.ResultActions
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers

@Component
class ApiKeyHelper {
    @Autowired
    protected lateinit var mvc: MockMvc

    @Autowired
    protected lateinit var defaultAuth: JustSessionAuthentication

    fun createApiKey(
        projectId: Long,
        request: ApiKeyCreateRequest = TestDataFactory.apiKeyCreateRequest(),
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ApiKeyDTO {
        val newApiKey = mvc.perform(
            post("/api/khub/projects/${projectId}/api-keys")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn().parseTo<ApiKeyDTO>()
        Assertions.assertNotNull(newApiKey.key)
        return newApiKey
    }

    fun getApiKeysPage(
        projectId: Long,
        onlyActive: Boolean = true,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ApiKeysPage {
        return mvc.perform(
            get("/api/khub/projects/${projectId}/api-keys")
                .param("onlyActive", "$onlyActive")
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn().parseTo<ApiKeysPage>()
    }

    fun revokeApiKeyAndExpectOk(
        apiKeyId: Long,
        projectId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ApiKeyDTO {
        return revokeApiKey(apiKeyId, projectId, ccAuth, apiKey).andExpect(MockMvcResultMatchers.status().isOk).andReturn().parseTo<ApiKeyDTO>()
    }

    fun revokeApiKey(
        apiKeyId: Long,
        projectId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ResultActions {
        return mvc.perform(
            post("/api/khub/projects/${projectId}/api-keys/${apiKeyId}/revoke")
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
    }

    fun apiRequestsSamples(
        apiKeyId: Long,
        projectId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ApiRequestSamples {
        return mvc.perform(
            get("/api/khub/projects/$projectId/api-keys/$apiKeyId/request-samples")
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn().parseTo<ApiRequestSamples>()
    }
}
