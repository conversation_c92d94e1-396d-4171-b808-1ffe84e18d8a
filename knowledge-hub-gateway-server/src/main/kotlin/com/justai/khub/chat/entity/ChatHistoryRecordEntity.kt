package com.justai.khub.chat.entity

import com.justai.khub.chat.enumeration.ChatHistoryRecordStatus
import com.justai.khub.common.dto.AdditionalGenerationInformation
import com.justai.khub.common.entity.converter.AdditionalInformationConverter
import com.justai.khub.project.dto.SearchPipelineConfig
import com.justai.khub.query.dto.UserHistory
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.*
import org.hibernate.annotations.Type
import java.time.LocalDateTime

@Entity
@Table(name = "chat_history")
class ChatHistoryRecordEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = -1

    @ManyToOne
    lateinit var chat: ChatEntity

    @Column(nullable = false)
    lateinit var request: String

    @Column
    var segment: String? = null

    @Column
    var response: String? = null

    @Column
    var archived: Boolean = false

    @Column
    @Enumerated(EnumType.STRING)
    lateinit var status: ChatHistoryRecordStatus

    @Column(nullable = false)
    lateinit var createdAt: LocalDateTime

    @Column(nullable = false)
    lateinit var updatedAt: LocalDateTime

    @Column
    var usedCompletionTokens: Int? = null

    @Column
    var usedPromptTokens: Int? = null

    @Type(JsonType::class)
    @Column(columnDefinition = "jsonb")
    var searchSettings: SearchPipelineConfig? = null

    @Type(JsonType::class)
    @Column(columnDefinition = "jsonb")
    var userHistory: UserHistory? = null

    @Column
    var lastLockedBy: String? = null

    @Column
    @Convert(converter = AdditionalInformationConverter::class)
    var addInfo: AdditionalGenerationInformation? = null
}
