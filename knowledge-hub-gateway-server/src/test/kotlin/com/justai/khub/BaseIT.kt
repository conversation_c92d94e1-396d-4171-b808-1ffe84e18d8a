package com.justai.khub

import com.justai.khub.util.*
import com.justai.security.session.auth.JustSessionAuthentication
import io.github.bucket4j.Bucket
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.TestInfo
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.TestPropertySource
import org.testcontainers.junit.jupiter.Testcontainers

@ActiveProfiles("dev", "test", "local")
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = [
        KHGatewayApplication::class,
        TestBeansConfiguration::class
    ],

    properties = ["spring.config.location=src/test/resources/,src/main/conf/,src/test/resources/application-test.yml"]
)
@TestPropertySource(
    properties = [
        "context.initializer.classes=com.justai.khub.util.DockerPostgresDataSourceInitializer"
    ]
)
@Testcontainers
abstract class BaseIT {
    private val log = LoggerFactory.getLogger(this::class.java)

    @Autowired
    protected lateinit var projectHelper: ProjectHelper

    @Autowired
    protected lateinit var projectPublicHelper: ProjectPublicHelper
    @Autowired
    protected lateinit var projectSourcePublicHelper: ProjectSourcePublicHelper

    @Autowired
    protected lateinit var integrationPublicHelper: IntegrationPublicHelper

    @Autowired
    protected lateinit var integrationHelper: IntegrationHelper

    @Autowired
    protected lateinit var retrieveChunksHelper: RetrieveChunksHelper

    @Autowired
    protected lateinit var projectFileHelper: ProjectFileHelper

    @Autowired
    protected lateinit var reportHelper: ReportHelper

    @Autowired
    protected lateinit var ingestHelper: IngestionHelper

    @Autowired
    protected lateinit var chatHelper: ChatHelper

    @Autowired
    protected lateinit var qaHelper: QAHelper

    @Autowired
    protected lateinit var retrieveHelper: RetrieveHelper

    @Autowired
    protected lateinit var chatPublicHelper: ChatPublicHelper

    @Autowired
    protected lateinit var apiKeyHelper: ApiKeyHelper

    @Autowired
    protected lateinit var userHelper: UserHelper

    @Autowired
    protected lateinit var billingHelper: BillingHelper

    @Autowired
    protected lateinit var defaultAuth: JustSessionAuthentication

    @Autowired
    protected lateinit var channelHelper: ChannelHelper

    @Autowired
    protected lateinit var globalRateLimitingBucket: Bucket

    @BeforeEach
    fun setup(testInfo: TestInfo) {
        log.info("Starting test ${testInfo.testClass.get().simpleName}.${testInfo.displayName}...")
        globalRateLimitingBucket.reset()
    }
}
