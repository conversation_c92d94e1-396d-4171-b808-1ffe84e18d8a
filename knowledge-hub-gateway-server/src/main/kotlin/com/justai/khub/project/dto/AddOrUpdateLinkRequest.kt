package com.justai.khub.project.dto

import com.justai.khub.api.public.model.AddLinkRequest
import com.justai.khub.api.public.model.UpdateLinkRequest

data class AddOrUpdateLinkRequest(
    val link: String,
    val name: String?,
    val segment: String?
) {
    constructor(addRequest: AddLinkRequest) : this(
        link = addRequest.link,
        name = addRequest.name,
        segment = addRequest.segment
    )

    constructor(updateRequest: UpdateLinkRequest) : this(
        link = updateRequest.link,
        name = updateRequest.name,
        segment = updateRequest.segment
    )
}
