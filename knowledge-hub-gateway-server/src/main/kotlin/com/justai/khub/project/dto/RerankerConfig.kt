package com.justai.khub.project.dto

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
enum class RerankerType {
    noop,
    manual,
    model
}

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes(
    JsonSubTypes.Type(value = NoopRerankerConfig::class, name = "noop"),
    JsonSubTypes.Type(value = ModelRerankerConfig::class, name = "model"),
    JsonSubTypes.Type(value = ManualRerankerConfig::class, name = "manual")
)
sealed class RerankerConfig {
    abstract val type: RerankerType
    abstract val minScore: Int
}

data class NoopRerankerConfig(
    override val type: RerankerType = RerankerType.noop,
    override val minScore: Int = -5
) : RerankerConfig()

data class ModelRerankerConfig(
    override val type: RerankerType = RerankerType.model,
    override val minScore: Int = -5,
    val model: String,
    val maxChunks: Int,
) : RerankerConfig()

data class ManualRerankerConfig(
    override val type: RerankerType = RerankerType.manual,
    override val minScore: Int = -5,
    val maxChunks: Int,
    val scoreReductionLimit: Int,
    val maxChunksPerDoc: Int
) : RerankerConfig()
