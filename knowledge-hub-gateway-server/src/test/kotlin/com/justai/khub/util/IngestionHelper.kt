package com.justai.khub.util

import com.justai.khub.api.fe.model.CancelIngestRequest
import com.justai.khub.api.fe.model.ProjectDTO
import com.justai.khub.common.util.JSON
import com.justai.khub.util.WebUtils.parseTo
import com.justai.security.session.auth.JustSessionAuthentication
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors
import org.springframework.stereotype.Component
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.ResultActions
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@Component
class IngestionHelper {

    @Autowired
    protected lateinit var mvc: MockMvc

    @Autowired
    protected lateinit var defaultAuth: JustSessionAuthentication

    fun startIngest(
        projectId: Long,
        versionName: String? = null,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ProjectDTO {
        val response = mvc.perform(
            post("/api/khub/projects/${projectId}/ingest")
                .param("version", versionName)
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
            .andExpect(status().isOk)
            .andReturn().parseTo<ProjectDTO>()
        return response
    }

    fun cancelIngestAndExpectOk(
        projectId: Long,
        reason: String? = null,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ) {
        cancelIngest(projectId, reason, ccAuth, apiKey)
            .andExpect(status().isOk)
    }

    fun cancelIngest(
        projectId: Long,
        reason: String? = null,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ResultActions {
        val request = CancelIngestRequest(reason = reason)
        return mvc.perform(
            post("/api/khub/projects/${projectId}/ingest/cancel")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
    }
}
