package com.justai.khub.qa.mapper

import com.justai.khub.api.fe.model.*
import com.justai.khub.chat.mapper.ChatMapper
import com.justai.khub.common.configuration.LocalFeaturesConfiguration
import com.justai.khub.common.configuration.dto.QAProperties
import com.justai.khub.common.configuration.dto.RagProperties
import com.justai.khub.common.dto.rag.QueryResponse
import com.justai.khub.common.service.PromptsService
import com.justai.khub.project.dto.LLMConfig
import com.justai.khub.project.dto.LLMProvider
import com.justai.khub.project.entity.ProjectEntity
import com.justai.khub.project.entity.ProjectFileEntity
import com.justai.khub.qa.dto.TestEvaluationResponse
import com.justai.khub.qa.dto.TestSetProperties
import com.justai.khub.qa.entity.*
import com.justai.khub.qa.enumeration.TestSetRunStatus
import com.justai.khub.qa.enumeration.TestSetStatus
import com.justai.khub.qa.enumeration.TestSetStatus.*
import com.justai.khub.qa.enumeration.TestSetType
import com.justai.khub.qa.enumeration.TestSetType.GENERATED
import com.justai.khub.qa.enumeration.TestSetType.MANUAL
import org.springframework.data.domain.Page
import org.springframework.scheduling.support.CronExpression
import org.springframework.stereotype.Component
import java.time.ZoneId
import java.time.ZonedDateTime

@Component
class QAMapper(
    private val qaProperties: QAProperties,
    private val ragProperties: RagProperties,
    private val promptsService: PromptsService,
    private val localFeaturesConfiguration: LocalFeaturesConfiguration,
    private val chatMapper: ChatMapper
) {
    companion object {
        val DEFAULT_TESTS_PER_DOC = 5
        val DEFAULT_MAX_TESTS = 2_000
    }


    fun toManualTestSet(name: String, totalTests: Int, project: ProjectEntity): TestSetEntity {
        return TestSetEntity().also {
            it.name = name
            it.type = MANUAL
            it.status = ACTIVE
            it.project = project
            it.totalTests = totalTests
            it.properties = TestSetProperties()
        }
    }

    fun toGeneratedTestSet(request: GenerateTestSetRequest, project: ProjectEntity): TestSetEntity {
        return TestSetEntity().also {
            it.name = request.name
            it.description = request.description
            it.type = GENERATED
            it.status = READY_TO_PROCESS
            it.project = project
            it.properties = TestSetProperties(
                maxTests = request.maxTests,
                testsPerDocument = request.testsPerDoc ?: DEFAULT_TESTS_PER_DOC,
                generationModel = if (request.generationModel.isNullOrBlank()) null else request.generationModel,
                generationPrompt = if (!localFeaturesConfiguration.customQAPromptsEnabled || request.generationPrompt.isNullOrBlank()) null else request.generationPrompt,
                evaluationModel = if (request.evaluationModel.isNullOrBlank()) null else request.evaluationModel,
                evaluationPrompt = if (!localFeaturesConfiguration.customQAPromptsEnabled || request.evaluationPrompt.isNullOrBlank()) null else request.evaluationPrompt
            )
        }
    }

    fun toDTO(
        testSet: TestSetEntity,
        activeTestSetRun: TestSetRunEntity?,
        testSetRunsCount: Long?
    ): TestSetDTO {
        return TestSetDTO(
            id = testSet.id,
            schedule = testSet.schedule?.schedule,
            name = testSet.name,
            description = testSet.description,
            type = toDTO(testSet.type),
            status = toDTO(testSet.status),
            totalTests = testSet.totalTests,
            maxTests = testSet.properties.maxTests,
            activeTestSetRun = activeTestSetRun?.let { toDTO(it) },
            testSetRunsCount = testSetRunsCount?.toInt() ?: 0,
            settings = toDTO(testSet.properties),
            audit = AuditDTO(
                createdAt = testSet.createdAt.atZone(ZoneId.systemDefault()).toInstant(),
                createdBy = testSet.createdBy.accountId,
                updatedAt = testSet.createdAt.atZone(ZoneId.systemDefault()).toInstant(),
                updatedBy = testSet.createdBy.accountId
            )
        )
    }

    private fun toDTO(properties: TestSetProperties): TestSetSettingsDTO {
        val generationPrompt = if (localFeaturesConfiguration.customQAPromptsEnabled) {
            properties.generationPrompt ?: promptsService.testSetGenerationPrompt()
        } else {
            null
        }
        val evaluationPrompt = if (localFeaturesConfiguration.customQAPromptsEnabled) {
            properties.evaluationPrompt ?: promptsService.testEvaluationPrompt()
        } else {
            null
        }
        return TestSetSettingsDTO(
            testsPerDocument = properties.testsPerDocument,
            maxTests = properties.maxTests,
            generationModel = ragProperties.getModelTitle(properties.generationModel),
            generationPrompt = generationPrompt,
            evaluationModel = ragProperties.getModelTitle(properties.evaluationModel),
            evaluationPrompt = evaluationPrompt,
        )
    }

    fun getDefaultTestSetSettings(): TestSetSettingsDTO {
        return TestSetSettingsDTO(
            testsPerDocument = DEFAULT_TESTS_PER_DOC,
            maxTests = DEFAULT_MAX_TESTS,
            generationModel = ragProperties.getModelTitle(qaProperties.defaultGenerationModel),
            generationPrompt = if (localFeaturesConfiguration.customQAPromptsEnabled) promptsService.testSetGenerationPrompt() else null,
            evaluationModel = ragProperties.getModelTitle(qaProperties.defaultEvaluationModel),
            evaluationPrompt = if (localFeaturesConfiguration.customQAPromptsEnabled) promptsService.testEvaluationPrompt() else null,
        )
    }

    fun toDTO(testSetType: TestSetType): TestSetDTO.Type {
        return when (testSetType) {
            MANUAL -> TestSetDTO.Type.MANUAL
            GENERATED -> TestSetDTO.Type.GENERATED
        }
    }

    fun toDTO(testSetStatus: TestSetStatus): TestSetDTO.Status {
        return when (testSetStatus) {
            READY_TO_PROCESS -> TestSetDTO.Status.READY_TO_PROCESS
            PROCESSING -> TestSetDTO.Status.PROCESSING
            FAILED -> TestSetDTO.Status.FAILED
            CANCELLED -> TestSetDTO.Status.CANCELLED
            ACTIVE -> TestSetDTO.Status.ACTIVE
        }
    }

    fun toDTO(testSetRun: TestSetRunEntity): TestSetRunDTO {
        return TestSetRunDTO(
            id = testSetRun.id,
            testSetId = testSetRun.id,
            status = toDTO(testSetRun.status),
            processingTimeMs = testSetRun.processingTimeMs,
            evaluationModel = ragProperties.getModelTitle(testSetRun.evaluationModel)!!,
            meanScore = testSetRun.meanScore,
            totalRequests = testSetRun.usedPromptTokens + testSetRun.usedCompletionTokens,
            lastError = testSetRun.lastError,
            finishedTests = testSetRun.finishedTests,
            totalTests = testSetRun.testSet.totalTests,
            createdAt = testSetRun.createdAt.atZone(ZoneId.systemDefault()).toInstant(),
            updatedAt = testSetRun.updatedAt.atZone(ZoneId.systemDefault()).toInstant(),
        )
    }


    fun toDTO(testSetRunStatus: TestSetRunStatus): TestSetRunDTO.Status {
        return when (testSetRunStatus) {
            TestSetRunStatus.READY_TO_PROCESS -> TestSetRunDTO.Status.READY_TO_PROCESS
            TestSetRunStatus.PROCESSING -> TestSetRunDTO.Status.PROCESSING
            TestSetRunStatus.FINISHED -> TestSetRunDTO.Status.FINISHED
            TestSetRunStatus.FAILED -> TestSetRunDTO.Status.FAILED
            TestSetRunStatus.CANCELLED -> TestSetRunDTO.Status.CANCELLED
        }
    }

    fun toDTO(
        page: Page<TestSetEntity>,
        activeTestSetRuns: List<TestSetRunEntity>,
        testSetRunsCountByTestSetId: Map<Long, Long>
    ): TestSetsPage {
        val activeTestSetRunsByTestSetId = activeTestSetRuns.associateBy { it.testSet.id }
        return TestSetsPage(
            content = page.content.map { toDTO(it, activeTestSetRunsByTestSetId[it.id], testSetRunsCountByTestSetId[it.id]) },
            paging = PagingResponse(
                totalCount = page.totalElements,
                pageNum = page.number,
                pageSize = page.size
            )
        )
    }

    fun toDTO(page: Page<TestSetRunEntity>): TestSetRunsPage {
        return TestSetRunsPage(
            content = page.content.map { toDTO(it) },
            paging = PagingResponse(
                totalCount = page.totalElements,
                pageNum = page.number,
                pageSize = page.size
            )
        )
    }

    fun toNewRun(testSet: TestSetEntity, request: TestSetRunRequest): TestSetRunEntity {
        return TestSetRunEntity().also {
            it.testSet = testSet
            it.evaluationModel = testSet.properties.evaluationModel ?: qaProperties.defaultEvaluationModel
            it.status = TestSetRunStatus.READY_TO_PROCESS
            it.autoEvaluate = request.autoEvaluate ?: true
        }
    }

    fun initTest(
        testSet: TestSetEntity,
        file: ProjectFileEntity,
        question: String,
        answer: String
    ): TestEntity {
        return TestEntity().apply {
            this.testSet = testSet
            this.groundTruthFile = file
            this.question = question
            this.groundTruthAnswer = answer
        }
    }

    fun initSuccessfulTestRun(
        testSetRun: TestSetRunEntity,
        test: TestEntity,
        runResult: QueryResponse,
        runDuration: Long
    ): TestRunEntity {
        return TestRunEntity().apply {
            this.actualAnswer = runResult.answer
            this.usedChunks =  chatMapper.initUsedChunksEntity(runResult.usedChunks).also { it.testRun = this }
            this.testSetRun = testSetRun
            this.test = test
            this.usedPromptTokens = runResult.promptTokens ?: 0
            this.usedCompletionTokens = runResult.completionTokens ?: 0
            this.processingTimeMs = runDuration.toInt()
        }
    }

    fun initFailedTest(
        testSetRun: TestSetRunEntity,
        test: TestEntity,
        errorCode: String,
        runDuration: Long
    ): TestRunEntity {
        return TestRunEntity().apply {
            this.testSetRun = testSetRun
            this.test = test
            this.processingTimeMs = runDuration.toInt()
            this.lastError = errorCode
        }
    }

    fun initLLMTestEvaluation(
        evaluation: TestEvaluationResponse
    ): TestEvaluationEntity {
        return TestEvaluationEntity().apply {
            this.score = evaluation.score
            this.usedPromptTokens = evaluation.promptTokens
            this.usedCompletionTokens = evaluation.completionTokens
            this.comment = evaluation.comment
        }
    }

    fun initFailedLLMTestEvaluation(errorCode: String): TestEvaluationEntity {
        return TestEvaluationEntity().apply {
            this.lastError = errorCode
        }
    }

    fun toLLMConfig(llmModel: String): LLMConfig {
        // override default properties if need
        return LLMConfig(
            llmProvider = if (llmModel.startsWith("openai/")) LLMProvider.openai else LLMProvider.mlp,
            model = llmModel.removePrefix("openai/")
        )
    }

    fun toDTO(entity: TestSetScheduleEntity): TestSetScheduleDTO {
        return TestSetScheduleDTO(
            schedule = entity.schedule,
            runOnSettingsChange = entity.runOnSettingsChange,
            runOnSourcesChange = entity.runOnSourcesChange,
        )
    }

    fun applyUpdate(
        existed: TestSetScheduleEntity?,
        testSet: TestSetEntity,
        update: TestSetScheduleDTO,
        expression: CronExpression
    ): TestSetScheduleEntity {
        return (existed ?: TestSetScheduleEntity()).apply {
            this.testSet = testSet
            this.schedule = update.schedule
            this.scheduledAt = expression.next(ZonedDateTime.now(ZoneId.of(qaProperties.schedulingTimezone)))!!.toEpochSecond()
            this.runOnSourcesChange = update.runOnSourcesChange
            this.runOnSettingsChange = update.runOnSettingsChange
        }
    }
}
