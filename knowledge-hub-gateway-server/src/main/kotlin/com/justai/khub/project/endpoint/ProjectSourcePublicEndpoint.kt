package com.justai.khub.project.endpoint

import com.justai.khub.api.public.SourcesPublicApiService
import com.justai.khub.api.public.model.*
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.common.util.WebUtils.withUserAndProject
import com.justai.khub.project.dto.AddOrUpdateLinkRequest
import com.justai.khub.project.dto.AddOrUpdateTextRequest
import com.justai.khub.project.dto.ProjectFilesFilter
import com.justai.khub.project.endpoint.ProjectFileEndpoint.Companion.sendFile
import com.justai.khub.project.enumeration.ProjectSourceStatus
import com.justai.khub.project.mapper.ProjectFileMapper.DEFAULT_SORT
import com.justai.khub.project.mapper.ProjectMapper.toPublicSourceDTO
import com.justai.khub.project.mapper.ProjectVersionMapper.Companion.DEFAULT_VERSION
import com.justai.khub.project.service.ProjectFilePublicRequestHandler
import com.justai.khub.project.service.ProjectFileService
import com.justai.khub.project.service.ProjectVersionService
import org.springframework.core.io.Resource
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import java.time.LocalDate

@Service
class ProjectSourcePublicEndpoint(
    private val projectVersionService: ProjectVersionService,
    private val projectFileService: ProjectFileService,
    private val projectFilePublicRequestHandler: ProjectFilePublicRequestHandler
) : SourcesPublicApiService {

    override fun getProjectSources(
        createDateFrom: LocalDate?,
        createDateTo: LocalDate?,
        sourceStatus: String?
    ): ProjectSources = withUserAndProject { currentUser ->
        val version = projectVersionService.getVersion(currentUser.projectId!!, DEFAULT_VERSION, currentUser.accountId)
        val mappedSort = Sort.by(
            Sort.Direction.ASC,
            DEFAULT_SORT
        )
        val filter = ProjectFilesFilter(
            projectId = currentUser.projectId,
            versionName = version.name,
            accountId = currentUser.accountId,
            pageable = PageRequest.of(0, 10000, mappedSort),
            statuses = sourceStatus?.let { listOf(ProjectSourceStatus.valueOf(it)) } ?: emptyList(),
            namePart = null
        )
        val page = projectFileService.getFiles(filter)
        val sources = page.content.filter { source ->
            createDateFrom?.let { source.createdAt.toLocalDate() >= it } ?: true
                && createDateTo?.let { source.createdAt.toLocalDate() <= it } ?: true
        }
        ProjectSources(sources.map { it.toPublicSourceDTO() })
    }

    override fun getProjectSource(sourceId: Long): SourceDTO = withUserAndProject { currentUser ->
        projectVersionService.getVersion(currentUser.projectId!!, DEFAULT_VERSION, currentUser.accountId)
        val source = projectFileService.getFile(sourceId)
        source.toPublicSourceDTO()
    }

    override fun downloadProjectSourceResponseEntity(sourceId: Long): ResponseEntity<Resource> = withUserAndProject { currentUser ->
        projectVersionService.getVersion(currentUser.projectId!!, DEFAULT_VERSION, currentUser.accountId)
        val (filePath, fileContent) = projectFileService.getFileContent(currentUser.projectId, sourceId, currentUser.accountId)
        val fileName = filePath.substringAfterLast("/")
        sendFile(fileName, fileContent)
    }

    override fun deleteProjectSource(sourceId: Long) = withUserAndProject { currentUser ->
        if (!projectFileService.deleteFile(currentUser.projectId!!, sourceId, currentUser.accountId)) {
            throw KhubException(ApiErrorCode.PROJECT_FILE_NOT_FOUND)
        }
        Unit
    }

    override fun addFile(file: MultipartFile, name: String?, segment: String?): SourceDTO = withUserAndProject { currentUser ->
        val newFile = projectFilePublicRequestHandler.addMultipartFile(file, name, segment, currentUser.projectId!!, false, currentUser.accountId)
        newFile.toPublicSourceDTO()
    }

    override fun updateFile(file: MultipartFile, name: String?, segment: String?) = withUserAndProject { currentUser ->
        val updatedFile = projectFilePublicRequestHandler.addMultipartFile(file, name, segment, currentUser.projectId!!, true, currentUser.accountId)
        updatedFile.toPublicSourceDTO()
    }

    override fun addLink(addLinkRequest: AddLinkRequest): SourceDTO = withUserAndProject { currentUser ->
        val newFile = projectFilePublicRequestHandler.addLink(AddOrUpdateLinkRequest(addLinkRequest), currentUser.projectId!!, false, currentUser.accountId)
        newFile.toPublicSourceDTO()
    }

    override fun updateLink(updateLinkRequest: UpdateLinkRequest) = withUserAndProject { currentUser ->
        val updatedFile = projectFilePublicRequestHandler.addLink(AddOrUpdateLinkRequest(updateLinkRequest), currentUser.projectId!!, true, currentUser.accountId)
        updatedFile.toPublicSourceDTO()
    }

    override fun addText(addTextRequest: AddTextRequest): SourceDTO = withUserAndProject { currentUser ->
        val newFile = projectFilePublicRequestHandler.addText(AddOrUpdateTextRequest(addTextRequest), currentUser.projectId!!, false, currentUser.accountId)
        newFile.toPublicSourceDTO()
    }

    override fun updateText(updateTextRequest: UpdateTextRequest) = withUserAndProject { currentUser ->
        val updatedFile = projectFilePublicRequestHandler.addText(AddOrUpdateTextRequest(updateTextRequest), currentUser.projectId!!, true, currentUser.accountId)
        updatedFile.toPublicSourceDTO()
    }
}
