package com.justai.khub.util

import com.justai.khub.api.fe.model.*
import com.justai.khub.common.util.JSON
import com.justai.khub.util.WebUtils.parseTo
import com.justai.security.session.auth.JustSessionAuthentication
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors
import org.springframework.stereotype.Component
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers

@Component
class ChannelHelper {

    @Autowired
    protected lateinit var mvc: MockMvc

    @Autowired
    protected lateinit var defaultAuth: JustSessionAuthentication

    fun createChannel(
        projectId: Long,
        channelName: String,
        channelType: ChannelType,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ChannelInfoDTO {
        val request = ChannelDTO(channelName, channelType)
        val response = mvc.perform(
            post("/api/khub/projects/${projectId}/channels")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn().parseTo<ChannelInfoDTO>()
        return response
    }

    fun getChannelsPage(
        projectId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ChannelsPage {
        val response = mvc.perform(
            get("/api/khub/projects/${projectId}/channels")
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn().parseTo<ChannelsPage>()
        return response
    }

    fun getJaicpChannels(
        projectId: Long,
        channelId: Long,
        versionName: String? = null,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): JaicpChannels {
        val response = mvc.perform(
            get("/api/khub/projects/${projectId}/channels/${channelId}/jaicp")
                .header("Authorization", "Bearer $apiKey")
                .param("version", versionName)
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn().parseTo<JaicpChannels>()
        return response
    }

}
