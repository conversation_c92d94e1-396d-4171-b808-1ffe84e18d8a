package com.justai.khub.attachment.endpoint

import com.justai.khub.api.fe.AttachmentsApiService
import com.justai.khub.attachment.service.AttachmentService
import com.justai.khub.common.exception.AttachmentNotFoundException
import com.justai.khub.common.util.WebUtils.getCurrentAccountIdOrNull
import org.springframework.core.io.ByteArrayResource
import org.springframework.core.io.Resource
import org.springframework.http.MediaType
import org.springframework.http.MediaTypeFactory
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service

@Service
class AttachmentEndpoint(
    private val attachmentService: AttachmentService
) : AttachmentsApiService {

    override fun downloadFileAttachmentResponseEntity(attachmentId: String, linkId: String): ResponseEntity<Resource> {
        val currentAccountId = getCurrentAccountIdOrNull()
        val contentType = MediaTypeFactory.getMediaType(linkId).orElse(MediaType.APPLICATION_OCTET_STREAM)
        val content = attachmentService.findAttachmentByPublicLink(attachmentId, linkId, currentAccountId)
            ?: throw AttachmentNotFoundException(isImage = (contentType.type == "image"))
        return ResponseEntity.ok()
            .contentType(contentType)
            .contentLength(content.size.toLong())
            .body(ByteArrayResource(content))
    }
}
