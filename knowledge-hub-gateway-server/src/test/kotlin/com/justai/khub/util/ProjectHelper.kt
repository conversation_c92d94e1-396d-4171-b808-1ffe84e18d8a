package com.justai.khub.util

import com.justai.khub.api.fe.model.ProjectCreateRequest
import com.justai.khub.api.fe.model.ProjectDTO
import com.justai.khub.api.fe.model.ProjectsPage
import com.justai.khub.common.connector.LakefsConnector
import com.justai.khub.common.util.JSON
import com.justai.khub.project.dto.SettingsSchema
import com.justai.khub.project.dto.UISettings
import com.justai.khub.project.repository.ProjectRepository
import com.justai.khub.util.WebUtils.parseTo
import com.justai.security.session.auth.JustSessionAuthentication
import org.junit.jupiter.api.Assertions
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.authentication
import org.springframework.stereotype.Component
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.ResultActions
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import kotlin.jvm.optionals.getOrNull

@Component
class ProjectHelper {

    @Autowired
    protected lateinit var lakefsConnector: LakefsConnector

    @Autowired
    protected lateinit var projectRepository: ProjectRepository

    @Autowired
    protected lateinit var mvc: MockMvc

    @Autowired
    protected lateinit var defaultAuth: JustSessionAuthentication

    fun deleteProjectAndExpectOk(
        projectId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ) {
        deleteProject(projectId, ccAuth, apiKey).andExpect(status().isOk).andReturn()
    }

    fun deleteProject(
        projectId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ResultActions {
        return mvc.perform(
            delete("/api/khub/projects/${projectId}")
                .header("Authorization", "Bearer $apiKey")
                .with(authentication(ccAuth))
        )
    }

    fun createProject(
        projectCreateRequest: ProjectCreateRequest = TestDataFactory.projectCreateRequest(),
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ProjectDTO {
        val response = tryCreateProject(projectCreateRequest, ccAuth, apiKey)
            .andExpect(status().isOk)
            .andReturn().parseTo<ProjectDTO>()

        Assertions.assertNotNull(response.audit.createdAt)
        Assertions.assertNotNull(response.audit.createdBy)

        Assertions.assertNotNull(response.audit.updatedAt)
        Assertions.assertNotNull(response.audit.updatedBy)
        return response
    }

    fun tryCreateProject(
        projectCreateRequest: ProjectCreateRequest = TestDataFactory.projectCreateRequest(),
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ResultActions {
        return mvc.perform(
            post("/api/khub/projects")
                .header("Authorization", "Bearer $apiKey")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(projectCreateRequest))
                .with(authentication(ccAuth))
        )
    }

    fun getProjectsPage(
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ProjectsPage {
        val response = mvc.perform(
            get("/api/khub/projects")
                .header("Authorization", "Bearer $apiKey")
                .with(authentication(ccAuth))
        )
            .andExpect(status().isOk)
            .andReturn().parseTo<ProjectsPage>()
        return response
    }

    fun getProjectAndExpectOk(
        id: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ProjectDTO {
        return getProject(id, ccAuth, apiKey).andExpect(status().isOk).andReturn().parseTo<ProjectDTO>()
    }

    fun getProject(
        id: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ResultActions {
        return mvc.perform(
            get("/api/khub/projects/${id}")
                .header("Authorization", "Bearer $apiKey")
                .with(authentication(ccAuth))
        )
    }

    fun deleteProjectRepository(projectDTO: ProjectDTO) {
        val project = projectRepository.findById(projectDTO.id).getOrNull() ?: return
        lakefsConnector.deleteRepository(project.repositoryId!!)
    }

    fun getDefaultVersionSettingsAndExpectOk(
        projectId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): SettingsSchema {
        return getDefaultVersionSettings(projectId, ccAuth, apiKey).andExpect(status().isOk).andReturn().parseTo<SettingsSchema>()
    }

    fun getDefaultVersionSettings(
        projectId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ResultActions {
        return mvc.perform(
            get("/api/khub/projects/${projectId}/versions/default/settings")
                .header("Authorization", "Bearer $apiKey")
                .with(authentication(ccAuth))
        )
    }

    fun setDefaultVersionSettings(
        projectId: Long,
        settings: UISettings,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): SettingsSchema {
        val response = mvc.perform(
            post("/api/khub/projects/${projectId}/versions/default/settings")
                .header("Authorization", "Bearer $apiKey")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(settings))
                .with(authentication(ccAuth))
        )
            .andExpect(status().isOk)
            .andReturn().parseTo<SettingsSchema>()
        return response
    }
}
