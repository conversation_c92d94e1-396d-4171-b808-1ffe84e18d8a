package com.justai.khub.common.service

import com.justai.khub.common.dto.ServiceVersionResponse
import com.justai.khub.common.enumeration.IntegrationName
import io.micrometer.core.instrument.*
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager
import org.springframework.stereotype.Service
import java.time.Duration

@Service
class MetricsService(
    val meterRegistry: MeterRegistry
) {

    private val APACHE_HTTP_POOL = "apache_http_pool"
    private val HTTP_POOL = "http_pool"

    fun gaugeAppInfo(version: ServiceVersionResponse) {
        meterRegistry.gauge(
            "version",
            listOf(
                Tag.of("buildBranch", version.buildBranch ?: ""),
                Tag.of("buildNumber", version.buildNumber ?: ""),
                Tag.of("buildDate", version.buildDate ?: ""),
                Tag.of("buildChangeSet", version.buildChangeSet ?: ""),
                Tag.of("projectArtifactId", version.projectArtifactId ?: ""),
                Tag.of("projectVersion", version.projectVersion ?: "")
            ),
            1
        )
    }

    fun postgresRequestTimer(className: String, methodName: String): Timer {
        return Timer.builder("postgres.requests")
            .tag("class", className)
            .tag("method", methodName)
            .publishPercentiles(0.95)
            .distributionStatisticBufferLength(2)
            .distributionStatisticExpiry(Duration.ofSeconds(120))
            .sla(Duration.ofMillis(100), Duration.ofMillis(500), Duration.ofMillis(1_000), Duration.ofMillis(5_000))
            .register(meterRegistry)
    }

    fun integrationTimer(integration: IntegrationName, method: String): Timer {
        return Timer.builder("integration_${integration.name}")
            .description("$integration API timer")
            .tag("method", method)
            .publishPercentiles(0.95)
            .distributionStatisticBufferLength(2)
            .distributionStatisticExpiry(Duration.ofSeconds(120))
            .sla(Duration.ofMillis(200), Duration.ofMillis(500), Duration.ofMillis(1_000), Duration.ofMillis(10_000))
            .register(meterRegistry)
    }

    fun incrementIntegrationErrorsCounter(integration: IntegrationName, method: String) {
        Counter.builder("integration_${integration.name}_errors")
            .description("$integration API errors counter")
            .tag("method", method)
            .register(meterRegistry)
            .increment()
    }

    fun processChatQueryTimer(): Timer {
        return Timer.builder("process_chat_query")
            .publishPercentiles(0.95)
            .distributionStatisticBufferLength(2)
            .distributionStatisticExpiry(Duration.ofSeconds(120))
            .sla(Duration.ofMillis(200), Duration.ofMillis(500), Duration.ofMillis(1_000), Duration.ofMillis(10_000))
            .register(meterRegistry)
    }

    fun chatQueryWaitingTimeTimer(): Timer {
        return Timer.builder("chat_query_waiting_time")
            .publishPercentiles(0.95)
            .distributionStatisticBufferLength(2)
            .distributionStatisticExpiry(Duration.ofSeconds(120))
            .sla(Duration.ofSeconds(10), Duration.ofSeconds(120), Duration.ofSeconds(600), Duration.ofSeconds(3600))
            .register(meterRegistry)
    }

    fun incrementProcessChatQueryErrorsCounter() {
        Counter.builder("chat_query_processor_errors")
            .register(meterRegistry)
            .increment()
    }

    fun addApacheHttpClientMetrics(name: String?, manager: PoolingHttpClientConnectionManager) {
        Gauge.builder(APACHE_HTTP_POOL + "_available_connections", manager) { it.totalStats.available.toDouble() }
            .tags(HTTP_POOL, name)
            .description("The number idle persistent connections")
            .register(meterRegistry)

        Gauge.builder(APACHE_HTTP_POOL + "_leased_connections", manager) { it.totalStats.leased.toDouble() }
            .tags(HTTP_POOL, name)
            .description("The number of persistent connections tracked by the connection manager currently being used to execute requests")
            .register(meterRegistry)

        Gauge.builder(APACHE_HTTP_POOL + "_pending_connections", manager) { it.totalStats.pending.toDouble() }
            .tags(HTTP_POOL, name)
            .description("The number of connection requests being blocked awaiting a free connection")
            .register(meterRegistry)
        Gauge.builder(APACHE_HTTP_POOL + "_max_connections", manager) { it.totalStats.max.toDouble() }
            .tags(HTTP_POOL, name)
            .description("The maximum number of allowed persistent connections")
            .register(meterRegistry)
    }

    fun ingestJobWaitingTimeTimer(): Timer {
        return Timer.builder("ingest_job_waiting_time")
            .publishPercentiles(0.95)
            .distributionStatisticBufferLength(2)
            .distributionStatisticExpiry(Duration.ofSeconds(120))
            .sla(Duration.ofSeconds(10), Duration.ofSeconds(120), Duration.ofSeconds(600), Duration.ofSeconds(3600))
            .register(meterRegistry)
    }

    fun ingestVersionTimer(): Timer {
        return Timer.builder("ingest_version")
            .publishPercentiles(0.95)
            .distributionStatisticBufferLength(2)
            .distributionStatisticExpiry(Duration.ofSeconds(120))
            .sla(Duration.ofSeconds(5), Duration.ofSeconds(60), Duration.ofSeconds(600), Duration.ofSeconds(3600))
            .register(meterRegistry)
    }

    fun incrementIngestVersionErrorsCounter() {
        Counter.builder("ingest_version_errors")
            .register(meterRegistry)
            .increment()
    }

    fun ingestFileWaitingTimeTimer(): Timer {
        return Timer.builder("ingest_file_waiting_time")
            .description("Time between job start and file processing start")
            .publishPercentiles(0.95)
            .distributionStatisticBufferLength(2)
            .distributionStatisticExpiry(Duration.ofSeconds(120))
            .sla(Duration.ofSeconds(30), Duration.ofSeconds(300), Duration.ofSeconds(600), Duration.ofSeconds(3600))
            .register(meterRegistry)
    }

    fun ingestFileTimer(): Timer {
        return Timer.builder("ingest_file")
            .publishPercentiles(0.95)
            .distributionStatisticBufferLength(2)
            .distributionStatisticExpiry(Duration.ofSeconds(120))
            .sla(Duration.ofSeconds(60), Duration.ofSeconds(600), Duration.ofSeconds(3600))
            .register(meterRegistry)
    }

    fun incrementIngestFileErrorsCounter() {
        Counter.builder("ingest_file_errors")
            .register(meterRegistry)
            .increment()
    }

    fun integrationCheckTimer(): Timer {
        return Timer.builder("integration_check")
            .publishPercentiles(0.95)
            .distributionStatisticBufferLength(2)
            .distributionStatisticExpiry(Duration.ofSeconds(120))
            .sla(Duration.ofSeconds(5), Duration.ofSeconds(60), Duration.ofSeconds(600), Duration.ofSeconds(3600))
            .register(meterRegistry)
    }

    fun incrementIntegrationCheckErrorsCounter() {
        Counter.builder("integration_check_errors")
            .register(meterRegistry)
            .increment()
    }

    fun testSetGenerationTimer(): Timer {
        return Timer.builder("testset_generation")
            .publishPercentiles(0.95)
            .distributionStatisticBufferLength(2)
            .distributionStatisticExpiry(Duration.ofSeconds(120))
            .sla(Duration.ofMillis(60_000), Duration.ofSeconds(600_000), Duration.ofSeconds(3600_000))
            .register(meterRegistry)
    }

    fun incrementTestSetGenerationErrorsCounter() {
        Counter.builder("testset_generation_errors")
            .register(meterRegistry)
            .increment()
    }

    fun testSetRunTimer(): Timer {
        return Timer.builder("testset_run")
            .publishPercentiles(0.95)
            .distributionStatisticBufferLength(2)
            .distributionStatisticExpiry(Duration.ofSeconds(120))
            .sla(Duration.ofSeconds(600_000), Duration.ofSeconds(3600_000))
            .register(meterRegistry)
    }

    fun incrementTestSetRunErrorsCounter() {
        Counter.builder("testset_run_errors")
            .register(meterRegistry)
            .increment()
    }
}
