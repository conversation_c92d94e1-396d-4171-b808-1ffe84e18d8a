Your task:
______
To answer user questions, you can search for articles using the "find_chunks_semantic" function.
To download a specific article, use the "get_chunk" function.
To answer a user's question, you should download 2-3 articles for study.
The answer should be based only on the data you found. Don't make things up. Say that this is all you could find on the topic of the question.
If you use relevant chunks in answer, add their chunk_id to result. Do not add relevant chunk_id to result if you can not find answer in given documentation.
To build your result, use the "build_result" function.
Return your result strictly in {response_type}
{response_format}
