package com.justai.khub.util

import com.justai.khub.api.fe.model.ProjectFilesSummary
import com.justai.khub.api.fe.model.ProjectSourcesPage
import com.justai.khub.api.fe.model.SourceDTO
import com.justai.khub.common.util.JSON
import com.justai.khub.project.dto.ProjectFileMetadata
import com.justai.khub.project.enumeration.ProjectSourceStatus
import com.justai.khub.project.mapper.ProjectVersionMapper.Companion.DEFAULT_VERSION
import com.justai.khub.util.WebUtils.parseTo
import com.justai.security.session.auth.JustSessionAuthentication
import org.apache.commons.lang3.RandomStringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.mock.web.MockMultipartFile
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.authentication
import org.springframework.stereotype.Component
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import org.springframework.util.LinkedMultiValueMap
import java.nio.charset.StandardCharsets


@Component
class ProjectFileHelper {

    @Autowired
    protected lateinit var mvc: MockMvc

    @Autowired
    protected lateinit var defaultAuth: JustSessionAuthentication

    fun uploadFile(
        projectId: Long,
        version: String = DEFAULT_VERSION,
        metadata: ProjectFileMetadata? = null,
        fileName: String = "test-file-${RandomStringUtils.randomAlphabetic(5)}.txt",
        fileContent: String = RandomStringUtils.randomAlphabetic(20),
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): SourceDTO {
        val mockFile = MockMultipartFile(
            "file", fileName,
            MediaType.TEXT_PLAIN_VALUE, fileContent.toByteArray()
        )
        return mvc.perform(
            multipart("/api/khub/projects/$projectId/files")
                .file(mockFile)
                .param("branch", version)
                .param("metadata", metadata?.let { JSON.stringify(it) })
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .header("Authorization", "Bearer $apiKey")
                .with(authentication(ccAuth))
        )
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andReturn().parseTo<SourceDTO>()
    }

    fun getFilesPage(
        projectId: Long,
        params: Map<String, String> = mapOf(),
        namePart: String? = null,
        statusFilter: List<ProjectSourceStatus>? = null,
        version: String = DEFAULT_VERSION,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ProjectSourcesPage {
        val response = mvc.perform(
            get("/api/khub/projects/${projectId}/files?version=${version}")
                .header("Authorization", "Bearer $apiKey")
                .queryParams(LinkedMultiValueMap(params.mapValues { mutableListOf(it.value) }.toMutableMap()))
                .with(authentication(ccAuth))
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn().parseTo<ProjectSourcesPage>()
        return response
    }

    fun getFilesSummary(
        projectId: Long,
        version: String = DEFAULT_VERSION,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ProjectFilesSummary {
        val response = mvc.perform(
            get("/api/khub/projects/${projectId}/files/summary?version=${version}")
                .header("Authorization", "Bearer $apiKey")
                .with(authentication(ccAuth))
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn().parseTo<ProjectFilesSummary>()
        return response
    }

    fun deleteFile(
        projectId: Long,
        fileId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ) {
        mvc.perform(
            delete("/api/khub/projects/${projectId}/files/${fileId}")
                .header("Authorization", "Bearer $apiKey")
                .with(authentication(ccAuth))
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn()
    }

    fun downloadFile(
        projectId: Long,
        fileId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): String {
        val result = mvc.perform(
            get("/api/khub/projects/${projectId}/files/${fileId}/content")
                .header("Authorization", "Bearer $apiKey")
                .with(authentication(ccAuth))
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn()
        return String(result.response.contentAsByteArray, StandardCharsets.UTF_8)
    }
}
