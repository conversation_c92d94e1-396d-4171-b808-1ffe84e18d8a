package com.justai.khub.project.endpoint

import com.justai.khub.api.public.ProjectsPublicApiService
import com.justai.khub.api.public.model.ProjectInfo
import com.justai.khub.api.public.model.ProjectResources
import com.justai.khub.common.configuration.dto.RagProperties
import com.justai.khub.common.util.WebUtils.withUserAndProject
import com.justai.khub.project.mapper.ProjectMapper.mapProjectStatus
import com.justai.khub.project.mapper.ProjectVersionMapper.Companion.DEFAULT_VERSION
import com.justai.khub.project.service.ProjectFileService
import com.justai.khub.project.service.ProjectVersionService
import org.springframework.stereotype.Service
import java.time.ZoneId

@Service
class ProjectPublicEndpoint(
    private val projectVersionService: ProjectVersionService,
    private val ragProperties: RagProperties,
    private val projectFileService: ProjectFileService,
) : ProjectsPublicApiService {

    override fun getProjectInfo(): ProjectInfo = withUserAndProject { currentUser ->
        val version = projectVersionService.getVersion(currentUser.projectId!!, DEFAULT_VERSION, currentUser.accountId)
        val fileStatuses = projectFileService.getFilesSummaryByStatus(listOf(version.id))
        ProjectInfo(
            id = version.project.id,
            name = version.project.name,
            status = mapProjectStatus(fileStatuses),
            resources = ProjectResources(llmModels = ragProperties.llm.models.map { it.title }),
            createdAt = version.createdAt.atZone(ZoneId.systemDefault()).toInstant(),
            updatedAt = version.updatedAt.atZone(ZoneId.systemDefault()).toInstant()
        )
    }
}
