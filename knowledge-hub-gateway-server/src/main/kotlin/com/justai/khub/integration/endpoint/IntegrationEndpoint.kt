package com.justai.khub.integration.endpoint

import com.justai.khub.api.fe.IntegrationApiService
import com.justai.khub.api.fe.model.*
import com.justai.khub.common.configuration.dto.IntegrationProperties
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.common.util.WebUtils.getCurrentAccountId
import com.justai.khub.integration.dto.*
import com.justai.khub.integration.entity.IntegrationEntity
import com.justai.khub.integration.enumeration.IntegrationStatus
import com.justai.khub.integration.job.IntegrationCheckJob
import com.justai.khub.integration.mapper.IntegrationMapper.mapIntegrationSort
import com.justai.khub.integration.mapper.IntegrationMapper.toEntity
import com.justai.khub.integration.mapper.IntegrationMapper.toFullDTO
import com.justai.khub.integration.mapper.IntegrationMapper.toShortDTO
import com.justai.khub.integration.mapper.OAuthIntegrationMapper
import com.justai.khub.integration.service.AtlassianService
import com.justai.khub.integration.service.IntegrationService
import com.justai.khub.project.mapper.ProjectVersionMapper.Companion.DEFAULT_VERSION
import org.slf4j.LoggerFactory
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service

@Service
class IntegrationEndpoint(
    private val integrationService: IntegrationService,
    private val integrationCheckJob: IntegrationCheckJob?,
    private val integrationProperties: IntegrationProperties,
    private val atlassianService: AtlassianService,
    private val oAuthIntegrationMapper: OAuthIntegrationMapper,
) : IntegrationApiService {
    private val log = LoggerFactory.getLogger(this::class.java)

    override fun createIntegration(projectId: Long, integrationDTO: IntegrationDTO, version: String?): IntegrationFull {
        val actualVersion = version ?: DEFAULT_VERSION
        val currentAccountId = getCurrentAccountId()
        val createdIntegration = integrationService.createIntegration(integrationDTO, projectId, actualVersion, currentAccountId)
        when (createdIntegration.settings) {
            is HasOAuthToken -> {} // Don't start checking until oauth access granted
            else -> integrationCheckJob?.scheduleManual(createdIntegration)
        }

        return createdIntegration.updatedWithSettings().toFullDTO()
    }

    override fun deleteIntegration(projectId: Long, integrationId: Long) {
        val currentAccountId = getCurrentAccountId()
        integrationService.deleteIntegration(integrationId, currentAccountId)
    }

    override fun getIntegration(projectId: Long, integrationId: Long): IntegrationFull {
        val currentAccountId = getCurrentAccountId()
        val integration = integrationService.getIntegration(integrationId, currentAccountId)
        return integration.updatedWithSettings().toFullDTO()
    }

    override fun getIntegrations(
        projectId: Long,
        pageSize: Int,
        version: String?,
        pageNum: Int,
        name: String?,
        sort: String?,
        direction: String?,
        status: String?,
    ): IntegrationsPage {
        val mappedSort = Sort.by(
            direction?.let { Sort.Direction.fromString(it) } ?: Sort.Direction.ASC,
            mapIntegrationSort(sort)
        )
        val mappedStatuses = if (status.isNullOrBlank()) {
            listOf()
        } else {
            status.let { statusesRaw -> statusesRaw.split(",").map { IntegrationStatus.valueOf(it.trim()) } }
        }
        val filter = IntegrationFilter(
            versionName = version ?: DEFAULT_VERSION,
            accountId = getCurrentAccountId(),
            pageable = PageRequest.of(pageNum, pageSize, mappedSort),
            statuses = mappedStatuses,
            namePart = name?.trim(),
            projectId = projectId,

        )
        val page = integrationService.getIntegrations(filter)
        return IntegrationsPage(
            content = page.content.map { it.toShortDTO() },
            paging = PagingResponse(
                totalCount = page.totalElements,
                pageNum = page.number,
                pageSize = page.size
            )
        )
    }

    override fun updateIntegration(projectId: Long, integrationId: Long, integrationDTO: IntegrationDTO): IntegrationFull {
        val currentAccountId = getCurrentAccountId()
        val updateRequest = integrationDTO.toEntity(null, integrationProperties).also { it.id = integrationId }
        val (updatedIntegration, isSettingsUpdated) = integrationService.updateIntegration(updateRequest, currentAccountId)
        if (isSettingsUpdated) {
            when (updatedIntegration.settings) {
                is HasOAuthToken -> {} // Don't start checking until oauth access granted
                else -> integrationCheckJob?.scheduleManual(updatedIntegration)
            }
        }
        return updatedIntegration.updatedWithSettings().toFullDTO()
    }

    override fun syncIntegration(projectId: Long, integrationId: Long): IntegrationShort {
        val currentAccountId = getCurrentAccountId()
        val integration = integrationService.getIntegration(integrationId, currentAccountId)
        when (val settings = integration.settings) {
            is CloudConfluenceIntegrationSettings -> if (settings.token == null) {
                throw KhubException(ApiErrorCode.ATLASSIAN_ACCESS_NOT_GRANTED)
            }
            is HasSimpleToken -> {}
        }
        integrationCheckJob?.scheduleManual(integration)
        return integration.toShortDTO()
    }

    override fun authenticateAtlassianOAuth2(state: String, code: String): String {
        try {
            val settings = integrationService.findIntegrationByAuthState(state).settings
            val token = atlassianService.getAccessToken(settings, authCode = code)
            val cloudIds = atlassianService.getCloudIds(settings, token = token.accessToken)
            val integration = integrationService.saveOAuthAccess(
                authState = state,
                accessToken = token.accessToken,
                expiresIn = token.expiresIn,
                refreshToken = token.refreshToken,
                cloudIds = cloudIds,
            )
            integrationCheckJob?.scheduleManual(integration)
            return oAuthIntegrationMapper.generateOAuthResultHTML(true)
       } catch (e: Exception) {
            log.error("Atlassian OAuth2 authentication failed with unexpected exception, message: {}", e.message, e)
            return oAuthIntegrationMapper.generateOAuthResultHTML(false)
        }
    }

    private fun makeConfluenceCloudAuthUrl(state: String): String =
        "https://auth.atlassian.com/authorize?audience=api.atlassian.com&client_id=${integrationProperties.atlassian.clientId}&scope=${integrationProperties.atlassian.scopeString}&redirect_uri=${integrationProperties.atlassian.redirectUrl}&state=$state&response_type=code&prompt=consent"

    private fun IntegrationEntity.updatedWithSettings(): IntegrationEntity = apply {
        val oldSettings = settings
        val newSettings = when (oldSettings) {
            is CloudConfluenceIntegrationSettings -> {
                val clouds = oldSettings.token?.let { runCatching { atlassianService.getCloudIds(oldSettings, it) } }?.getOrNull()
                oldSettings.copy(
                    clouds = clouds ?: emptyMap(),
                    authorizationUrl = makeConfluenceCloudAuthUrl(oldSettings.authState)
                )
            }
            is ConfluenceIntegrationSettings, is MinervaIntegrationSettings -> oldSettings
        }
        settings = newSettings
    }
}
