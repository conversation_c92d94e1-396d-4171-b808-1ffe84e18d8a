package com.justai.khub.qa.service

import com.justai.khub.qa.entity.TestEntity
import org.apache.poi.xssf.usermodel.XSSFCell
import org.apache.poi.xssf.usermodel.XSSFSheet
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.springframework.stereotype.Service
import java.io.ByteArrayOutputStream

@Service
class TestSetReportGenerator : ReportGenerator() {
    val HEADERS = listOf(
        "Question",
        "Ground truth answer",
        "Ground truth file",
    )
    val COLUMN_WIDTHS_IN_CHARS = listOf(60, 80, 40)

    fun generateReport(tests: List<TestEntity>): ByteArray {
        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet("Tests")
        createHeadersRow(workbook, sheet)
        tests.forEachIndexed { index, testEntity ->
            createDataRow(workbook, sheet, index + 1, testEntity)
        }
        COLUMN_WIDTHS_IN_CHARS.forEachIndexed { index, width -> sheet.setColumnWidth(index, width * 256) }
        addBordersToAllCells(workbook, sheet)
        return ByteArrayOutputStream().use {
            workbook.write(it)
            it.toByteArray()
        }
    }

    private fun createHeadersRow(workbook: XSSFWorkbook, sheet: XSSFSheet) {
        val headersRow = sheet.createRow(0)
        HEADERS.forEachIndexed { index, header ->
            headersRow.createCell(index).apply {
                setCellValue(header)
                cellStyle = createHeaderStyle(workbook)
            }
        }
    }

    private fun createDataRow(workbook: XSSFWorkbook, sheet: XSSFSheet, rowIndex: Int, test: TestEntity) {
        val dataRow = sheet.createRow(rowIndex)
        val dataSetters: MutableList<(XSSFCell) -> Unit> = mutableListOf(
            { it.setCellValue(test.question) },
            { it.setCellValue(test.groundTruthAnswer) },
            { it.setCellValue(test.groundTruthFile?.relativePath) },
        )
        dataSetters.forEachIndexed { index, dataSetter ->
            dataRow.createCell(index)
                .apply(dataSetter)
                .also { it.cellStyle = createDataStyle(workbook) }
        }

    }
}
