package com.justai.khub.billing.endpoint

import com.justai.khub.billing.dto.AccountBalancesResponse
import com.justai.khub.billing.dto.CurrentLimitsRequestDto
import com.justai.khub.billing.dto.TariffUpdateDto
import com.justai.khub.billing.mapper.BillingMapper.toAccountBalancesResponse
import com.justai.khub.billing.service.AccountBalanceService
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping(path = ["/api/khub/internal/billing"])
class BillingEndpoint(private val accountBalanceService: AccountBalanceService) {

    @PostMapping("/account-limits/list")
    fun findAccountBalances(@RequestBody request: CurrentLimitsRequestDto): AccountBalancesResponse {
        val balances = accountBalanceService.getBalanceByAccountIds(request.accountIds)
        return balances.toAccountBalancesResponse()
    }

    @PostMapping("/cc-tariff-update")
    fun ccTariffUpdate(@RequestBody update: List<TariffUpdateDto>): AccountBalancesResponse {
        val updatedBalances = accountBalanceService.updateAccountBalances(update)
        return updatedBalances.toAccountBalancesResponse()
    }

    @GetMapping("/healthCheck")
    fun billingHealthCheck() = "Ok"
}
