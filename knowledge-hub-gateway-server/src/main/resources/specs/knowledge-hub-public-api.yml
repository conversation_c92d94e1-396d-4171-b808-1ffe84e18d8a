openapi: 3.0.1

info:
  title: Jay Knowledge Hub public API
  version: 1.0.0

servers:
  - url: https://khub.just-ai.com
    description: Production server

tags:
  - name: ProjectsPublic
    description: Работа с проектом базы знаний.
  - name: QueriesPublic
    description: Одиночные запросы на поиск чанков и генерацию ответов. Дополнительно можно передать в запросе историю сообщений.
  - name: ChatsPublic
    description: Запросы на поиск чанков и генерацию ответов в рамках пользовательского чата.
  - name: SourcesPublic
    description: Работа с источниками базы знаний.
  - name: IntegrationsPublic
    description: Работа с интеграциями базы знаний.

paths:
  # *******************************************************************************
  #                           Project Service
  # *******************************************************************************
  /api/knowledge-hub/info:
    get:
      tags:
        - ProjectsPublic
      operationId: getProjectInfo
      summary: 'Информация по проекту'
      description: 'Возвращает информацию по текущему проекту базы знаний.'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectInfo'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'

  /api/knowledge-hub/sources:
    get:
      tags:
        - SourcesPublic
      operationId: getProjectSources
      summary: 'Список источников'
      description: 'Возвращает информацию об источниках базы знаний.'
      parameters:
        - $ref: '#/components/parameters/createDateFrom'
        - $ref: '#/components/parameters/createDateTo'
        - $ref: '#/components/parameters/sourceStatus'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectSources'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
  /api/knowledge-hub/sources/links:
    post:
      tags:
        - SourcesPublic
      operationId: addLink
      summary: 'Добавление по ссылке'
      description: 'Загружает файл по ссылке и добавляет его в качестве источника в базу знаний.
        После добавления источника автоматически запускается его индексация.'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddLinkRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceDTO'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
    put:
      tags:
        - SourcesPublic
      operationId: updateLink
      summary: 'Обновление по ссылке'
      description: 'Заменяет источник с указанным названием, загружая файл по ссылке.
        После обновления источника автоматически запускается его индексация.'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateLinkRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceDTO'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'

  /api/knowledge-hub/sources/texts:
    post:
      tags:
        - SourcesPublic
      operationId: addText
      summary: 'Добавление из текста'
      description: 'Добавляет переданный текст в качестве источника в базу знаний.
         После добавления источника автоматически запускается его индексация.'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddTextRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceDTO'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
    put:
      tags:
        - SourcesPublic
      operationId: updateText
      summary: 'Обновление из текста'
      description: 'Заменяет источник с указанным названием на переданный текст.
         После обновления источника автоматически запускается его индексация.'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTextRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceDTO'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'

  /api/knowledge-hub/sources/files:
    post:
      tags:
        - SourcesPublic
      operationId: addFile
      summary: 'Добавление из файла'
      description: 'Добавляет переданный файл в качестве источника в базу знаний.
         После добавления источника автоматически запускается его индексация.'
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              required:
                - file
              type: object
              properties:
                file:
                  type: string
                  format:
                    binary
                  description: Загружаемый файл. См. [требования к файлам](https://khub.just-ai.com/docs/upload#file-requirements).
                name:
                  type: string
                  description: Название источника. Должно быть уникальным в рамках проекта. Если не указано, формируется автоматически.
                segment:
                  type: string
                  description: Тег (сегмент) базы знаний. Если указан, источник будет доступен при поиске по этому тегу.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceDTO'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'

    put:
      tags:
        - SourcesPublic
      operationId: updateFile
      summary: 'Обновление из файла'
      description: 'Заменяет источник с указанным названием на переданный файл.
         После обновления источника автоматически запускается его индексация.'
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              required:
                - file
              type: object
              properties:
                file:
                  type: string
                  format:
                    binary
                  description: Загружаемый файл. См. [требования к файлам](https://khub.just-ai.com/docs/upload#file-requirements).
                name:
                  type: string
                  description: Название источника. Если в базе знаний нет источника с таким названием, возвращается ошибка.
                segment:
                  type: string
                  description: Тег (сегмент) базы знаний. Если указан, источник будет доступен при поиске по этому тегу.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceDTO'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'

  /api/knowledge-hub/sources/{sourceId}:
    get:
      tags:
        - SourcesPublic
      operationId: getProjectSource
      summary: 'Информация об источнике'
      description: 'Возвращает информацию об источнике базы знаний.'
      parameters:
        - $ref: '#/components/parameters/sourceIdRequired'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceDTO'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
    delete:
      tags:
        - SourcesPublic
      operationId: deleteProjectSource
      summary: 'Удаление источника'
      description: 'Удаляет источник из базы знаний.'
      parameters:
        - $ref: '#/components/parameters/sourceIdRequired'
      responses:
        '200':
          description: OK
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'

  /api/knowledge-hub/sources/{sourceId}/download:
    get:
      tags:
        - SourcesPublic
      operationId: downloadProjectSource
      summary: 'Скачивание источника'
      description: 'Скачивает файл-источник из базы знаний.'
      parameters:
        - $ref: '#/components/parameters/sourceIdRequired'
      responses:
        '200':
          description: OK
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
          headers:
            Content-Disposition:
              schema:
                type: string
              example: attachment; filename="example.pdf"
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'

  /api/knowledge-hub/integrations:
    post:
      tags:
        - IntegrationsPublic
      operationId: createIntegration
      summary: 'Создание интеграции'
      description: 'Добавление новой интеграции в базу знаний. Индексация запускается автоматически после загрузки данных при создании интеграции и при автоматической синхронизации, если она включена.'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IntegrationCreateRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntegrationDTO'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'

    get:
      tags:
        - IntegrationsPublic
      operationId: getIntegrations
      summary: 'Список интеграций'
      description: 'Возвращает список интеграций базы знаний.'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectIntegrations'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'

  /api/knowledge-hub/integrations/{integrationId}:
    get:
      tags:
        - IntegrationsPublic
      operationId: getIntegration
      summary: 'Информация об интеграции'
      description: 'Возвращает информацию об интеграции.'
      parameters:
        - $ref: '#/components/parameters/integrationIdRequired'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntegrationDTO'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'
    delete:
      tags:
        - IntegrationsPublic
      operationId: deleteIntegration
      summary: 'Удаление интеграции'
      description: 'Удаляет интеграцию из базы знаний.'
      parameters:
        - $ref: '#/components/parameters/integrationIdRequired'
      responses:
        '200':
          description: OK
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'


  # *******************************************************************************
  #                           Retrieving Service
  # *******************************************************************************
  /api/knowledge-hub/retrieve:
    post:
      tags:
        - QueriesPublic
      operationId: retrieveChunks
      summary: 'Поиск чанков (retrieving)'
      description: |
        Получение чанков в базе знаний, релевантных для пользовательского ответа.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RetrieveChunksRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RetrievedChunks'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'

  # *******************************************************************************
  #                           Query service
  # *******************************************************************************
  /api/knowledge-hub/query:
    post:
      tags:
        - QueriesPublic
      operationId: generateQueryAnswer
      summary: 'Генерация ответа'
      description: |
        Синхронный запрос на генерацию ответа на пользовательский запрос.

        Учитывайте, что обработка запроса может занимать значительное время.
        Убедитесь, что используемый вами HTTP-клиент имеет установленный таймаут соединения более 1 минуты.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerateAnswerRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QueryProcessingResult'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'

  /api/knowledge-hub/async/query:
    post:
      tags:
        - QueriesPublic
      operationId: generateQueryAnswerAsync
      summary: 'Генерация ответа (асинхронный запрос)'
      description: |
        Асинхронный запрос на генерацию ответа на пользовательский запрос.

        Получить результат можно методом [GET /api/knowledge-hub/query/{queryId}](#operation/getQueryAnswer), где `queryId` — идентификатор запроса, полученный в текущем ответе.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerateAnswerRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QueryProcessingResult'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'

  /api/knowledge-hub/query/{queryId}:
    get:
      tags:
        - QueriesPublic
      operationId: getQueryAnswer
      summary: 'Статус обработки запроса на генерацию ответа'
      description: |
        Возвращает текущий статус обработки запроса на генерацию ответа.
        Использует long-polling, если указан параметр `waitTimeSeconds`.
      parameters:
        - $ref: '#/components/parameters/queryIdRequired'
        - $ref: '#/components/parameters/waitTimeSecondsOptional'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QueryProcessingResult'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          description: Текущий проект или запрашиваемая запись не найдены или были удалены
        '500':
          $ref: '#/components/responses/ServerError'

  /api/knowledge-hub/query/{queryId}/cancel:
    post:
      tags:
        - QueriesPublic
      operationId: cancelQueryProcessing
      summary: 'Отмена обработки запроса на генерацию ответа'
      parameters:
        - $ref: '#/components/parameters/queryIdRequired'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QueryProcessingResult'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          description: Текущий проект или запрашиваемая запись не найдены или были удалены
        '500':
          $ref: '#/components/responses/ServerError'

  # *******************************************************************************
  #                           Chat service
  # *******************************************************************************
  /api/knowledge-hub/chat:
    post:
      tags:
        - ChatsPublic
      operationId: createChat
      summary: 'Создание чата'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateChatRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Chat'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/ProjectNotFoundError'
        '500':
          $ref: '#/components/responses/ServerError'

  /api/knowledge-hub/chat/{chatId}:
    get:
      tags:
        - ChatsPublic
      operationId: getChat
      summary: 'Информация о пользовательском чате'
      parameters:
        - $ref: '#/components/parameters/chatIdRequired'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Chat'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          description: Текущий проект или чат не найден или был удален
        '500':
          $ref: '#/components/responses/ServerError'

  /api/knowledge-hub/chat/{chatId}/retrieve:
    post:
      tags:
        - ChatsPublic
      operationId: retrieveChunksFromChat
      summary: 'Поиск чанков (retrieving)'
      description: |
        Получение чанков в базе знаний, релевантных для пользовательского ответа в рамках чата.
      parameters:
        - $ref: '#/components/parameters/chatIdRequired'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RetrieveChunksFromChatRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RetrievedChunks'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          description: Текущий проект или чат не найден или был удален
        '500':
          $ref: '#/components/responses/ServerError'
  /api/knowledge-hub/chat/{chatId}/query:
    post:
      tags:
        - ChatsPublic
      operationId: processChatQuery
      summary: 'Генерация ответа'
      description: |
        Синхронный запрос на генерацию ответа на пользовательский запрос. При этом учитывается история сообщений в чате.

        Учитывайте, что обработка запроса может занимать значительное время. Убедитесь, что используемый вами HTTP-клиент имеет установленный таймаут соединения более 1 минуты.
      parameters:
        - $ref: '#/components/parameters/chatIdRequired'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatQueryProcessingResult'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          description: Текущий проект или чат не найден или был удален
        '500':
          $ref: '#/components/responses/ServerError'
  /api/knowledge-hub/async/chat/{chatId}/query:
    post:
      tags:
        - ChatsPublic
      operationId: processChatQueryAsync
      summary: 'Генерация ответа (асинхронный запрос)'
      description: |
        Асинхронный запрос на генерацию ответа на пользовательский запрос. При этом учитывается история сообщений в чате.

        Получить результат можно методом [GET /api/knowledge-hub/chat/{chatId}/query/{queryId}](#operation/getChatQueryAnswer), где `queryId` — идентификатор запроса, полученный в текущем ответе.
      parameters:
        - $ref: '#/components/parameters/chatIdRequired'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatQueryProcessingResult'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          description: Текущий проект или чат не найден или был удален
        '500':
          $ref: '#/components/responses/ServerError'
  /api/knowledge-hub/chat/{chatId}/query/{queryId}:
    get:
      tags:
        - ChatsPublic
      operationId: getChatQueryAnswer
      summary: 'Статус обработки запроса на генерацию ответа'
      description: |
        Возвращает текущий статус обработки запроса на генерацию ответа в пользовательском чате.
        Использует long-polling, если указан параметр `waitTimeSeconds`.
      parameters:
        - $ref: '#/components/parameters/chatIdRequired'
        - $ref: '#/components/parameters/queryIdRequired'
        - $ref: '#/components/parameters/waitTimeSecondsOptional'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatQueryProcessingResult'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          description: Текущий проект или запись в чате не найдены или были удалены
        '500':
          $ref: '#/components/responses/ServerError'
  /api/knowledge-hub/chat/{chatId}/query/{queryId}/cancel:
    post:
      tags:
        - ChatsPublic
      operationId: cancelRecordProcessing
      summary: 'Отмена обработки чат-запроса'
      parameters:
        - $ref: '#/components/parameters/chatIdRequired'
        - $ref: '#/components/parameters/queryIdRequired'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatQueryProcessingResult'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          description: Текущий проект или запрашиваемая запись в чате не найдены или были удалены
        '500':
          $ref: '#/components/responses/ServerError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      description: API-ключ содержит информацию об аккаунте пользователя и по текущему проекту базы знаний.

  schemas:
    LongId:
      description: Идентификатор ресурса.
      type: integer
      format: int64

    Error:
      description: Содержимое ошибки с описанием.
      required:
        - code
        - description
      type: object
      properties:
        code:
          description: Код ошибки.
          type: string
        description:
          description: Описание ошибки.
          type: string

    QueryProcessingStatus:
      description: |
        Статус обработки пользовательского запроса.
        - `READY_TO_PROCESS` — запрос готов к обработке.
        - `PROCESSING` — запрос обрабатывается.
        - `FINISHED` — обработка завершена успешно.
        - `FAILED` — обработка завершена с ошибкой.
        - `CANCELED` — обработка остановлена.
      type: string
      enum:
        - READY_TO_PROCESS
        - PROCESSING
        - FINISHED
        - FAILED
        - CANCELED

    BriefSourceInfo:
      type: object
      description: Краткая информация о документе-источнике.
      required:
        - id
        - path
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        path:
          type: string
          description: Название источника в базе знаний.
        externalLink:
          description: |
            Ссылка на источник:
            - Если источник загружен из интеграции — ссылка на оригинал, например на страницу в Confluence.<br/>Ссылка недоступна, если интеграция ни разу не была синхронизирована после появления этого поля.
            - Если источник загружен вручную в виде файла — временная ссылка для скачивания. Срок действия таких ссылок ограничен.<br/>В качестве альтернативы используйте для скачивания метод [GET /sources/\{sourceId\}/download](#operation/downloadProjectSource).
          type: string

    QueryProcessingResult:
      type: object
      description: Текущий результат обработки пользовательского запроса к базе знаний.
      required:
        - id
        - request
        - status
        - createdAt
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        request:
          description: Текст оригинального пользовательского запроса.
          type: string
        response:
          description: Текст ответа, сгенерированного LLM в результате обработки пользовательского запроса.
          type: string
        status:
          $ref: '#/components/schemas/QueryProcessingStatus'
        createdAt:
          description: Время начала обработки запроса.
          type: string
          format: date-time
        updatedAt:
          description: Время последнего обновления запроса.
          type: string
          format: date-time
        comment:
          description: Комментарий по результату обработки запроса.
          type: string
        relevantSources:
          description: Список источников, на основе которых сгенерирован ответ.
          type: array
          items:
            $ref: '#/components/schemas/BriefSourceInfo'

    ChatQueryProcessingResult:
      type: object
      description: Текущий результат обработки пользовательского запроса в чате базы знаний.
      required:
        - id
        - chatId
        - request
        - status
        - createdAt
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        chatId:
          $ref: '#/components/schemas/LongId'
        request:
          description: Текст оригинального пользовательского запроса.
          type: string
        response:
          description: Текст ответа, сгенерированного LLM в результате обработки пользовательского запроса.
          type: string
        status:
          $ref: '#/components/schemas/QueryProcessingStatus'
        createdAt:
          description: Время создания запроса.
          type: string
          format: date-time
        updatedAt:
          description: Время последнего обновления запроса.
          type: string
          format: date-time
        comment:
          description: Комментарий по результату обработки запроса.
          type: string
        relevantSources:
          description: Список источников, на основе которых сгенерирован ответ.
          type: array
          items:
            $ref: '#/components/schemas/BriefSourceInfo'

    PipelineType:
      type: string
      description: |
        Способ поиска релевантных чанков:
        - `semantic` — по семантическому сходству эмбеддингов. Векторное представление запроса пользователя будет сравниваться с векторными представлениями чанков ваших данных.
        - `agent` — с помощью LLM. LLM сама инициирует поиск наиболее подходящих эмбеддингов ваших данных в векторном хранилище, при этом используется function calling.
      enum:
        - semantic
        - agent

    LlmSettings:
      type: object
      description: Настройки LLM.
      properties:
        model:
          description: |
            Используемая модель.

            Список доступных моделей представлен в ответе метода [GET /api/knowledge-hub/info](#operation/getProjectInfo) в массиве `resources.llmModels`.

            Если `pipeline = agent`, то необходимо указать модель, которая поддерживает function calling.
          type: string
        contextWindow:
          description: Максимальный размер контекстного окна запроса в токенах.
          type: integer
          format: int32
          minimum: 4000
          maximum: 16000
        maxTokens:
          description: Максимальное количество токенов в ответе.
          type: integer
          format: int32
          minimum: 500
          maximum: 1000
        temperature:
          description: |
            Температура.
            Влияет на баланс между предсказуемостью и креативностью текста в ответе.
            Низкая температура — больше предсказуемости, высокая — больше креативности.
          type: number
          format: double
          minimum: 0.0
          maximum: 1.0
        topP:
          description: |
            Top P.
            Управляет разнообразием ответов модели.
            Чем меньше значение, тем строже отбор слов, ответ будет более похож на ответ с низкой температурой.
          type: number
          format: double
          minimum: 0.0
          maximum: 1.0
        frequencyPenalty:
          description: |
            Frequency penalty.
            Регулирует вероятность повторения слов в ответе.
            Чем выше значение, тем меньше вероятность повторения.
            Например, если слово «хороший» уже встречалось 3 раза, Frequency penalty снизит вероятность его повторного появления.
          type: number
          format: double
          minimum: -2.0
          maximum: 2.0
        presencePenalty:
          description: |
            Presence penalty.
            Штрафует модель за повторение токенов в ответе.
            В отличие от Frequency penalty, где штраф зависит от количества повторений, Presence penalty одинаково штрафует все повторы, независимо от частоты.
            Например, токен, встречающийся дважды, будет оштрафован так же, как и токен, встречающийся 10 раз.
          type: number
          format: double
          minimum: -2.0
          maximum: 2.0

    ParticipantRole:
      type: string
      description: Роль участника диалога. `user` — пользователь, `assistant` — база знаний.
      enum:
        - user
        - assistant

    RerankerType:
      type: string
      description: Тип повторного ранжирования. `manual` — эмпирическое, `model` — с помощью модели.
      enum:
        - manual
        - model

    HistoryRecord:
      type: object
      description: Запись истории диалога. Содержит текст сообщения и роль автора сообщения.
      required:
        - content
        - role
      properties:
        content:
          description: Текст сообщения.
          type: string
        role:
          $ref: '#/components/schemas/ParticipantRole'

    Rephrase:
      type: object
      description: |
        Настройки перефразирования пользовательского запроса. Позволяют сделать его точнее и понятнее для LLM.

        Параметр используется только в случае, если `pipeline = semantic`.
      required:
        - prompt
      properties:
        prompt:
          description: |
            Промт для LLM с инструкцией по перефразированию. В формулировке промта можно использовать плейсхолдеры:
            - `{history_str}` — история диалога.
            - `{query_str}` — текущий запрос пользователя.
            - `{context_str}` — релевантные чанки, найденные на этапе поиска.
          type: string
          example: "Прочитай историю диалога, переформулируй текущий вопрос пользователя с учетом истории, добавив ее в виде контекста. Сделай вопрос более понятным, четким и структурированным. Добавь к вопросу похожие запросы и заголовок, верни текст с заголовком."

    Reranker:
      type: object
      description: |
        Настройки повторного ранжирования результатов поиска.

        Используются только в случае, если `pipeline = semantic`.
      required:
        - type
      properties:
        type:
          $ref: '#/components/schemas/RerankerType'
        minScore:
          description: |
            Пороговая оценка релевантности. Результаты поиска с оценкой ниже порога не будут использоваться для генерации ответа.
          type: number
          format: double
          minimum: -10.0
          maximum: 10.0
        maxChunksPerDocument:
          description: Максимальное количество результатов из одного документа.
          type: integer
          format: int32
          minimum: 1
          maximum: 10
        maxChunks:
          description: Максимальное количество результатов после повторного ранжирования.
          type: integer
          format: int32
          minimum: 1
          maximum: 30
        scoreReductionLimit:
          description: |
            Максимальное отличие от оценки релевантности от лучшего чанка, в процентах.
            Чанки, чьи оценки отличаются сильнее, не будут использоваться для генерации ответа.
          type: integer
          format: int32
          minimum: 1
          maximum: 40

    FullTextSearchStrategy:
      type: string
      description: |
        Стратегия использования полнотекстового поиска.
        - `hybrid` — объединяет все полученные результаты семантического и полнотекстового поиска.
        - `weighted` — отбирает заданное количество результатов семантического и полнотекстового поиска.
        - `threshold` — если семантический поиск не предоставил достаточно релевантные результаты, выдает результаты полнотекстового поиска.
      enum:
        - hybrid
        - weighted
        - threshold

    FullTextSearch:
      type: object
      description: |
        Настройки полнотекстового поиска, который может дополнять семантический поиск.

        Используются только в случае, если `pipeline = semantic`.
      required:
        - strategy
      properties:
        strategy:
          $ref: '#/components/schemas/FullTextSearchStrategy'
        semanticPortion:
          description: |
            Сколько лучших семантических результатов будет включено в конечный результат.
            Рекомендуемое значение — 10.

            Параметр используется для стратегии `weighted`.
          type: integer
          format: int32
          minimum: 0
          maximum: 10
        ftsPortion:
          description: |
            Сколько лучших результатов полнотекстового поиска будет включено в конечный результат.
            Рекомендуемое значение — 1 или 2.

            Параметр используется для стратегии `weighted`.
          type: integer
          format: int32
          minimum: 1
          maximum: 10
        threshold:
          description: |
            Пороговая оценка релевантности для результатов семантического поиска.
            Если все семантические результаты ниже порога, поиск переключается на полнотекстовый.

            Параметр используется для стратегии `threshold`.
          type: number
          format: double
          minimum: 0
          maximum: 1
        useOnlyInSegment:
          description: |
            Выполнять полнотекстовый поиск только в источниках с тегом, переданным в параметре `search.segment`.
            Если параметр отсутствует или указано значение `false`, полнотекстовый поиск выполняется по всей базе знаний.
          type: boolean

    SearchSettings:
      type: object
      description: Настройки поиска релевантных чанков.
      required:
        - similarityTopK
      properties:
        similarityTopK:
          description: Количество чанков, которые будут извлечены из источников для дальнейшей обработки, например повторного ранжирования или генерации ответа.
          type: integer
          format: int32
          minimum: 5
          maximum: 30
        candidateRadius:
          description: |
            Количество соседних чанков каждого найденного чанка, которые будут извлечены вместе с ним.

            Например, если `similarityTopK = 10`, а `candidateRadius = 3`, то к каждому найденному чанку добавится еще по 3 чанка до и после него.
            Таким образом объем найденного чанка увеличится, в итоге модели будет отправлено 10 «больших» чанков.
          type: integer
          format: int32
          minimum: 0
          maximum: 10
        reranker:
          $ref: '#/components/schemas/Reranker'
        fullTextSearch:
          $ref: '#/components/schemas/FullTextSearch'
        rephraseUserQuery:
          $ref: '#/components/schemas/Rephrase'
        segment:
          description: |
            Тег документов-источников для поиска чанков. Пользователь может указать тег при загрузке источников в базу знаний.
          type: string
          example: "FAQ"

    ResponseGeneration:
      type: object
      description: Настройки для генерации ответа LLM.
      properties:
        prompt:
          description: Промт, на основании которого LLM генерирует ответ.
          type: string
        showRelevantSources:
          description: |
            Возвращать список источников, на основе которых сгенерирован ответ.
          type: boolean

    RetrievedChunks:
      type: object
      description: Чанки, найденные на этапе поиска.
      required:
        - chunks
      properties:
        chunks:
          type: array
          description: Список чанков, отсортированных в порядке убывания оценки релевантности (score).
          items:
            $ref: '#/components/schemas/ChunkWithScore'

    ChunkMetadata:
      type: object
      description: Метаданные чанка.
      required:
        - sourcePath
      properties:
        sourcePath:
          description: Имя источника, из которого извлечен чанк.
          type: string
        sourceUrl:
          description: Ссылка на источник, из которого извлечен чанк.
          type: string
        segment:
          description: Тег источника, указанный при загрузке в базу знаний.
          type: string

    ChunkWithScore:
      type: object
      description: Информация о чанке с оценкой релевантности.
      required:
        - score
        - content
        - source
      properties:
        score:
          description: Оценка релевантности чанка (score) к запросу пользователя.
          type: number
          format: double
        content:
          description: Текстовый контент чанка.
          type: string
        docId:
          deprecated: True
          description: |
            Идентификатор источника, из которого извлечен чанк.
            Параметр устарел. Используйте `source`.
          type: string
        metadata:
          $ref: '#/components/schemas/ChunkMetadata'
        source:
          $ref: '#/components/schemas/BriefSourceInfo'

    RagSettings:
      type: object
      description: |
        Настройки обработки запроса.
      required:
        - pipeline
      properties:
        pipeline:
          $ref: '#/components/schemas/PipelineType'
        search:
          $ref: '#/components/schemas/SearchSettings'
        llm:
          $ref: '#/components/schemas/LlmSettings'
        responseGeneration:
          $ref: '#/components/schemas/ResponseGeneration'

    RetrievingSettings:
      type: object
      description: |
        Настройки поиска чанков.
      required:
        - pipeline
      properties:
        pipeline:
          $ref: '#/components/schemas/PipelineType'
        search:
          $ref: '#/components/schemas/SearchSettings'
        llm:
          $ref: '#/components/schemas/LlmSettings'

    GenerateAnswerRequest:
      type: object
      description: |
        Запрос к базе знаний для получения ответа.
        Дополнительно может включать настройки обработки запроса и историю диалога.
        При наличии истории запрос модифицируется с учетом контекста. Модификация запроса происходит с помощью LLM.
        Если настройки обработки запроса не указаны, используются настройки проекта, в котором был выпущен API-ключ.
      required:
        - query
      properties:
        query:
          description: Текст пользовательского запроса.
          type: string
        history:
          type: array
          description: История диалога. Записи отображаются в обратном хронологическом порядке (от поздних к ранним).
          items:
            $ref: '#/components/schemas/HistoryRecord'
        settings:
          $ref: '#/components/schemas/RagSettings'

    CreateChatRequest:
      type: object
      description: |
        Запрос на создание нового чата.
        Дополнительно может включать настройки обработки запроса.
        Если настройки обработки запроса не указаны, используются настройки проекта, в котором был выпущен API-ключ.
      properties:
        name:
          description: Название пользовательского чата.
          type: string
        settings:
          $ref: '#/components/schemas/RagSettings'

    Chat:
      type: object
      description:
        Результат создания пользовательского чата.
        Содержит настройки поиска и генерации по умолчанию для всех запросов в данном чате.
      required:
        - id
        - settings
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        name:
          description: Название пользовательского чата.
          type: string
        settings:
          $ref: '#/components/schemas/RagSettings'

    ChatRequest:
      type: object
      description: |
        Чат-запрос с базой знаний.
        Дополнительно может включать настройки обработки запроса.
        При выполнении запроса учитывается контекст текущей истории чата: предыдущие сообщения между пользователем и базой знаний.
      required:
        - query
      properties:
        query:
          description: Текст пользовательского запроса.
          type: string
        settings:
          $ref: '#/components/schemas/RagSettings'

    RetrieveChunksRequest:
      type: object
      description: |
        Запрос на поиск релевантных чанков в базе знаний.
        Дополнительно может включать настройки поиска и историю диалога.
        При наличии истории запрос модифицируется с учетом истории. Модификация запроса происходит с помощью LLM.
        Если настройки поиска не указаны, используются настройки проекта, в котором был выпущен API-ключ.
      required:
        - query
      properties:
        query:
          description: Текст пользовательского запроса.
          type: string
        history:
          type: array
          description: История диалога. Записи отображаются в обратном хронологическом порядке (от поздних к ранним).
          items:
            $ref: '#/components/schemas/HistoryRecord'
        settings:
          $ref: '#/components/schemas/RetrievingSettings'

    RetrieveChunksFromChatRequest:
      type: object
      description: |
        Запрос на поиск релевантных чанков в базе знаний в рамках чата.
        Дополнительно может включать настройки поиска.
        При выполнении запроса учитывается контекст текущей истории чата: предыдущие сообщения между пользователем и базой знаний.
        Если настройки поиска не указаны, используются настройки проекта, в котором был выпущен API-ключ.
      required:
        - query
      properties:
        query:
          description: Текст пользовательского запроса.
          type: string
        settings:
          $ref: '#/components/schemas/RetrievingSettings'

    ProjectStatus:
      description: |
        Статус проекта базы знаний.
        - `CREATED` — проект создан, но не проиндексирован.
        - `INGESTING_DOCUMENTS` — идет индексация проекта.
        - `ACTIVE` — проект проиндексирован и готов к обработке пользовательских запросов.
        - `INGEST_FAILED` — во время последней индексации проекта произошла ошибка.
        - `DELETED` — проект был удален.
      type: string
      enum:
        - CREATED
        - INGESTING_DOCUMENTS
        - ACTIVE
        - DELETED
        - INGEST_FAILED

    IntegrationStatus:
      description: |
        Статус интеграции.
        - `ACTIVE` — последняя загрузка данных из интеграции завершена успешно, либо загрузка еще ни разу не была выполнена.
        - `AWAITING_ACTION` — требуется подтвердить запрос доступа к Confluence Cloud.
        - `CHECKING` — выполняется загрузка данных из интеграции.
        - `FAILED` — последняя загрузка данных завершена с ошибкой.
        - `DISABLED` — сейчас не используется.
      type: string
      enum:
        - ACTIVE
        - AWAITING_ACTION
        - CHECKING
        - FAILED
        - DISABLED

    ProjectResources:
      type: object
      description: Ресурсы, доступные в проекте.
      required:
        - llmModels
      properties:
        llmModels:
          type: array
          description: Языковые модели.
          items:
            type: string

    ProjectInfo:
      type: object
      description: Информация о проекте базы знаний.
      required:
        - id
        - name
        - status
        - createdAt
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        name:
          description: Название проекта.
          type: string
        status:
          $ref: '#/components/schemas/ProjectStatus'
        resources:
          $ref: '#/components/schemas/ProjectResources'
        createdAt:
          description: Дата и время создания проекта.
          type: string
          format: date-time
        updatedAt:
          description: Дата и время последнего обновления статуса проекта.
          type: string
          format: date-time

    SourceDTO:
      description: Информация об источнике.
      required:
        - id
        - name
        - type
        - status
        - version
        - createdAt
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        name:
          description: Название источника.
          type: string
        segment:
          description: Тег источника, указанный при загрузке в базу знаний.
          type: string
        status:
          description: Статус индексации источника.
          type: string
        lastError:
          description: Последняя ошибка индексации.
          type: string
        version:
          description: Версия источника в проекте.
          type: string
        sizeBytes:
          description: Размер источника в байтах.
          type: integer
        sizeChars:
          description: Количество символов в проиндексированном источнике.
          type: integer
        createdAt:
          description: Дата и время добавления источника в проект.
          type: string
          format: date-time

    ProjectSources:
      type: object
      description: Источники проекта.
      required:
        - sources
      properties:
        sources:
          type: array
          items:
            $ref: '#/components/schemas/SourceDTO'

    ProjectIntegrations:
      type: object
      description: Интеграции проекта.
      required:
        - integrations
      properties:
        integrations:
          type: array
          items:
            $ref: '#/components/schemas/IntegrationDTO'

    IntegrationCreateRequest:
      description: Запрос на создание интеграции.
      required:
        - name
        - settings
      properties:
        name:
          description: Название интеграции.
          type: string
        autoSync:
          type: boolean
          description: Признак необходимости автоматически синхронизировать данные.
        syncIntervalMinutes:
          type: integer
          description: Период обновления в минутах. Не менее 30 минут.
        settings:
          $ref: '#/components/schemas/IntegrationSettings'

    IntegrationDTO:
      description: Информация об интеграции.
      required:
        - id
        - name
        - autoSync
        - syncIntervalMinutes
        - settings
        - status
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        name:
          description: Название интеграции.
          type: string
        autoSync:
          type: boolean
          description: Признак необходимости автоматически синхронизировать данные.
        syncIntervalMinutes:
          type: integer
          description: Период обновления в минутах.
        settings:
          $ref: '#/components/schemas/IntegrationSettings'
        status:
          $ref: '#/components/schemas/IntegrationStatus'
        lastError:
          type: string
          description: Последняя ошибка при загрузке данных из интеграции.

    IntegrationSettings:
      description: Настройки интеграции.
      type: object
      discriminator:
        propertyName: type
        mapping:
          confluence: '#/components/schemas/ConfluenceSettings'
          cloud_confluence: '#/components/schemas/CloudConfluenceSettings'
          minerva: '#/components/schemas/MinervaSettings'
      required:
        - baseUrl
      properties:
        baseUrl:
          type: string
          description: Базовый URL интеграции.

    ConfluenceSettings:
      description: Настройки интеграции с Confluence Data Center или Confluence Server.
      allOf:
        - $ref: '#/components/schemas/IntegrationSettings'
        - type: object
          required:
            - type
            - space
            - token
          properties:
            type:
              type: string
              enum: [confluence]
              default: confluence
              description: Тип интеграции.
            space:
              type: string
              description: Имя пространства.
            token:
              type: string
              description: Токен доступа из настроек профиля.

    CloudConfluenceSettings:
      description: Настройки интеграции с Confluence Cloud.
      allOf:
        - $ref: '#/components/schemas/IntegrationSettings'
        - type: object
          required:
            - type
            - space
            - baseUrl
            - authBaseUrl
            - siteUrl
          properties:
            type:
              type: string
              enum: [cloud_confluence]
              default: cloud_confluence
              description: Тип интеграции.
            baseUrl:
              type: string
              description: Базовый URL интеграции.
              default: https://api.atlassian.com
              x-overrides: true
            authBaseUrl:
              type: string
              description: Ссылка на сервер аутентификации Atlassian (для использования прокси).
              default: https://auth.atlassian.com
            space:
              type: string
              description: Имя пространства.
            siteUrl:
              type: string
              description: Ссылка на сайт Confluence.

    MinervaSettings:
      description: Настройки интеграции с Minerva Soft.
      allOf:
        - $ref: '#/components/schemas/IntegrationSettings'
        - type: object
          required:
            - type
            - spaceId
            - token
          properties:
            type:
              type: string
              enum: [ minerva ]
              default: minerva
              description: Тип интеграции.
            spaceId:
              type: string
              description: ID проекта
            token:
              type: string
              description: Токен доступа из настроек профиля.
            segment:
              type: string
              description: Для поиска по сегменту

    AddLinkRequest:
      required:
        - link
      properties:
        name:
          type: string
          description: Название источника. Должно быть уникальным в рамках проекта. Если не указано, формируется автоматически.
        link:
          type: string
          description: |
            Ссылка на файл. См. [требования к файлам](https://khub.just-ai.com/docs/upload#file-requirements).
            Если по ссылке HTML-страница, загружается только HTML, без изображений.
        segment:
          type: string
          description: Тег (сегмент) базы знаний. Если указан, источник будет доступен при поиске по этому тегу.
    UpdateLinkRequest:
      required:
        - link
      properties:
        name:
          type: string
          description: Название источника. Если в базе знаний нет источника с таким названием, возвращается ошибка.
        link:
          type: string
          description: |
            Ссылка на файл. См. [требования к файлам](https://khub.just-ai.com/docs/upload#file-requirements).
            Если по ссылке HTML-страница, загружается только HTML, без изображений.
        segment:
          type: string
          description: Тег (сегмент) базы знаний. Если указан, источник будет доступен при поиске по этому тегу.

    AddTextRequest:
      required:
        - text
      properties:
        name:
          type: string
          description: Название источника. Должно быть уникальным в рамках проекта. Если не указано, формируется автоматически.
        text:
          type: string
          description: Текст источника. Максимальный размер указан в [требованиях к файлам](https://khub.just-ai.com/docs/upload#file-requirements).
        segment:
          type: string
          description: Тег (сегмент) базы знаний. Если указан, источник будет доступен при поиске по этому тегу.
    UpdateTextRequest:
      required:
        - text
      properties:
        name:
          type: string
          description: Название источника. Если в базе знаний нет источника с таким названием, возвращается ошибка.
        text:
          type: string
          description: Текст источника. Максимальный размер указан в [требованиях к файлам](https://khub.just-ai.com/docs/upload#file-requirements).
        segment:
          type: string
          description: Тег (сегмент) базы знаний. Если указан, источник будет доступен при поиске по этому тегу.

  parameters:
    queryIdRequired:
      name: queryId
      in: path
      description: Идентификатор запроса на генерацию ответа.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'
    chatIdRequired:
      name: chatId
      in: path
      description: Идентификатор чата в проекте базы знаний.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'
    sourceIdRequired:
      name: sourceId
      in: path
      description: Идентификатор источника в проекте базы знаний.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'
    sourceStatus:
      name: sourceStatus
      in: query
      description: |
        Отбирать источники с указанным статусом индексации. Возможные статусы:
        - `READY_TO_INGEST` — готов для индексации.
        - `PROCESSING` — в обработке.
        - `INGESTED` — проиндексирован.
        - `FAILED_TO_INGEST` — ошибка индексации.
      required: false
      schema:
        type: string
    createDateFrom:
      name: createDateFrom
      in: query
      description: Отбирать источники, добавленные начиная с указанной даты включительно.
      required: false
      schema:
        type: string
        format: date
    createDateTo:
      name: createDateTo
      in: query
      description: Отбирать источники, добавленные до указанной даты включительно.
      required: false
      schema:
        type: string
        format: date
    waitTimeSecondsOptional:
      in: query
      name: waitTimeSeconds
      description: Время ожидания ответа в HTTP-запросе. Используется в long-polling.
      required: false
      schema:
        type: integer
        format: int32
        minimum: 0
        default: 3
        maximum: 30

    integrationIdRequired:
      name: integrationId
      in: path
      description: Идентификатор интеграции.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'

  responses:
    UnauthorizedError:
      description: Неавторизованный запрос или некорректный API-ключ
    ForbiddenError:
      description: Доступ запрещен
    ProjectNotFoundError:
      description: Текущий проект не найден или был удален
    ServerError:
      description: Ошибка сервера
    BadRequestError:
      description: Ошибка в параметрах запроса
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

security:
  - bearerAuth: []
