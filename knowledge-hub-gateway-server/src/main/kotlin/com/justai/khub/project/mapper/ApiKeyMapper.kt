package com.justai.khub.project.mapper

import com.justai.khub.api.fe.model.ApiKeyCreateRequest
import com.justai.khub.api.fe.model.ApiKeyDTO
import com.justai.khub.api.fe.model.ApiRequestSample
import com.justai.khub.api.fe.model.ApiRequestSamples
import com.justai.khub.common.configuration.dto.ApiKeyProperties
import com.justai.khub.common.configuration.dto.KHubUser
import com.justai.khub.common.service.MessageService
import com.justai.khub.common.util.Base62Encoder
import com.justai.khub.project.entity.ProjectEntity
import com.justai.khub.project.entity.ApiKeyEntity
import com.justai.khub.user.enumeration.ApiKeyStatus
import org.apache.commons.lang.text.StrSubstitutor
import org.apache.commons.lang3.RandomStringUtils
import org.springframework.security.crypto.bcrypt.BCrypt
import org.springframework.stereotype.Component
import java.time.LocalDateTime
import java.time.ZoneId

@Component
class ApiKeyMapper(
    private val apiKeyProperties: ApiKeyProperties,
    private val messageService: MessageService,
) {

    fun toEntity(request: ApiKeyCreateRequest, project: ProjectEntity, currentUser: KHubUser): ApiKeyEntity {
        val key = RandomStringUtils.randomAlphabetic(apiKeyProperties.keyLength)
        val hash = generateHash(key)
        return ApiKeyEntity().also {
            it.name = request.name
            it.expiredAt = request.expiredAt?.let { expiredAt -> LocalDateTime.ofInstant(expiredAt, ZoneId.systemDefault()) }
            it.ownerLogin = currentUser.userData!!.login
            it.project = project
            it.status = ApiKeyStatus.ACTIVE
            it.hash = hash
            it.prefix = key.substring(0, apiKeyProperties.visibleKeyLength)
            it.value = key
        }
    }

    fun toDTO(source: ApiKeyEntity, apiKey: String? = null): ApiKeyDTO {
        return ApiKeyDTO(
            id = source.id,
            name = source.name,
            status = determineStatus(source),
            ownerLogin = source.ownerLogin,
            prefix = source.prefix,
            key = apiKey?.let { "${Base62Encoder.encode(source.id)}.${it}" },
            createdAt = source.createdAt.atZone(ZoneId.systemDefault()).toInstant(),
            updatedAt = source.updatedAt.atZone(ZoneId.systemDefault()).toInstant(),
            expiredAt = source.expiredAt?.atZone(ZoneId.systemDefault())?.toInstant(),
            lastUsedAt = source.lastUsedAt?.atZone(ZoneId.systemDefault())?.toInstant(),
            projectId = source.project?.id
        )
    }

    fun toApiRequestSamples(source: ApiKeyEntity): ApiRequestSamples {
        val apiKeyRequestPlaceholders = mapOf(
            "api_key" to toDTO(source, source.value).key
        )
        val samples = apiKeyProperties.requestSamples.map {
            ApiRequestSample(
                StrSubstitutor.replace(it.content, apiKeyRequestPlaceholders),
                messageService.getMessage(it.hint)
            )
        }
        return ApiRequestSamples(samples)
    }

    private fun determineStatus(apiKey: ApiKeyEntity): String {
        if (apiKey.status == ApiKeyStatus.REVOKED) return apiKey.status.toString()
        if (apiKey.expiredAt != null && apiKey.expiredAt!!.isBefore(LocalDateTime.now())) return ApiKeyStatus.EXPIRED.toString()
        return apiKey.status.toString()
    }

    fun generateHash(key: String): String {
        return BCrypt.hashpw(key, BCrypt.gensalt())
    }
}
