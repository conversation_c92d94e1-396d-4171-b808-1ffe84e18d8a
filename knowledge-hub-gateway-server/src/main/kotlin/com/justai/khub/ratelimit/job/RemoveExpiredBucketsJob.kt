package com.justai.khub.ratelimit.job

import com.justai.khub.ratelimit.dto.RateLimitProperties
import com.justai.shutdown.CallbackAwaiter
import com.justai.shutdown.GracefulShutDownAware
import com.justai.shutdown.ShutDownLatch
import io.github.bucket4j.postgresql.PostgreSQLSelectForUpdateBasedProxyManager
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty(value = ["scheduling.enable", "rate-limit.remove-expired-buckets.enabled"], havingValue = "true", matchIfMissing = true)
class RemoveExpiredBucketsJob(
    private val rateLimitProperties: RateLimitProperties,
    private val proxyManager: PostgreSQLSelectForUpdateBasedProxyManager<String>,
) : GracefulShutDownAware {
    private val log = LoggerFactory.getLogger(this::class.java)

    private val shutDownLatch = ShutDownLatch()

    @Scheduled(fixedDelay = 2 * 60 * 60_00)
    fun removeExpiredBuckets() {
        var removedCount = 0
        do {
            shutDownLatch.run {
                removedCount = proxyManager.removeExpired(rateLimitProperties.removeExpiredBuckets.maxToRemoveInOneTransaction)
                if (removedCount > 0) {
                    log.info("Removed {} expired buckets", removedCount)
                }
            }

        } while (removedCount > rateLimitProperties.removeExpiredBuckets.thresholdToContinueRemoving)
    }

    override fun shutDownGracefully(runnable: Runnable) {
        CallbackAwaiter.withCallback(runnable).awaitAsync {
            log.info("Stop RemoveExpiredBucketsJob...")
            shutDownLatch.awaitShutdown()
            log.info("Stop RemoveExpiredBucketsJob... DONE")
        }
    }
}
