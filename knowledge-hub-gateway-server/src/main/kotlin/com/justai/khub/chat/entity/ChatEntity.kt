package com.justai.khub.chat.entity

import com.justai.khub.common.entity.AuditableEntity
import com.justai.khub.project.dto.SearchPipelineConfig
import com.justai.khub.project.entity.ProjectVersionEntity
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import org.hibernate.annotations.Type

@Entity
@Table(name = "chat")
class ChatEntity : AuditableEntity() {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = -1

    @ManyToOne
    lateinit var version: ProjectVersionEntity

    @Column(nullable = false)
    lateinit var name: String

    @Type(JsonType::class)
    @Column(columnDefinition = "jsonb")
    lateinit var searchSettings: SearchPipelineConfig

    @Column(nullable = false)
    var internal: Boolean = false
}
