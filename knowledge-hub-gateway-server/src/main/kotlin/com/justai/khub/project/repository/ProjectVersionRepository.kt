package com.justai.khub.project.repository

import com.justai.khub.project.entity.ProjectVersionEntity
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.EntityGraph
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface ProjectVersionRepository : JpaRepository<ProjectVersionEntity, Long> {
    fun findByProjectIdAndNameAndDeletedAtIsNull(projectId: Long, name: String): ProjectVersionEntity?

    @EntityGraph(attributePaths = ["project"])
    @Query(
        "SELECT v" +
            " FROM ProjectVersionEntity v" +
            " WHERE v.name = :name" +
            " AND v.createdBy.accountId = :accountId" +
            " AND v.project.deletedAt IS NULL"
    )
    fun findAllByAccountAndNameNotDeleted(accountId: Long, name: String, pageable: Pageable): Page<ProjectVersionEntity>

    @Modifying
    @Query("UPDATE ProjectVersionEntity v SET v.sourcesUpdatedAt=:sourcesUpdatedAt WHERE v.id = :id")
    fun updateSourcesUpdatedAt(id: Long, sourcesUpdatedAt: LocalDateTime): Int

}

