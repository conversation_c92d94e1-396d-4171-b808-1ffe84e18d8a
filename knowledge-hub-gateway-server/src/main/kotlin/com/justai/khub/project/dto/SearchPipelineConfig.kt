package com.justai.khub.project.dto

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
enum class SearchPipelineType {
    semantic,
    agent
}

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class CSVProcessorConfig(
    val enabled: Boolean? = true,
    val promptAdjustment: String? =null
)

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes(
    JsonSubTypes.Type(value = AgentSearchPipelineConfig::class, name = "agent"),
    JsonSubTypes.Type(value = SemanticSearchPipelineConfig::class, name = "semantic")
)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
sealed class SearchPipelineConfig {
    abstract val type: SearchPipelineType
    abstract val dao: DaoConfig
    abstract val llm: LLMConfig
    abstract val search: RetrievingConfig?
    abstract val reranker: RerankerConfig?
    abstract val abbreviations: AbbreviationsConfig?
    abstract val showSources: Boolean?
    abstract val csvProcessorConfig: CSVProcessorConfig?
}


@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class AgentSearchPipelineConfig(
    override val type: SearchPipelineType = SearchPipelineType.agent,
    override val dao: DaoConfig,
    override val llm: LLMConfig,
    override val search: RetrievingConfig?,
    override val reranker: RerankerConfig?,
    override val abbreviations: AbbreviationsConfig? = null,
    override val showSources: Boolean? = false,
    override val csvProcessorConfig: CSVProcessorConfig? = null,
    val systemPrompt: String? = null,
) : SearchPipelineConfig()


@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class SemanticSearchPipelineConfig(
    override val type: SearchPipelineType = SearchPipelineType.semantic,
    override val dao: DaoConfig,
    override val llm: LLMConfig,
    override val search: RetrievingConfig?,
    override val reranker: RerankerConfig?,
    override val abbreviations: AbbreviationsConfig? = null,
    override val showSources: Boolean? = false,
    override val csvProcessorConfig: CSVProcessorConfig? = null,
    val responseGenerator: ResponseGeneratorConfig
) : SearchPipelineConfig()
