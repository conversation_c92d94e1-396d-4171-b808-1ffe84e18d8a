package com.justai.khub.integration.dto

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo


@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "type"
)
@JsonSubTypes(
    JsonSubTypes.Type(value = ConfluenceIntegrationSettings::class, name = IntegrationSettings.CONFLUENCE_ID),
    JsonSubTypes.Type(value = CloudConfluenceIntegrationSettings::class, name = IntegrationSettings.CLOUD_CONFLUENCE_ID),
    JsonSubTypes.Type(value = MinervaIntegrationSettings::class, name = IntegrationSettings.MINERVA),
)
sealed interface IntegrationSettings {
    val baseUrl: String

    companion object {
        const val CONFLUENCE_ID = "confluence"
        const val CLOUD_CONFLUENCE_ID = "cloud_confluence"
        const val MINERVA = "minerva"
    }
}
