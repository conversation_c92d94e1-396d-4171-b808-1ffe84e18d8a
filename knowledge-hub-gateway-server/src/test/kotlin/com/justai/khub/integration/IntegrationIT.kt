package com.justai.khub.integration

import com.justai.khub.BaseProjectIT
import com.justai.khub.util.TestDataFactory
import org.apache.commons.lang3.RandomStringUtils
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test


class IntegrationIT : BaseProjectIT() {

    @Test
    fun testCRUD() {
        val createdIntegration = integrationHelper.createIntegration(project.id)
        val foundIntegration = integrationHelper.getIntegrationAndExpectOk(project.id, createdIntegration.id)
        assertEquals(createdIntegration.name, foundIntegration.name)

        val integrationsPage = integrationHelper.getIntegrationsPage(project.id)
        assertTrue(integrationsPage.content.any { it.id == createdIntegration.id })

        val updateRequest = TestDataFactory.integrationDTO("newName-${RandomStringUtils.randomAlphabetic(4)}")
        val updatedIntegration = integrationHelper.updateIntegrationAndExpectOk(project.id, createdIntegration.id, updateRequest)
        assertEquals(updateRequest.name, updatedIntegration.name)
        assertEquals(updateRequest.checkIntervalMinutes, updatedIntegration.checkIntervalMinutes)
        val foundUpdatedIntegration = integrationHelper.getIntegrationAndExpectOk(project.id, createdIntegration.id)
        assertEquals(updateRequest.name, foundUpdatedIntegration.name)

        integrationHelper.deleteIntegrationAndExpectOk(project.id, createdIntegration.id)
        val updatedIntegrationPage = integrationHelper.getIntegrationsPage(project.id)
        assertFalse(updatedIntegrationPage.content.any { it.id == createdIntegration.id })
    }
}
