CREATE TABLE public.integration
(
    id                     BIGINT GE<PERSON>RATED ALWAYS AS IDENTITY PRIMARY KEY,
    version_id             BIGINT       NOT NULL REFERENCES public.project_version (id),
    name                   VARCHAR(512) NOT NULL,
    status                 VARCHAR(255) NOT NULL,
    settings               JSONB        NOT NULL,
    last_checked_at        TIMESTAMP,
    check_interval_minutes INT          NOT NULL,
    auto_ingest            BOOLEAN      NOT NULL,
    auto_sync              BOOLEAN      NOT NULL,
    last_error             VARCHAR,

    created_at             TIMESTAMP    NOT NULL,
    created_by_account_id  BIGINT       NOT NULL,
    created_by_user_id     BIGINT,
    updated_at             TIMESTAMP    NOT NULL,
    updated_by_account_id  BIGINT       NOT NULL,
    updated_by_user_id     BIGINT
);

ALTER SEQUENCE public.integration_id_seq RESTART WITH 15870;
CREATE INDEX idx_integration_status ON public.integration (status);
CREATE INDEX idx_integration_created_by_account_id ON public.integration (created_by_account_id);

