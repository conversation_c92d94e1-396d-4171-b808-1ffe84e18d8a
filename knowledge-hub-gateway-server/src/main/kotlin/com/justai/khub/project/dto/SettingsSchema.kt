package com.justai.khub.project.dto

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo


data class SettingsSchema(
    val guideUrl: LinkSettingDescription? = null,
    val partitions: List<SettingPartition>
)

data class SettingPartition(
    val name: String,
    val title: LocaleObject,
    val menuTitle: LocaleObject?,
    val description: LocaleObject?,
    val more: MoreObject?,
    val sections: Map<String, SettingsSection>
)

data class SettingsSection(
    val title: LocaleObject?,
    val isCard: Boolean?,
    val settings: Map<String, SettingDescription>,
    val description: LocaleObject? = null,
    val nestedSections: List<SettingsSection> = emptyList(),
    val dependsOn: List<DependencyConfig> = listOf()
)

data class LocaleObject(
    val ru: String?,
    val en: String?
)

data class MoreObject(
    val label: LocaleObject,
    val link: LocaleObject
)

enum class SettingType {
    link,

    select,
    cards,

    prompt,
    text,

    number,
    slider,

    switch,
    block,
    accordion,

    file
}

data class DependencyConfig(
    val path: String,
    val value: List<String>,
    val onChangeMessage: LocaleObject? = null
)

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "type"
)
@JsonSubTypes(
    JsonSubTypes.Type(value = LinkSettingDescription::class, name = "link"),
    JsonSubTypes.Type(value = SelectSettingDescription::class, name = "select"),
    JsonSubTypes.Type(value = CardsSettingDescription::class, name = "cards"),
    JsonSubTypes.Type(value = TextSettingDescription::class, name = "text"),
    JsonSubTypes.Type(value = PromptSettingDescription::class, name = "prompt"),
    JsonSubTypes.Type(value = NumberSettingDescription::class, name = "number"),
    JsonSubTypes.Type(value = SliderSettingDescription::class, name = "slider"),
    JsonSubTypes.Type(value = SwitchSettingDescription::class, name = "switch"),
    JsonSubTypes.Type(value = BlockSettingDescription::class, name = "block"),
    JsonSubTypes.Type(value = AccordionSettingDescription::class, name = "accordion"),
    JsonSubTypes.Type(value = FileSettingDescription::class, name = "file")
)
sealed class SettingDescription(
    open val type: SettingType,
    open val required: Boolean = false,
    open val dependsOn: List<DependencyConfig> = listOf()
)

data class LinkSettingDescription(
    val title: LocaleObject,
    val label: LocaleObject? = null,
    val url: LocaleObject
) : SettingDescription(SettingType.link)

data class SelectSettingDescription(
    val label: LocaleObject,
    val value: String? = null,
    val options: List<Option>,
    override val required: Boolean = false,
    override val dependsOn: List<DependencyConfig> = listOf(),
) : SettingDescription(SettingType.select, required, dependsOn)

data class CardsSettingDescription(
    val label: LocaleObject?,
    val value: String? = null,
    val hint: LocaleObject? = null,
    val options: List<Option>,
    override val required: Boolean = false,
    override val dependsOn: List<DependencyConfig> = listOf(),
) : SettingDescription(type = SettingType.cards, required = required, dependsOn = dependsOn)

data class Option(
    val title: LocaleObject,
    val value: String,
    val dependsOn: List<DependencyConfig> = listOf(),
    val description: LocaleObject? = null,
    val changeDependenciesAfterSelect: List<DependencyConfig> = listOf(),
    val img: String? = null
)

data class TextSettingDescription(
    val label: LocaleObject,
    val placeholder: String? = null,
    val value: String? = null,
    val maxLen: Int? = null,
    val hint: LocaleObject? = null,
    val disabled: Boolean = false,
    override val required: Boolean = false,
    override val dependsOn: List<DependencyConfig> = listOf(),
) : SettingDescription(SettingType.text, required, dependsOn)

data class PromptSettingDescription(
    val label: LocaleObject,
    val placeholder: String? = null,
    val value: String? = null,
    val maxLen: Int? = null,
    val hint: LocaleObject? = null,
    override val required: Boolean = false,
    override val dependsOn: List<DependencyConfig> = listOf(),
) : SettingDescription(SettingType.prompt, required,dependsOn)

enum class NumberFormat {
    int,
    double
}

data class SliderSettingDescription(
    val label: LocaleObject,
    val format: NumberFormat,
    val value: Double,
    val min: Double? = null,
    val max: Double? = null,
    val step: Double? = null,
    val visibleStep: Double? = null,
    val hint: LocaleObject? = null,
    override val required: Boolean = false,
    override val dependsOn: List<DependencyConfig> = listOf(),
) : SettingDescription(SettingType.slider, required, dependsOn)

data class NumberSettingDescription(
    val label: LocaleObject,
    val format: NumberFormat,
    val value: Double,
    val maxLen: Int? = null,
    val min: Double? = null,
    val max: Double? = null,
    val step: Double? = null,
    val visibleStep: Double? = null,
    val hint: LocaleObject? = null,
    override val required: Boolean = false,
    override val dependsOn: List<DependencyConfig> = listOf(),
) : SettingDescription(SettingType.number, required, dependsOn)

data class SwitchSettingDescription(
    val label: LocaleObject,
    val value: Boolean,
    val hint: LocaleObject? = null,
    override val required: Boolean = false,
    override val dependsOn: List<DependencyConfig> = listOf(),
) : SettingDescription(SettingType.switch, required, dependsOn)

data class BlockSettingDescription(
    val label: LocaleObject?,
    val nestedSettings: Map<String, SettingDescription>,
    override val required: Boolean = false,
    override val dependsOn: List<DependencyConfig> = listOf(),
) : SettingDescription(type = SettingType.block, required = required, dependsOn = dependsOn)

data class AccordionSettingDescription(
    val label: LocaleObject?,
    val accordion: Map.Entry<String, SettingDescription>? = null,
    val nestedSettings: Map<String, SettingDescription>,
    override val required: Boolean = false,
    override val dependsOn: List<DependencyConfig> = listOf(),
) : SettingDescription(type = SettingType.accordion, required = required, dependsOn = dependsOn)

data class FileSettingDescription(
    val fileName: String?,
    val maxSizeInMb: Int?,
    val acceptFormat: FileFormat?,
    val label: LocaleObject?,
    val template: FileTemplate?,
    val rawData: String? = null,
    val hint: LocaleObject? = null,
    override val required: Boolean = false,
    override val dependsOn: List<DependencyConfig> = listOf(),
) : SettingDescription(type = SettingType.file, required = required, dependsOn = dependsOn)

data class FileTemplate(
    val name: LocaleObject,
    val link: LocaleObject
)

enum class FileFormat {
    json
}
