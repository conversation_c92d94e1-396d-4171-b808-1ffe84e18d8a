package com.justai.khub.common.configuration.dto

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "qa", ignoreUnknownFields = true)
data class QAProperties(
    val testSetGenerationThreadPoolSize: Int,
    val testSetRunThreadPoolSize: Int,
    val defaultGenerationModel: String,
    val defaultEvaluationModel: String,
    val schedulingTimezone: String,
    val useChunksContent: Boolean,
)
