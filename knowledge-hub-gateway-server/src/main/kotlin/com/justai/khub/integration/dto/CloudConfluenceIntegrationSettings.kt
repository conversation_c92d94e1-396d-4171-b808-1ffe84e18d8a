package com.justai.khub.integration.dto

data class CloudConfluenceIntegrationSettings(
    override val baseUrl: String = "https://api.atlassian.com",
    val authBaseUrl: String = "https://auth.atlassian.com",
    override val space: String,
    override val token: String? = null,
    val authState: String = "",
    val refreshToken: String? = null,
    val expiresAt: Long = 0,
    val authorizationUrl: String? = null,
    val clouds: Map<String, String> = emptyMap(),
    val selectedCloudId: String? = null,
    val siteUrl: String,
    val spaceKey: String? = null,
) : IntegrationSettings, ConfluenceSettingsCommon, HasOAuthToken

