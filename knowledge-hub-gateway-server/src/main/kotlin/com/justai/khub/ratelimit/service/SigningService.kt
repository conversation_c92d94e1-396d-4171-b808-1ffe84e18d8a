package com.justai.khub.ratelimit.service

import com.justai.onprem.license.signature.LicenseSignatureService
import org.springframework.stereotype.Service

@Service
class SigningService(
    private val signatureService: LicenseSignatureService?,
) {

    fun signHeaders(headersToSign: List<Pair<String, String>>): String {
        checkNotNull(signatureService) { "License key is absent. Can't sign anything" }

        val stringToSign = headersToSign.sortedBy { it.first.lowercase() }
            .joinToString(separator = "\n") { "${it.first}=${it.second}".lowercase() }

        return signatureService.signStringToBase64(stringToSign)
    }
}
