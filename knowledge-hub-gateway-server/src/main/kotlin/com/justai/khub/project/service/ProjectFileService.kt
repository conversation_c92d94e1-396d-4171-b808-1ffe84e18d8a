package com.justai.khub.project.service

import com.justai.khub.api.fe.model.ProjectFilesSummary
import com.justai.khub.common.connector.IngesterConnector
import com.justai.khub.common.connector.LakefsConnector
import com.justai.khub.common.connector.RagServiceConnector
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.integration.entity.IntegrationEntity
import com.justai.khub.project.dto.ExternalFileShort
import com.justai.khub.project.dto.FilesSummaryByStatus
import com.justai.khub.project.dto.ProjectFileMetadata
import com.justai.khub.project.dto.ProjectFilesFilter
import com.justai.khub.project.entity.ProjectFileEntity
import com.justai.khub.project.entity.ProjectVersionEntity
import com.justai.khub.project.enumeration.ProjectSourceStatus
import com.justai.khub.project.mapper.ProjectFileMapper.resetContentRelatedProperties
import com.justai.khub.project.repository.ProjectFileRepository
import com.justai.khub.project.validator.ProjectFileValidator
import io.lakefs.clients.sdk.ApiException
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.jpa.repository.query.EscapeCharacter
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.nio.file.Files
import java.nio.file.Path
import java.time.LocalDateTime
import kotlin.jvm.optionals.getOrNull

@Service
class ProjectFileService(
    private val projectVersionService: ProjectVersionService,
    private val projectFileRepository: ProjectFileRepository,
    private val lakefsConnector: LakefsConnector,
    private val ragServiceConnector: RagServiceConnector,
    private val ingesterConnector: IngesterConnector,
    private val projectFileValidator: ProjectFileValidator,
    @Value("\${eureka.instance.instance-id}")
    private val instanceId: String
) {

    private val log = LoggerFactory.getLogger(this::class.java)

    @Transactional(readOnly = true)
    fun getFilesSummary(projectId: Long, version: String, accountId: Long): ProjectFilesSummary {
        val versionEntity = projectVersionService.getVersion(projectId, version, accountId) // check access rights
        return projectFileRepository.getFilesSummary(versionEntity.id).first().let {
            ProjectFilesSummary(
                totalFiles = (it[0] as Long).toInt(),
                totalFilesSizeBytes = (it[1] as? Long)?.toInt() ?: 0,
                totalFilesSizeChars = (it[2] as? Long)?.toInt() ?: 0,
            )
        }
    }

    @Transactional(readOnly = true)
    fun getFilesSummaryByStatus(versionIds: List<Long>): List<FilesSummaryByStatus> {
        return projectFileRepository.getFilesSummaryByStatus(versionIds).map {
            FilesSummaryByStatus(
                versionId = (it[0] as Long),
                status = it[1] as ProjectSourceStatus,
                count = (it[2] as Long).toInt(),
                maxUpdatedAt = (it[3] as LocalDateTime),
            )
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    fun addFile(entity: ProjectFileEntity, content: Path): ProjectFileEntity {
        projectFileValidator.validate(entity)
        val version = entity.version
        val repositoryId = version.project.repositoryId ?: throw KhubException(ApiErrorCode.PROJECT_REPOSITORY_NOT_INITIALIZED)
        val createdFile = projectFileRepository.saveAndFlush(entity)

        lakefsConnector.addFile(repositoryId, version.name, entity.pathInStorage, content)
        projectVersionService.onSourcesChanged(version)
        return createdFile
    }

    @Transactional(rollbackFor = [Exception::class])
    fun updateFileContent(entity: ProjectFileEntity, newContent: Path, autoIngest: Boolean): ProjectFileEntity {
        val lockedEntity = projectFileRepository.findForUpdateById(entity.id) ?: throw KhubException(ApiErrorCode.PROJECT_FILE_NOT_FOUND)
        if (lockedEntity.status == ProjectSourceStatus.READY_TO_INGEST || lockedEntity.status == ProjectSourceStatus.PROCESSING) {
            throw KhubException(ApiErrorCode.INGESTION_JOB_ALREADY_STARTED)
        }
        lockedEntity.status = if (autoIngest) ProjectSourceStatus.READY_TO_INGEST else ProjectSourceStatus.NOT_INGESTED
        lockedEntity.resetContentRelatedProperties(Files.size(newContent).toInt())

        return addFile(lockedEntity, newContent).also {
            ingesterConnector.deleteFile(lockedEntity.version.ingestSettings.dao!!.indexName, lockedEntity.pathInStorage)
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    fun resetFileIngestion(file: ProjectFileEntity, autoIngest: Boolean) {
        val lockedEntity = projectFileRepository.findForUpdateById(file.id) ?: throw KhubException(ApiErrorCode.PROJECT_FILE_NOT_FOUND)
        if (lockedEntity.status == ProjectSourceStatus.READY_TO_INGEST || lockedEntity.status == ProjectSourceStatus.PROCESSING) {
            throw KhubException(ApiErrorCode.INGESTION_JOB_ALREADY_STARTED)
        }
        lockedEntity.status = if (autoIngest) ProjectSourceStatus.READY_TO_INGEST else ProjectSourceStatus.NOT_INGESTED
        file.sizeBytes?.let {
            lockedEntity.resetContentRelatedProperties(it)
        }

        ingesterConnector.deleteFile(lockedEntity.version.ingestSettings.dao!!.indexName, lockedEntity.pathInStorage)
        projectFileRepository.saveAndFlush(lockedEntity)
        projectVersionService.onSourcesChanged(lockedEntity.version)
    }

    fun getFileContent(projectId: Long, fileId: Long, accountId: Long): Pair<String, ByteArray> {
        val file = projectFileRepository.findById(fileId).getOrNull() ?: throw KhubException(ApiErrorCode.PROJECT_FILE_NOT_FOUND)
        projectVersionService.getVersion(projectId, file.version.name, accountId) // check access rights
        val repositoryId = file.version.project.repositoryId ?: throw KhubException(ApiErrorCode.PROJECT_REPOSITORY_NOT_INITIALIZED)
        return file.relativePath to lakefsConnector.getFileContent(repositoryId, file.version.name, file.pathInStorage)
    }

    fun getParsedContent(projectId: Long, fileId: Long, accountId: Long): Pair<String, ByteArray?> {
        val (version, file) = checkAccessAndIsIngested(projectId, fileId, accountId)
        return lakefsConnector.getParsedContent(version.project.repositoryId!!, version.name, file.pathInStorage)
    }

    fun getChunksArchive(projectId: Long, fileId: Long, accountId: Long): Pair<String, ByteArray?> {
        val (version, file) = checkAccessAndIsIngested(projectId, fileId, accountId)
        val fileName = "${file.relativePath.substringBeforeLast(".")}_chunks.zip"
        return fileName to ragServiceConnector.getChunksArchive(
            version.ingestSettings.dao!!.indexName,
            file.pathInStorage
        )
    }

    private fun checkAccessAndIsIngested(
        projectId: Long,
        fileId: Long,
        accountId: Long
    ): Pair<ProjectVersionEntity, ProjectFileEntity> {
        val file = projectFileRepository.findById(fileId).getOrNull() ?: throw KhubException(ApiErrorCode.PROJECT_FILE_NOT_FOUND)
        val version = projectVersionService.getVersion(projectId, file.version.name, accountId) // check access rights
        if (file.status != ProjectSourceStatus.INGESTED) {
            throw KhubException(ApiErrorCode.PROJECT_FILE_NOT_INGESTED)
        }
        if (file.version.project.repositoryId == null) {
            throw KhubException(ApiErrorCode.PROJECT_REPOSITORY_NOT_INITIALIZED)
        }
        return version to file
    }


    @Transactional(rollbackFor = [Exception::class])
    fun deleteFile(projectId: Long, fileId: Long, accountId: Long): Boolean {
        val fileWithChildren = getFileAndChildrenForDelete(projectId, fileId, accountId)
        if (fileWithChildren.isNotEmpty()) {
            projectFileRepository.deleteAll(fileWithChildren)
            projectVersionService.onSourcesChanged(fileWithChildren.first().version)
        }
        fileWithChildren.forEach {
            ingesterConnector.deleteFile(it.version.ingestSettings.dao!!.indexName, it.pathInStorage)
            it.deleteFromLakeFs()
        }
        return true
    }

    private fun getFileAndChildrenForDelete(projectId: Long, fileId: Long, accountId: Long): List<ProjectFileEntity> {
        val file = projectFileRepository.findById(fileId).getOrNull() ?: return emptyList()
        val version = projectVersionService.getVersion(projectId, file.version.name, accountId)
        if (file.status == ProjectSourceStatus.PROCESSING) {
            throw KhubException(ApiErrorCode.DELETE_PROCESSING_FILE_ERROR)
        }
        val children = projectFileRepository.findAllByParentIdAndVersionId(version.id, file.id)
            .onEach { childFile -> getFileAndChildrenForDelete(projectId, childFile.id, accountId) }

        return listOf(file) + children
    }

    @Transactional(readOnly = true)
    fun getFiles(filter: ProjectFilesFilter): Page<ProjectFileEntity> {
        val version = projectVersionService.getVersion(filter.projectId, filter.versionName, filter.accountId)
        val statusFilter = filter.statuses.ifEmpty { ProjectSourceStatus.entries }
        return if (filter.namePart.isNullOrEmpty())
            projectFileRepository.findAllByVersionIdAndStatusIn(version.id, statusFilter, filter.pageable)
        else {
            val escapedName = EscapeCharacter.of('\\').escape(filter.namePart)!!
            projectFileRepository.findAllByVersionIdAndNameContainsAndStatusIn(
                version.id,
                statusFilter,
                escapedName,
                filter.pageable
            )
        }
    }

    @Transactional(readOnly = true)
    fun getFilesByStoragePaths(version: ProjectVersionEntity, storagePaths: Iterable<String>): List<ProjectFileEntity> {
        return projectFileRepository.findAllByVersionIdAndPathInStorage(version.id, storagePaths)
    }

    @Transactional(readOnly = true)
    fun getFilesByStoragePath(version: ProjectVersionEntity, pathInStorage: String): ProjectFileEntity? {
        return projectFileRepository.findAllByVersionIdAndPathInStorage(version.id, listOf(pathInStorage)).firstOrNull()
    }

    @Transactional(rollbackFor = [Exception::class])
    fun startProcessing(file: ProjectFileEntity): Boolean {
        return projectFileRepository.updateStatusAndErrorMessage(
            file.id,
            listOf(ProjectSourceStatus.READY_TO_INGEST, ProjectSourceStatus.FAILED_TO_INGEST),
            ProjectSourceStatus.PROCESSING,
            null
        ) > 0
    }

    @Transactional(rollbackFor = [Exception::class])
    fun failProcessing(file: ProjectFileEntity, errorMessage: String): Boolean {
        return projectFileRepository.updateStatusAndErrorMessage(
            file.id,
            listOf(ProjectSourceStatus.PROCESSING),
            ProjectSourceStatus.FAILED_TO_INGEST,
            errorMessage
        ) > 0
    }

    @Transactional(rollbackFor = [Exception::class])
    fun finishProcessing(file: ProjectFileEntity): Boolean {
        return projectFileRepository.updateStatusAndErrorMessage(
            file.id,
            listOf(ProjectSourceStatus.PROCESSING),
            ProjectSourceStatus.INGESTED,
            null
        ) > 0
    }

    @Transactional(rollbackFor = [Exception::class])
    fun releaseLockedActiveFiles(): Int {
        return projectFileRepository.updateDocumentsByStatusAndLastLockedBy(
            ProjectSourceStatus.PROCESSING,
            ProjectSourceStatus.NOT_INGESTED,
            instanceId
        )
    }

    @Transactional(rollbackFor = [Exception::class])
    fun releaseAllTimedOutFiles(lastUpdateBefore: LocalDateTime): Int {
        return projectFileRepository.releaseTimedOutFiles(
            ProjectSourceStatus.PROCESSING,
            ProjectSourceStatus.NOT_INGESTED,
            lastUpdateBefore
        )
    }

    @Transactional(rollbackFor = [Exception::class])
    fun setSizeCharsAndParsedFileName(fileId: Long, sizeChars: Int, parsedFileName: String) {
        projectFileRepository.setSizeCharsAndParsedFileName(fileId, sizeChars, parsedFileName)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun setTokensUsage(fileId: Long, promptTokens: Int, completionTokens: Int) {
        projectFileRepository.setTokensUsage(fileId, promptTokens, completionTokens)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun setMetadata(fileId: Long, metadata: ProjectFileMetadata) {
        projectFileRepository.setMetadata(fileId, metadata)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun updateStatusAndErrorMessage(fileId: Long, newStatus: ProjectSourceStatus, errorMessage: String? = null): Int {
        return projectFileRepository.updateStatusAndErrorMessage(fileId, ProjectSourceStatus.entries, newStatus)
    }

    @Transactional(readOnly = true)
    fun getFile(fileId: Long): ProjectFileEntity {
        return projectFileRepository.findById(fileId).getOrNull() ?: throw KhubException(ApiErrorCode.PROJECT_FILE_NOT_FOUND)
    }

    @Transactional(readOnly = true)
    fun getFiles(fileIds: Iterable<Long>): List<ProjectFileEntity> {
        return projectFileRepository.findAllById(fileIds)
    }

    @Transactional(readOnly = true)
    fun getShortExternalByIntegration(integration: IntegrationEntity): List<ExternalFileShort> {
        return projectFileRepository.getFilesByIntegrationId(integration.id)
            .map {
                ExternalFileShort(
                    id = it.id,
                    externalId = it.externalId!!,
                    relativePath = it.relativePath,
                    contentVersion = it.contentVersion,
                    parentId = it.parent?.id,
                    externalLink = it.metadata.externalLink
                )
            }
    }

    @Transactional(readOnly = true)
    fun findTotalIngestedChars(versionId: Long): Int {
        return projectFileRepository.findIngestedCharsByVersionIdAndStatus(versionId, ProjectSourceStatus.INGESTED)
    }

    @Transactional(readOnly = true)
    fun findAllIngested(versionId: Long): List<ProjectFileEntity> {
        val pagination = PageRequest.of(0, Integer.MAX_VALUE)
        return projectFileRepository.findAllByVersionIdAndStatusIn(
            versionId,
            listOf(ProjectSourceStatus.INGESTED),
            pagination
        ).content
    }

    @Transactional(readOnly = true)
    fun findIngestedFilesCount(versionId: Long): Long {
        return projectFileRepository.countByVersionIdAndStatusIn(versionId, listOf(ProjectSourceStatus.INGESTED))
    }

    private fun ProjectFileEntity.deleteFromLakeFs() {
        this.version.project.repositoryId?.let { repo ->
            try {
                lakefsConnector.deleteFile(repo, this.version.name, this.pathInStorage)
            } catch (ex: ApiException) {
                if (ex.code == 404) {
                    log.warn("Can not delete ${this.pathInStorage}: file not found or has already deleted")
                } else {
                    throw ex
                }
            }
        }
    }
}
