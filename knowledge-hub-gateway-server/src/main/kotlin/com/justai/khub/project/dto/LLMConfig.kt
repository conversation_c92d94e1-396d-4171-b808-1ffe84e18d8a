package com.justai.khub.project.dto

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
enum class LLMProvider {
    openai, mlp
}

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class LLMConfig(
    val llmProvider: LLMProvider? = null,
    val model: String? = null,
    val modelId: String? = null,
    val accountId: Int? = null,
    val frequencyPenalty: Double = 0.0,
    val maxTokens: Int = 1000,
    val presencePenalty: Double = 0.0,
    val temperature: Double = 0.0,
    val topP: Double = 1.0,
    val contextWindow: Int = 16000
)
