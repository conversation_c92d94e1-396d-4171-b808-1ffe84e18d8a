package com.justai.khub.util

import com.justai.khub.ApiClient
import com.justai.khub.api.*
import com.justai.khub.config.TestConfig
import org.springframework.stereotype.Service

@Service
class BBTContextFactory(
    private val testConfig: TestConfig
) {

    fun bbtContext(auth: Auth): BBTContext {
        val restTemplate = RestTemplateFactory.createRestTemplate(auth, testConfig.gateway.baseUrl)
        val apiClient = ApiClient(restTemplate)
            .setBasePath(testConfig.gateway.baseUrl)
        return BBTContext(
            apiKeyApi = ApiKeyApi(apiClient),
            chatApi = ChatApi(apiClient),
            ingestApi = IngestApi(apiClient),
            projectFilesApi = ProjectFilesApi(apiClient),
            projectsApi = ProjectsApi(apiClient),
            reportApi = ReportApi(apiClient),
            retrieveApi = RetrieveApi(apiClient),
            systemApi = SystemApi(apiClient),
            userApi = UserApi(apiClient),
            attachmentsApi = AttachmentsApi(apiClient),
            qaApi = QaApi(apiClient)
        )
    }
}
