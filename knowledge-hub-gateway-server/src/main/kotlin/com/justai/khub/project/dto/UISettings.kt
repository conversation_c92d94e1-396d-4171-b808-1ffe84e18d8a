package com.justai.khub.project.dto

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class ChunkMethod(
    val type: ChunkerType = ChunkerType.sentence
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class UIIndexation(
    val type: String? = null,
    val chunkMethod: ChunkMethod = ChunkMethod(),
    val chunker: ChunkerConfig = ChunkerConfig(),
    val llm: LLMConfig = LLMConfig()
)


@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class UIRephrase(
    val rephraseQuery: Boolean? = false,
    val prompt: String? = null,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class UIHistory(
    var useHistory: Boolean? = true,
    val historyCondensePrompt: String? = null,
    val historyMaxTokens: Int? = null,
    val historyMinUserMessages: Int? = null
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class UIRetrieving(
    val rephrase: UIRephrase? = null,
    var history: UIHistory? = UIHistory(),
    val similarityTopK: Int = 5,
    val candidateRadius: Int = 0,
    val metadataSearch: Boolean? = false,
    val fullText: FullTextSearchConfig? = null
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class UIReranker(
    val useReranker: Boolean? = false,
    val type: RerankerType? = RerankerType.manual,
    val model: String? = null,
    val minScore: Int? = -5,
    val maxChunks: Int? = null,
    val scoreReductionLimit: Int? = null,
    val maxChunksPerDoc: Int? = null
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class UIFileData(
    val fileName: String,
    val rawData: String
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class UISources(
    val showSources: Boolean = false
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class UISearch(
    val type: SearchPipelineType = SearchPipelineType.semantic,
    val systemPrompt: String? = null,
    val retrieving: UIRetrieving = UIRetrieving(),
    val reranker: UIReranker? = null,
    val abbreviations: UIFileData? = null,
    val advanced: UISearchAdvancedSettings? = null
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class UIGeneration(
    val llm: LLMConfig,
    val sources: UISources = UISources(),
    val systemPrompt: String? = null,
    val advanced: UIGenerationAdvancedSettings? = null,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class UIGenerationAdvancedSettings(
    val csvProcessorPromptAdjustment: String? = null,
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class UISearchAdvancedSettings(
    val csvProcessingEnabled: Boolean? = true
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class UISettings(
    val search: UISearch,
    val generation: UIGeneration,
    val indexation: UIIndexation? = null // empty for chat settings
)
