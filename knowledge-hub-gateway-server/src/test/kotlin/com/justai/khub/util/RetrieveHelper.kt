package com.justai.khub.util

import com.justai.khub.api.public.model.RetrieveChunksFromChatRequest
import com.justai.khub.api.public.model.RetrieveChunksRequest
import com.justai.khub.api.public.model.RetrievingSettings
import com.justai.khub.common.configuration.SecurityConfiguration.Companion.INTERNAL_URL_PREFIX
import com.justai.khub.common.util.JSON
import com.justai.security.session.auth.JustSessionAuthentication
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors
import org.springframework.stereotype.Component
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.ResultActions
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post

@Component
class RetrieveHelper {
    @Autowired
    protected lateinit var mvc: MockMvc

    @Autowired
    protected lateinit var defaultAuth: JustSessionAuthentication

    fun retrieveChunks(
        projectId: Long,
        query: String,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ResultActions {
        val request = RetrieveChunksRequest(query = query)
        return mvc.perform(
            post("$INTERNAL_URL_PREFIX/projects/$projectId/retrieve")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
    }

    fun retrieveChunksForDefaultChat(
        projectId: Long,
        query: String,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ResultActions {
        val request = RetrieveChunksRequest(query = query)
        return mvc.perform(
            post("$INTERNAL_URL_PREFIX/projects/$projectId/chat/default/retrieve")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
    }
}
