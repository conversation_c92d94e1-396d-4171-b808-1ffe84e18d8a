package com.justai.khub.billing.repository

import com.justai.khub.billing.entity.BillableOperationEntity
import com.justai.khub.billing.enumeration.BillableOperationStatus
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.time.Instant
import java.time.LocalDateTime

@Repository
interface BillableOperationRepository : JpaRepository<BillableOperationEntity, Long> {

    fun findByStatusAndUpdatedAtBefore(status: BillableOperationStatus, updatedAt: LocalDateTime): List<BillableOperationEntity>

    @Modifying
    @Query(
        "UPDATE BillableOperationEntity o" +
            " SET o.status=:newStatus, o.updatedAt=:updatedAt, o.comment = :comment" +
            " WHERE o.id = :operationId AND o.status IN :expectedStatuses"
    )
    fun updateStatus(
        operationId: Long,
        expectedStatuses: List<BillableOperationStatus>,
        newStatus: BillableOperationStatus,
        comment: String? = null,
        updatedAt: LocalDateTime = LocalDateTime.now()
    ): Int
}
