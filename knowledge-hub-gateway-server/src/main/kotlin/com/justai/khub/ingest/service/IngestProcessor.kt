package com.justai.khub.ingest.service

import com.justai.khub.attachment.properties.AttachmentsProperties
import com.justai.khub.attachment.service.AttachmentService
import com.justai.khub.common.configuration.dto.IngestProperties
import com.justai.khub.common.connector.IngesterConnector
import com.justai.khub.common.connector.ParsingServiceConnector
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.common.service.MetricsService
import com.justai.khub.common.util.CommonUtils.extractErrorCode
import com.justai.khub.ingest.dto.ParsingTaskDTO
import com.justai.khub.project.dto.ChunkerType
import com.justai.khub.project.dto.FileLink
import com.justai.khub.project.dto.IngestPipelineConfig
import com.justai.khub.project.entity.ProjectFileEntity
import com.justai.khub.project.enumeration.ProjectSourceStatus
import com.justai.khub.project.service.ProjectFileService
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.Duration
import java.time.LocalDateTime
import java.util.concurrent.atomic.AtomicInteger
import kotlin.math.max

@Service
class IngestProcessor(
    private val projectFileService: ProjectFileService,
    private val metricsService: MetricsService,
    private val parsingServiceConnector: ParsingServiceConnector,
    private val ingesterConnector: IngesterConnector,
    private val attachmentService: AttachmentService,
    private val attachmentsProperties: AttachmentsProperties,
    private val ingestProperties: IngestProperties,
) {
    private val log = LoggerFactory.getLogger(this::class.java)

    fun ingestFile(file: ProjectFileEntity) {
        val lastUpdatedAt = file.updatedAt
        val updated = projectFileService.startProcessing(file)
        if (!updated) {
            log.info("Skip ingesting file: '{}' (id = {}): already processed", file.relativePath, file.id)
            return
        }
        log.info("Started ingesting file '{}' (id = {})", file.relativePath, file.id)
        metricsService.ingestFileWaitingTimeTimer().record(Duration.between(lastUpdatedAt, LocalDateTime.now()))
        metricsService.ingestFileTimer().record(Runnable {
            try {
                val ingestedChars = AtomicInteger(projectFileService.findTotalIngestedChars(file.version.id))
                val isFinished = ingestFile(ingestedChars, file)
                projectFileService.finishProcessing(file)
                if (isFinished) {
                    log.info("Successfully finished ingesting file '{}' (id = {})", file.relativePath, file.id)
                } else {
                    log.info("Cancelled ingesting file '{}' (id = {})", file.relativePath, file.id)
                }

            } catch (ex: Exception) {
                handleFileIngestError(file, ex)
                log.warn("Failed to ingest file '{}' (id = {}): {}", file.relativePath, file.id, ex.message)
            }
        })
    }

    private fun ingestFile(alreadyIngestedChars: AtomicInteger, file: ProjectFileEntity): Boolean {
        val repositoryId = file.version.project.repositoryId ?: throw KhubException(ApiErrorCode.PROJECT_REPOSITORY_NOT_INITIALIZED)
        val fileLink = FileLink(repositoryId = repositoryId, branchName = file.version.name, path = file.pathInStorage)

        val parsingTask = parseFile(fileLink)
        if (Thread.currentThread().isInterrupted) {
            throw InterruptedException()
        }
        var actualFileState = projectFileService.getFile(file.id)
        if (actualFileState.status != ProjectSourceStatus.PROCESSING) {
            return false
        }
        if (parsingTask.parsedFile == null || parsingTask.parsedContentLength == null) {
            log.error("Failed to parse file '{}' (id = {}). Status = {}, error = {}", file.relativePath, file.id, parsingTask.state, parsingTask.error)
            throw KhubException(ApiErrorCode.MISSED_PARSED_FILE_ERROR)
        } else {
            log.info("Finished parsing file '{}' (id = {}). Status = {}", file.relativePath, file.id, parsingTask.state)
        }
        projectFileService.setSizeCharsAndParsedFileName(file.id, parsingTask.parsedContentLength, parsingTask.parsedFile)
        attachmentService.replaceAttachments(file, parsingTask.attachmentIds ?: listOf())
        val versionIngestSettings = file.version.ingestSettings
        updateIngestedCharsAndCheckLLMChunkerLimits(parsingTask, file, versionIngestSettings, alreadyIngestedChars)
        val ingestResponse = ingesterConnector.ingest(file, fileLink, parsingTask.parsedFile, versionIngestSettings)
        actualFileState = projectFileService.getFile(file.id)
        if (actualFileState.status != ProjectSourceStatus.PROCESSING) {
            return false
        }
        val totalPromptTokensUsed = ingestResponse.tokensUsage.sumOf { it.promptTokens }
        val totalCompletionTokensUsed = ingestResponse.tokensUsage.sumOf { it.completionTokens }
        projectFileService.setTokensUsage(file.id, totalPromptTokensUsed, totalCompletionTokensUsed)
        return true
    }

    private fun updateIngestedCharsAndCheckLLMChunkerLimits(
        parsingTask: ParsingTaskDTO,
        file: ProjectFileEntity,
        versionIngestSettings: IngestPipelineConfig,
        alreadyIngestedChars: AtomicInteger
    ) {
        val additionalIngestedChars = if (file.status == ProjectSourceStatus.INGESTED && file.sizeChars != null) {
            max(0, parsingTask.parsedContentLength!! - file.sizeChars!!)
        } else {
            parsingTask.parsedContentLength!!
        }
        if (ingestProperties.chunker.llmChunkerTotalCharsLimit > 0
            && alreadyIngestedChars.addAndGet(additionalIngestedChars) > ingestProperties.chunker.llmChunkerTotalCharsLimit
            && versionIngestSettings.chunker.type == ChunkerType.llm
        ) {
            alreadyIngestedChars.addAndGet(-additionalIngestedChars)
            throw KhubException(ApiErrorCode.LLM_CHUNKING_LIMIT_EXCEEDED)
        }
    }

    private fun parseFile(fileLink: FileLink, currentRetry: Int = 0): ParsingTaskDTO {
        val MAX_RETRIES = 2
        val config = mapOf(
            "save_tables" to "true",
            "with_attachments" to "${attachmentsProperties.enabled}",
            "attached_image_min_width" to "75",
            "attached_image_min_height" to "75",
        )
        val parsingTask = parsingServiceConnector.startParsing(fileLink, config)
        val finishedTask = try {
            parsingServiceConnector.waitTaskFinish(parsingTask, ingestProperties.parser.parsingTimeoutSeconds)
        } catch (ex: KhubException) {
            if (ex.errors.any { it.error == ApiErrorCode.PARSING_TIMEOUT }) {
                parsingServiceConnector.cancelTask(parsingTask.id)
            }
            throw ex
        } catch (ex: InterruptedException) {
            Thread.interrupted() // clear interrupted flag to be able to use apache client in parsingServiceConnector
            log.warn(
                "Cancelling parsing file '{}:{}:{}'",
                fileLink.repositoryId,
                fileLink.branchName,
                fileLink.repositoryId
            )
            parsingServiceConnector.cancelTask(parsingTask.id)
            Thread.currentThread().interrupt()
            throw ex
        }
        return if (finishedTask.error == ApiErrorCode.SERVER_SHUTTING_DOWN.code && currentRetry < MAX_RETRIES) {
            parseFile(fileLink, currentRetry + 1)
        } else {
            finishedTask
        }
    }

    private fun handleFileIngestError(file: ProjectFileEntity, ex: Exception) {
        log.error("Error ingesting file '{}' (id = {})", file.relativePath, file.id, ex)
        val errorCode = ex.extractErrorCode(ApiErrorCode.INGEST_ERROR.code)
        metricsService.incrementIngestFileErrorsCounter()
        projectFileService.failProcessing(file, errorCode)
    }
}
