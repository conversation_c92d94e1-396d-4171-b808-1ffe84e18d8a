package com.justai.khub.util

import com.justai.khub.project.dto.IngestPipelineConfig
import com.justai.khub.project.dto.SearchPipelineConfig
import com.justai.khub.query.dto.UserHistory

object RagConnectorDump {

    data class QueryDump(
        val message: String,
        val segment: String?,
        val conversationId: String,
        val searchPipelineConfig: SearchPipelineConfig,
        val ingestSettings: IngestPipelineConfig,
        val history: UserHistory? = null
    )
}
