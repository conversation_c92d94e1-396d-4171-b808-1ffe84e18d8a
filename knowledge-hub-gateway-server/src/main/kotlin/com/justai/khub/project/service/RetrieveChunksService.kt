package com.justai.khub.project.service

import com.justai.khub.attachment.service.AttachmentService
import com.justai.khub.chat.mapper.ChatMapper
import com.justai.khub.chat.repository.UsedChunksRepository
import com.justai.khub.chat.service.ChatHistoryService
import com.justai.khub.chat.service.ChatService
import com.justai.khub.common.connector.RagServiceConnector
import com.justai.khub.common.dto.DocumentChunk
import com.justai.khub.common.dto.DocumentChunkWithSource
import com.justai.khub.integration.dto.CloudConfluenceIntegrationSettings
import com.justai.khub.integration.dto.ConfluenceIntegrationSettings
import com.justai.khub.integration.dto.MinervaIntegrationSettings
import com.justai.khub.integration.entity.IntegrationEntity
import com.justai.khub.integration.service.IntegrationService
import com.justai.khub.project.dto.SearchPipelineConfig
import com.justai.khub.project.entity.ProjectFileEntity
import com.justai.khub.project.entity.ProjectVersionEntity
import com.justai.khub.query.dto.UserHistory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class RetrieveChunksService(
    private val projectVersionService: ProjectVersionService,
    private val chatService: ChatService,
    private val chatHistoryService: ChatHistoryService,
    private val chatMapper: ChatMapper,
    private val ragServiceConnector: RagServiceConnector,
    private val usedChunksRepository: UsedChunksRepository,
    private val projectFileService: ProjectFileService,
    private val integrationService: IntegrationService,
    private val attachmentService: AttachmentService
) {
    fun retrieveChunks(
        query: String,
        projectId: Long,
        versionName: String,
        topK: Int?,
        accountId: Long
    ): List<DocumentChunkWithSource> {
        val version = projectVersionService.getVersion(projectId, versionName, accountId)
        val usedChunks = ragServiceConnector.retrieveChunks(
            query = query,
            searchConfig = version.searchSettings,
            ingestConfig = version.ingestSettings,
            topK = topK
        ).chunks.map { chatMapper.toDocumentChunk(it) }
        return enrichChunksBySources(usedChunks, version)
    }

    fun retrieveChunks(
        query: String,
        version: ProjectVersionEntity,
        conversationId: String? = null,
        customSearchSettings: SearchPipelineConfig? = null,
        history: UserHistory? = null,
        createPublicLinksForSources: Boolean = false
    ): List<DocumentChunkWithSource> {
        val usedChunks = ragServiceConnector.retrieveChunks(
            query = query,
            searchConfig = customSearchSettings ?: version.searchSettings,
            ingestConfig = version.ingestSettings,
            history = history,
        ).chunks.map { chatMapper.toDocumentChunk(it) }
        return enrichChunksBySources(usedChunks, version, createPublicLinksForSources)
    }

    fun retrieveChunksForDefaultChat(
        query: String,
        projectId: Long,
        versionName: String,
        topK: Int?,
        accountId: Long
    ): List<DocumentChunkWithSource> {
        val version = projectVersionService.getVersion(projectId, versionName, accountId)
        val chat = chatService.getDefaultChat(version)
        val usedChunks = ragServiceConnector.retrieveChunks(
            query = query,
            searchConfig = chat?.searchSettings ?: version.searchSettings,
            ingestConfig = version.ingestSettings,
            topK = topK,
            conversationId = chat?.id?.toString()
        ).chunks.map { chatMapper.toDocumentChunk(it) }
        return enrichChunksBySources(usedChunks, version)
    }

    @Transactional(readOnly = true)
    fun retrieveChunksForRecord(projectId: Long, chatId: Long, recordId: Long, accountId: Long): List<DocumentChunkWithSource>? {
        val record = chatHistoryService.getChatRecord(projectId, chatId, recordId, accountId)
        val usedChunks = usedChunksRepository.findByChatHistoryRecordId(record.id)?.chunks?.chunks ?: return null
        return enrichChunksBySources(usedChunks, record.chat.version)
    }

    private fun enrichChunksBySources(
        usedChunks: List<DocumentChunk>,
        version: ProjectVersionEntity,
        createPublicLinksForSources: Boolean = false
    ): List<DocumentChunkWithSource> {
        if (usedChunks.isEmpty()) return emptyList()

        val usedFiles = projectFileService.getFilesByStoragePaths(version, usedChunks.map { it.docId })
        val filesWithIntegration = usedFiles.filter { it.integrationId != null }.associateBy { it.id }
        val usedFilesByPath = usedFiles.associateBy { it.pathInStorage }
        val integrationById = initIntegrations(filesWithIntegration)
        val usedChunksWithSources = usedChunks.map { chunk ->
            val sourceFile = usedFilesByPath[chunk.docId] ?: throw IllegalStateException("No source for doc id = ${chunk.docId}")
            val sourceFileLink = filesWithIntegration[sourceFile.id]?.let { integrationById[it.integrationId] }?.let {
                when (it.settings) {
                    is ConfluenceIntegrationSettings,
                    is CloudConfluenceIntegrationSettings,
                    is MinervaIntegrationSettings -> sourceFile.metadata.externalLink
                }
            }
            chatMapper.toChunkWithSource(chunk, sourceFile, sourceFileLink)
        }
        if (createPublicLinksForSources) {
            val sourcesWithoutLinks = usedChunksWithSources.mapNotNullTo(HashSet()) { if (it.source.externalLink == null) usedFilesByPath[it.source.path] else null }
            val newPublicLinksBySourceId = attachmentService.createAttachmentLinksForFiles(sourcesWithoutLinks)
            usedChunksWithSources.forEach { chunk ->
                if (chunk.source.externalLink == null) {
                    chunk.source.externalLink = newPublicLinksBySourceId[chunk.source.sourceId]
                }
            }
        }
        return usedChunksWithSources
    }

    private fun initIntegrations(filesWithIntegration: Map<Long, ProjectFileEntity>): Map<Long, IntegrationEntity> {
        return if (filesWithIntegration.isNotEmpty()) {
            integrationService
                .findIntegrationsByIds(filesWithIntegration.mapNotNullTo(HashSet()) { it.value.integrationId })
                .associateBy { it.id }
        } else emptyMap()
    }
}
