package com.justai.khub.integration.service

import com.fasterxml.jackson.databind.node.ObjectNode
import com.justai.khub.common.configuration.dto.IntegrationProperties
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.integration.dto.*
import com.justai.khub.integration.entity.IntegrationEntity
import com.justai.khub.project.dto.ExternalFileShort
import com.justai.khub.project.service.ProjectFileService
import com.justai.loadbalancer.annotations.HttpExternal
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.*
import org.slf4j.LoggerFactory
import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.*
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate
import java.time.Instant


private const val REFRESH_TOKEN_BEFORE_SECONDS_EXPIRATION: Long = 60 * 5

@OptIn(ExperimentalCoroutinesApi::class)
@Service
class ConfluenceIntegrationChecker(
    @HttpExternal private val confluenceRestTemplate: RestTemplate,
    private val integrationProperties: IntegrationProperties,
    private val projectFilesService: ProjectFileService,
    private val externalUpdateApplier: ExternalUpdateApplier,
    private val atlassianService: AtlassianService,
    private val integrationService: IntegrationService,
) {
    private val log = LoggerFactory.getLogger(this::class.java)

    suspend fun checkIntegration(integration: IntegrationEntity, settings: ConfluenceSettingsCommon) {
        val actualSettings = actualizeToken(integration, settings)

        val headers = HttpHeaders().also { it.set("Authorization", "Bearer ${actualSettings.token}") }
        checkAuthentication(headers, actualSettings)
        val space = getSpace(headers, actualSettings)
        if (settings is CloudConfluenceIntegrationSettings)
            integrationService.updateSpaceKey(integration, space.key)
        val existedFilesShort = projectFilesService.getShortExternalByIntegration(integration).associateBy { it.externalId }
        val foundExternalFileIds = mutableSetOf<String>()
        var numChanges = 0
        loadAllPages(headers, actualSettings.apiUrlPrefix, space.key).map { batch ->
            batch.map { fillAttachments(headers, actualSettings, it) }
        }.collect { batch ->
            batch.forEach { page ->
                foundExternalFileIds += page.content.id
                page.attachments.mapTo(foundExternalFileIds) { it.attachment.id }
            }
            numChanges += processBatch(existedFilesShort, batch, integration)
        }
        val deletedFiles = existedFilesShort.filterNot { foundExternalFileIds.contains(it.key) }
        numChanges += deletedFiles.size
        deletedFiles.values.forEach { externalUpdateApplier.deleteFile(it, integration) }
        log.info("Found {} changed source for integration {}", numChanges, integration.id)
    }

    private suspend fun actualizeToken(integration: IntegrationEntity, settings: ConfluenceSettingsCommon): ConfluenceSettingsCommon {
        return when (settings) {
            is CloudConfluenceIntegrationSettings -> {
                if (settings.expiresAt < Instant.now().plusSeconds(REFRESH_TOKEN_BEFORE_SECONDS_EXPIRATION).epochSecond) {
                    val newToken = atlassianService.refreshAccessToken(
                        integrationSettings = settings,
                        refreshToken = settings.refreshToken ?: throw KhubException(ApiErrorCode.INVALID_ACCESS_TOKEN),
                    )
                    integrationService.updateOAuthToken(integration = integration, newToken = newToken.accessToken, expiresIn = newToken.expiresIn, refreshToken = newToken.refreshToken)
                    settings.copy(token = newToken.accessToken)
                } else settings
            }
            is ConfluenceIntegrationSettings -> settings
        }
    }

    private fun checkAuthentication(headers: HttpHeaders, settings: ConfluenceSettingsCommon) {
        val response = confluenceRestTemplate
            .exchange("${settings.apiUrlPrefix}/rest/api/user/current", HttpMethod.GET, HttpEntity<Void>(headers), ObjectNode::class.java)
        if (response.statusCode != HttpStatus.OK) {
            log.warn("Confluence authentication error: Status code: {}, response body: {}", response.statusCode, response.body)
            throw KhubException(ApiErrorCode.INTEGRATION_CHECK_ERROR)
        }
        if (response.body?.get("type")?.asText() == "anonymous") {
            log.warn("Confluence authentication error: unexpected anonymous authentication, response body: {}", response.body)
            throw KhubException(ApiErrorCode.INTEGRATION_INVALID_CREDENTIALS)
        }
    }

    private suspend fun fillAttachments(
        headers: HttpHeaders,
        settings: ConfluenceSettingsCommon,
        originalPage: PageResponse
    ): ConfluencePage {
        val attachmentsForPage = loadAttachmentsForPage(headers, settings.apiUrlPrefix, originalPage).flatMapConcat { attachments ->
            flow {
                attachments.forEach { attachment ->
                   attachment._links?.get("download")?.let { downloadLink ->
                       val attachmentDownloadUrlPrefix = if (settings is CloudConfluenceIntegrationSettings) {
                           attachment._links["self"]?.substringBefore("rest/api/") ?: settings.apiUrlPrefix
                       } else {
                           settings.apiUrlPrefix
                       }
                       emit(
                            AttachmentWithLink(
                                attachment = attachment,
                                downloadLink = DownloadReference(
                                    link = "${attachmentDownloadUrlPrefix}${downloadLink}",
                                    headers = headers
                                )
                            )
                        )
                    }
                }
            }
        }.toList()
        return ConfluencePage(originalPage, attachmentsForPage)
    }

    fun processBatch(
        existedFilesByExternalId: Map<String, ExternalFileShort>,
        batch: List<ConfluencePage>,
        integration: IntegrationEntity
    ): Int {
        var numChanges = 0
        val existedFilesWithParent = existedFilesByExternalId.values.filter { it.parentId != null }.groupBy { it.parentId }
        for (page in batch) {
            val existedFile = existedFilesByExternalId[page.content.id]
            val hasChanges = if (existedFile != null) {
                val childrenFiles = existedFilesWithParent[existedFile.id] ?: emptyList()
                externalUpdateApplier.updateConfluencePage(existedFile, childrenFiles, page, integration)
            } else {
                externalUpdateApplier.addConfluencePage(page, integration)
            }
            if (hasChanges) {
                numChanges += 1
            }
        }
        return numChanges
    }

    private fun loadAllPages(headers: HttpHeaders, apiUrlPrefix: String, spaceKey: String): Flow<List<PageResponse>> {
        return loadContent { start, limit ->
            confluenceRestTemplate.exchange(
                "${apiUrlPrefix}/rest/api/content/?spaceKey={spaceKey}&expand=body.export_view,version,ancestors&start={start}&limit={limit}",
                HttpMethod.GET,
                HttpEntity<Void>(headers),
                object : ParameterizedTypeReference<ListResponse<PageResponse>>() {},
                spaceKey,
                start,
                limit
            )
        }
    }

    private fun loadAttachmentsForPage(
        headers: HttpHeaders,
        apiUrlPrefix: String,
        page: ContentResponse
    ): Flow<List<AttachmentResponse>> {
        return loadContent { start, limit ->
            confluenceRestTemplate.exchange(
                "${apiUrlPrefix}/rest/api/content/{id}/child/attachment?expand=childTypes.attachment,version&start={start}&limit={limit}",
                HttpMethod.GET,
                HttpEntity<Void>(headers),
                object : ParameterizedTypeReference<ListResponse<AttachmentResponse>>() {},
                page.id,
                start,
                limit
            )
        }
    }

    private fun <T> loadContent(
        exchangeBlock: (Int, Int) -> ResponseEntity<ListResponse<T>>
    ): Flow<List<T>> {
        return flow {
            var start = 0
            val limit = 100
            var totalPages = 0
            while (true) {
                val response = exchangeBlock(start, limit)
                start += limit
                val batchSize = response.body!!.results.size
                if (totalPages + batchSize > integrationProperties.confluence.maxPages) {
                    emit(response.body!!.results.subList(0, integrationProperties.confluence.maxPages - totalPages))
                } else {
                    emit(response.body!!.results)
                }
                totalPages += response.body!!.results.size
                if (response.body!!.results.size < limit) {
                    return@flow
                }
                if (totalPages >= integrationProperties.confluence.maxPages) {
                    log.warn("Stop integration check: too many pages")
                    return@flow
                }
            }
        }
    }

    private fun getSpace(headers: HttpHeaders, settings: ConfluenceSettingsCommon): SpaceResponse {
        if (settings is CloudConfluenceIntegrationSettings)
            return getSpaceV2(headers, settings)

        var start = 0
        val limit = 50
        while (true) {
            val response = confluenceRestTemplate.exchange(
                "${settings.apiUrlPrefix}/rest/api/space?start=${start}&limit=${limit}",
                HttpMethod.GET,
                HttpEntity<Void>(headers),
                object : ParameterizedTypeReference<ListResponse<SpaceResponse>>() {},
                settings.space
            )
            val foundSpace = response.body?.results?.firstOrNull { it.key == settings.space || it.name == settings.space }
            if (foundSpace != null) {
                return foundSpace
            }
            if (response.body!!.results.size < limit) {
                break
            }
            start += limit
        }
        throw KhubException(ApiErrorCode.INTEGRATION_CONFLUENCE_SPACE_NOT_FOUND, mapOf("space" to settings.space))
    }

    private fun getSpaceV2(headers: HttpHeaders, settings: CloudConfluenceIntegrationSettings): SpaceResponse {
        settings.selectedCloudId ?: throw KhubException(ApiErrorCode.INTEGRATION_CONFLUENCE_SITE_NOT_FOUND)

        var cursor: String? = null

        val limit = 50
        while (true) {
            val response = confluenceRestTemplate.exchange(
                "${settings.apiUrlPrefix}/api/v2/spaces?cursor=$cursor&limit=$limit",
                HttpMethod.GET,
                HttpEntity<Void>(headers),
                object : ParameterizedTypeReference<ListResponseV2<SpaceResponse>>() {},
            )
            val foundSpace = response.body?.results?.firstOrNull { it.key == settings.space || it.name == settings.space }
            if (foundSpace != null) {
                return foundSpace
            }
            if (response.body!!.results.size < limit) {
                break
            }
            cursor = response.body?._links?.next ?: break
        }
        throw KhubException(ApiErrorCode.INTEGRATION_CONFLUENCE_SPACE_NOT_FOUND, mapOf("space" to settings.space))
    }

    val ConfluenceSettingsCommon.apiUrlPrefix: String
        get() = when (this) {
            is CloudConfluenceIntegrationSettings -> {
                selectedCloudId ?: throw KhubException(ApiErrorCode.INTEGRATION_CONFLUENCE_SITE_NOT_FOUND)
                "$baseUrl/ex/confluence/${selectedCloudId}/wiki"
            }
            is ConfluenceIntegrationSettings -> baseUrl
        }
}
