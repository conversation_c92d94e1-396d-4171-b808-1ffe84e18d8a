<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.justai</groupId>
        <artifactId>knowledge-hub-gateway</artifactId>
        <version>2025.2-rc-SNAPSHOT</version>
    </parent>

    <artifactId>knowledge-hub-gateway-client-generator</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <version>${openapi-generator-maven.version}</version>
                <executions>
                    <execution>
                        <id>generate-internal-client</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/../knowledge-hub-gateway-server/src/main/resources/specs/knowledge-hub-gateway.yml</inputSpec>
                            <generatorName>java</generatorName>
                            <skipValidateSpec>true</skipValidateSpec>
                            <generateModelTests>false</generateModelTests>
                            <generateApiTests>false</generateApiTests>
                            <groupId>${project.groupId}</groupId>
                            <artifactId>knowledge-hub-gateway-client</artifactId>
                            <artifactVersion>${project.version}</artifactVersion>
                            <output>${project.build.directory}/api-client</output>
                            <typeMappings>
                                <mapping>file=org.springframework.core.io.Resource</mapping>
                                <mapping>date-time=java.time.Instant</mapping>
                                <mapping>DateTime=java.time.Instant</mapping>
                            </typeMappings>
                            <schemaMappings>ObjectNode=com.fasterxml.jackson.databind.node.ObjectNode,JsonNode=com.fasterxml.jackson.databind.JsonNode</schemaMappings>
                            <configOptions>
                                <java8>true</java8>
                                <dateLibrary>java8</dateLibrary>
                                <fullJavaUtil>false</fullJavaUtil>
                                <hideGenerationTimestamp>true</hideGenerationTimestamp>
                                <useTags>true</useTags>
                                <useJakartaEe>true</useJakartaEe>
                                <library>resttemplate</library>
                            </configOptions>
                            <modelPackage>com.justai.khub.api.model</modelPackage>
                            <apiPackage>com.justai.khub.api</apiPackage>
                            <addCompileSourceRoot>false</addCompileSourceRoot>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>1.6.0</version>
                <executions>
                    <execution>
                        <id>install-api-client</id>
                        <phase>install</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <executable>mvn</executable>
                            <workingDirectory>${project.build.directory}/api-client</workingDirectory>
                            <arguments>
                                <argument>install</argument>
                                <argument>-DskipTests</argument>
                            </arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>deploy-client</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <executable>mvn</executable>
                            <workingDirectory>${project.build.directory}/api-client</workingDirectory>
                            <arguments>
                                <argument>deploy</argument>
                                <argument>-DskipTests</argument>
                                <argument>
                                    -DaltDeploymentRepository=nexus-snapshots::default::https://nexus.just-ai.com/repository/maven-snapshots/
                                </argument>
                            </arguments>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
