package com.justai.khub.util

import com.justai.khub.api.fe.model.RetrievedChunksDTO
import com.justai.khub.util.WebUtils.parseTo
import com.justai.security.session.auth.JustSessionAuthentication
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors
import org.springframework.stereotype.Component
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers

@Component
class RetrieveChunksHelper {

    @Autowired
    protected lateinit var mvc: MockMvc

    @Autowired
    protected lateinit var defaultAuth: JustSessionAuthentication

    fun retrieveChunks(
        projectId: Long,
        chatId: Long,
        recordId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): RetrievedChunksDTO {
        return mvc.perform(
            MockMvcRequestBuilders.get("/api/khub/projects/${projectId}/chat/${chatId}/records/${recordId}/chunks")
                .header("Authorization", "Bearer $apiKey")
                .contentType(MediaType.APPLICATION_JSON)
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn().parseTo<RetrievedChunksDTO>()
    }
}
