package com.justai.khub.test

import com.justai.khub.api.model.ProjectUpdateRequest
import com.justai.khub.base.BaseBBT
import com.justai.khub.util.TestDataGenerator
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.http.HttpStatus
import org.springframework.web.client.HttpClientErrorException

class ProjectBBT : BaseBBT() {

    @Test
    fun testProjectsCRUD() = withDefaultAuth {
        val createRequest = TestDataGenerator.testProject()
        val newProject = projectsApi.createProject(createRequest)
        assertEquals(createRequest.name, newProject.name)

        val foundById = projectsApi.getProject(newProject.id)
        assertEquals(newProject.id, foundById.id)

        val projectsPage = projectsApi.getProjects(10, 0)
        assertTrue(projectsPage.content.any { it.id == newProject.id })

        val projectUpdateRequest = ProjectUpdateRequest().name("${foundById.name}-updated")
        val updatedProject = projectsApi.updateProject(foundById.id, projectUpdateRequest)
        assertEquals(updatedProject.name, projectUpdateRequest.name)

        val projectUpdateException = assertThrows<HttpClientErrorException> { projectsApi.updateProject(updatedProject.id, ProjectUpdateRequest().name("")) }
        assertEquals(HttpStatus.BAD_REQUEST, projectUpdateException.statusCode)

        projectsApi.deleteProject(newProject.id)
        val ex = assertThrows<HttpClientErrorException> { projectsApi.getProject(newProject.id) }
        assertEquals(HttpStatus.NOT_FOUND, ex.statusCode)
    }
}
