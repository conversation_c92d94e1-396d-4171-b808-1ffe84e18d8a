package com.justai.khub.project.service

import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.common.util.WebUtils
import com.justai.khub.common.util.WebUtils.checkUrlValid
import com.justai.khub.common.util.WebUtils.saveAsTempFile
import com.justai.khub.project.dto.AddOrUpdateLinkRequest
import com.justai.khub.project.dto.AddOrUpdateTextRequest
import com.justai.khub.project.dto.ProjectFileMetadata
import com.justai.khub.project.entity.ProjectFileEntity
import com.justai.khub.project.entity.ProjectVersionEntity
import com.justai.khub.project.mapper.ProjectFileMapper.initEntity
import com.justai.khub.project.mapper.ProjectVersionMapper.Companion.DEFAULT_VERSION
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate
import org.springframework.web.multipart.MultipartFile
import java.nio.file.Files
import java.nio.file.Path
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Service
class ProjectFilePublicRequestHandler(
    private val projectFileService: ProjectFileService,
    private val unbufferedRestTemplate: RestTemplate,
    private val projectService: ProjectService,
    private val projectVersionService: ProjectVersionService
) {
    val MAX_FILE_SIZE = 100 * 1024 * 1024L
    val dateTimeFormatter = DateTimeFormatter.ofPattern("dd_MM_HH_mm_ss")

    fun addLink(
        request: AddOrUpdateLinkRequest,
        projectId: Long,
        update: Boolean,
        accountId: Long
    ): ProjectFileEntity {
        return addOrUpdateFile(projectId, update, accountId) { version ->
            val url = checkUrlValid(request.link)
            val content = Files.createTempFile("upload", ".tmp")
            WebUtils.downloadFileFromUrl(unbufferedRestTemplate, url, MAX_FILE_SIZE, content)

            val metadata = ProjectFileMetadata(segment = request.segment)
            val filePath = initFilePath(request)
            val entity = initEntity(version, metadata, filePath, Files.size(content).toInt(), true)

            entity to content
        }
    }

    fun addText(
        request: AddOrUpdateTextRequest,
        projectId: Long,
        update: Boolean,
        accountId: Long
    ): ProjectFileEntity {
        return addOrUpdateFile(projectId, update, accountId) { version ->
            val metadata = ProjectFileMetadata(segment = request.segment)
            val filePath = initFilePath(request.name, "txt")

            val content = request.text.saveAsTempFile(filePath)
            val entity = initEntity(version, metadata, filePath, Files.size(content).toInt(), true)

            entity to content
        }
    }

    fun addMultipartFile(
        file: MultipartFile,
        name: String?,
        segment: String?,
        projectId: Long,
        update: Boolean,
        accountId: Long
    ): ProjectFileEntity {
        return addOrUpdateFile(projectId, update, accountId) { version ->
            val metadata = ProjectFileMetadata(segment = segment)
            val fileName = name ?: file.originalFilename ?: file.name

            val content = file.saveAsTempFile()
            val entity = initEntity(version, metadata, fileName, Files.size(content).toInt(), true)

            entity to content
        }
    }

    fun addOrUpdateFile(
        projectId: Long,
        update: Boolean,
        accountId: Long,
        contentProvider: (ProjectVersionEntity) -> Pair<ProjectFileEntity, Path>
    ): ProjectFileEntity {
        val project = projectService.getProject(projectId, accountId)
        val version = projectVersionService.getOrCreateVersion(project, DEFAULT_VERSION)

        val (newEntity, content) = contentProvider(version)

        return try {
            if (update) {
                val existedEntity = projectFileService.getFilesByStoragePath(version, newEntity.pathInStorage) ?: throw KhubException(ApiErrorCode.PROJECT_FILE_NOT_FOUND)
                projectFileService.updateFileContent(existedEntity, content, true)
            } else {
                projectFileService.addFile(newEntity, content)
            }
        } finally {
            Files.deleteIfExists(content)
        }
    }

    private fun initFilePath(name: String?, customExtension: String = ""): String {
        return if (name.isNullOrBlank()) {
            "uploaded_${LocalDateTime.now().format(dateTimeFormatter)}.$customExtension"
        } else {
            name
        }
    }

    private fun initFilePath(request: AddOrUpdateLinkRequest): String {
        val fileNameFromLink = request.link.substringAfterLast("/", "")
        return if (!request.name.isNullOrBlank()) {
            request.name
        } else if (fileNameFromLink.contains(".")) {
            initFilePath(fileNameFromLink, "")
        } else {
            initFilePath(request.name, request.link.substringAfterLast(".", "unk"))
        }
    }
}
