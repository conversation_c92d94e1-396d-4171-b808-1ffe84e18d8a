package com.justai.khub.channel.mapper

import com.justai.khub.api.fe.model.*
import com.justai.khub.channel.dto.BotConfigDto
import com.justai.khub.channel.dto.BotProjectReadDto
import com.justai.khub.channel.dto.ChannelSettings
import com.justai.khub.channel.dto.JaicpProjectSettings
import com.justai.khub.channel.entity.ChannelEntity
import com.justai.khub.channel.enumeration.ChannelStatus
import com.justai.khub.channel.enumeration.JaicpChannelStatus
import com.justai.khub.common.configuration.dto.IntegrationProperties
import com.justai.khub.project.entity.ApiKeyEntity
import com.justai.khub.project.entity.ProjectVersionEntity
import org.springframework.data.domain.Page
import java.time.ZoneId

object ChannelMapper {

    fun BotProjectReadDto.toEntity(version: ProjectVersionEntity, apiKey: ApiKeyEntity): ChannelEntity {
        return ChannelEntity().also {
            it.name = this.name
            it.status = ChannelStatus.ACTIVE
            it.settings = JaicpProjectSettings(
                apiTokenId = apiKey.id,
                projectId = this.id,
                projectShortName = this.shortName ?: this.name,
                botProjectLink = "/${this.shortName ?: this.name}",
                channelsLink = "/${this.shortName ?: this.name}/channels"
            )
            it.version = version
        }
    }

    fun ChannelEntity.toChannelInfo(): ChannelInfoDTO {
        return ChannelInfoDTO(
            id = this.id,
            name = this.name,
            type = this.settings.toType(),
            externalLink = this.settings.toExternalLink(),
            settings = this.settings,
            status = this.status.toString(),
            audit = AuditDTO(
                this.createdAt.atZone(ZoneId.systemDefault()).toInstant(),
                this.createdBy.accountId,
                this.updatedAt.atZone(ZoneId.systemDefault()).toInstant(),
                this.updatedBy.accountId
            )
        )
    }

    fun Page<ChannelEntity>.toChannelsPage(): ChannelsPage {
        return ChannelsPage(
            content = this.content.map { it.toChannelInfo() },
            paging = PagingResponse(
                totalCount = this.totalElements,
                pageNum = this.number,
                pageSize = this.size
            )
        )
    }

    fun getJaicpChannelStatus(botConfig: BotConfigDto): String {
        return if (true == botConfig.blocked) {
            JaicpChannelStatus.BLOCKED.toString()
        } else if (true == botConfig.removed) {
            JaicpChannelStatus.REMOVED.toString()
        } else botConfig.lastDeployResult?.status ?: JaicpChannelStatus.CREATED.toString()
    }

    private fun ChannelSettings.toType(): ChannelType {
        when (this) {
            is JaicpProjectSettings -> return ChannelType.JAICP
        }
    }

    private fun ChannelSettings.toExternalLink(): String {
        when (this) {
            is JaicpProjectSettings -> return this.botProjectLink
        }
    }
}
