Changelog
--------
### KHUB-802, KHUB-292
Добавлена опциональная кастомная обработка для csv файлов

Добавлено использование новой фичи `knowledge_hub_advanced_settings`. Если у пользователя она есть, то он может управлять расширенными настройками

#### Изменения в конфигах
```yaml
# добавлено поле включающую кастомную обработку csv файлов при инференсе. True по-дефолту
# Значение пробрасывается при запросах в rag сервис
rag:
  csvProcessingEnabled: true

# добавлен массив поддерживаемых расширений. Используется на фронте для опеределения можно ли загрузить файл
ingest:
  supportedExtensions:
      - ...

#### Изменения в API
Появился новый ендпоинт `GET /api/khub/projects/{projectId}/chat/{chatId}/records/{recordId}/chunks`

Он нужен фронту для получения чанков, использованных при генерации ответа на запрос {recordId}
```


### KHUB-1124
Добавлена интеграция с Confluence Cloud API.
Новый статус интеграции, AWAITING_ACTION:
```yaml
    IntegrationStatus:
        description: |
            Integration status.
            - `ACTIVE`: the last data download from the integration was completed successfully, or it has never been performed.
            - `AWAITING_ACTION`: confirmation of the request to access Confluence Cloud is required.
            - `CHECKING`: data download in progress.
            - `FAILED`: the last data download failed.
            - `DISABLED`: reserved for future use.
        type: string
        enum:
            - ACTIVE
            - AWAITING_ACTION
            - CHECKING
            - FAILED
            - DISABLED
```
Добавлен новый тип настроек интеграции cloud_confluence:
```yaml
    IntegrationSettings:
        description: Integration settings.
        type: object
        discriminator:
            propertyName: type
            mapping:
                confluence: '#/components/schemas/ConfluenceSettings'
                cloud_confluence: '#/components/schemas/CloudConfluenceSettings'
    CloudConfluenceSettings:
      description: Settings for integration with Confluence Cloud.
      allOf:
        - $ref: '#/components/schemas/IntegrationSettings'
        - type: object
          required:
            - type
            - space
          properties:
            type:
              type: string
              enum:
              - cloud_confluence
              default: cloud_confluence
              description: Type of integration.
            space:
              type: string
              description: Name of the space.
            selectedCloudId:
              type: string
              description: Cloud ID. See the [Atlassian documentation](https://support.atlassian.com/jira/kb/retrieve-my-atlassian-sites-cloud-id/).
```

# 2025.2

## 2025.2-rc

### KHUB-1036
Добавлена поддержка обработки ссылок на картинки в конфлюенс-странице.
Доступные форматы атачментов были расширены картинками:
```yaml
integrations:
    confluence:
        maxPages: 1000
        checkIntervalMinutes: 1
        acceptedAttachmentTypes:
            - txt
            - docx
            - pdf
            - md
            - mdx
            - doc
            - json
            - yaml
            - yml
            - xml
            - html
            - png
            - jpg
            - jpeg
            - svg
            - gif
```

### KHUB-1041
Добавлена поддержка сохранения аттачментов (файлов) конфлюенс-страницы в датасет проекта.
Доступные расширения файлов соответствуют расширениям, загружаемым через веб-интерфейс в проект БЗ.
```yaml
integrations:
  confluence:
    acceptedAttachmentTypes:
      - txt
      - docx
      - pdf
      - md
      - mdx
      - doc
      - json
      - yaml
      - yml
      - xml
      - html
```

### KHUB-551
Поддержано добавление источников в проект БЗ. Новые методы в [knowledge-hub-public-api.yml](knowledge-hub-gateway-server%2Fsrc%2Fmain%2Fresources%2Fspecs%2Fknowledge-hub-public-api.yml):
```yaml
paths:
    /api/knowledge-hub/sources/links:
        post:
          tags:
            - SourcesPublic
          operationId: addLink
        put:
          tags:
            - SourcesPublic
          operationId: updateLink

    /api/knowledge-hub/sources/texts:
        post:
          tags:
            - SourcesPublic
          operationId: addText
        put:
          tags:
            - SourcesPublic
          operationId: updateText

    /api/knowledge-hub/sources/files:
        post:
          tags:
            - SourcesPublic
          operationId: addFile
        put:
          tags:
            - SourcesPublic
          operationId: updateFile

    /api/knowledge-hub/sources/{sourceId}:
        get:
          tags:
            - SourcesPublic
          operationId: getProjectSource
        delete:
          tags:
            - SourcesPublic
          operationId: deleteProjectSource

    /api/knowledge-hub/sources/{sourceId}/download:
        get:
          tags:
            - SourcesPublic
          operationId: downloadProjectSource

    /api/knowledge-hub/integrations:
        post:
          tags:
            - IntegrationsPublic
          operationId: createIntegration
        get:
          tags:
            - IntegrationsPublic
          operationId: getIntegrations

    /api/knowledge-hub/integrations/{integrationId}:
        get:
          tags:
            - IntegrationsPublic
          operationId: getIntegration
        delete:
          tags:
            - IntegrationsPublic
          operationId: deleteIntegration
```

# 2025.1 - LTS release

## 2025.1-rc

### KHUB-1044
Поддержаны eternalLink в релевантных источниках. Если источник confluence page = ссылка на перманентный адресс страницы в конфлюенсе.
Если источник документ = временная ссылка на скачивание этого документа, как атачмента.
```yaml
    BriefSourceInfo:
        type: object
        description: Краткая информация о документе-источнике.
        required:
            - id
            - path
        properties:
            id:
                $ref: '#/components/schemas/LongId'
            path:
                type: string
                description: Название источника в базе знаний.
            externalLink:
                description: |
                    Ссылка на источник:
                    - Если источник загружен из интеграции — ссылка на оригинал, например на страницу в Confluence.<br/>Ссылка недоступна, если интеграция ни разу не была синхронизирована после появления этого поля.
                    - Если источник загружен вручную в виде файла — временная ссылка для скачивания. Срок действия таких ссылок ограничен.<br/>В качестве альтернативы используйте для скачивания метод [GET /sources/\{sourceId\}/download](#operation/downloadProjectSource).
                type: string
```

### KHUB-1042
Поддержаны eternalLink в релевантных источниках, когда источник = страница из Confluence
```yaml
    BriefSourceInfo:
      type: object
      description: Краткая информация по источнику, который используется в проекте базы знаний.
      required:
        - id
        - path
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        path:
          description: Путь к источнику в базе знаний. Путь - имя источника в проекте.
          type: string
        externalLink:
          description: Ссылка на внешний ресурс для данного источника. Для Confluence - ссылка страницы в пользовательском confluence.
          type: string
```

--------
### KHUB-558
Добавлены ссылки на релевантные источники в ответе на генерацию и источники в ответе ретривинга чанков


