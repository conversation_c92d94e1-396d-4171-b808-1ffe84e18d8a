package com.justai.khub.qa.service

import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.project.entity.ProjectVersionEntity
import com.justai.khub.project.service.ProjectService
import com.justai.khub.qa.dto.TestSetRunFilter
import com.justai.khub.qa.entity.TestSetRunEntity
import com.justai.khub.qa.enumeration.TestSetRunStatus
import com.justai.khub.qa.repository.TestSetRunRepository
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.jpa.domain.Specification
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import kotlin.jvm.optionals.getOrNull

@Service
class TestSetRunService(
    private val projectService: ProjectService,
    private val testRunReportGenerator: TestRunReportGenerator,
    private val transactionTemplate: TransactionTemplate,
    private val testSetRunRepository: TestSetRunRepository,
) {

    @Transactional(readOnly = true)
    fun findReadyToRun(limit: Int): List<TestSetRunEntity> {
        val pagination = PageRequest.of(0, limit, Sort.by(Sort.Direction.ASC, "createdAt"))
        return testSetRunRepository.findAllByStatusIn(listOf(TestSetRunStatus.READY_TO_PROCESS), pagination)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun startTestSetRun(testSetRun: TestSetRunEntity): Boolean {
        return testSetRunRepository.updateStatus(
            testSetRun.id,
            listOf(TestSetRunStatus.READY_TO_PROCESS),
            TestSetRunStatus.PROCESSING,
            null
        ) > 0
    }

    @Transactional(rollbackFor = [Exception::class])
    fun cancelTestSetRun(testSetRun: TestSetRunEntity) {
        testSetRunRepository.updateStatus(
            testSetRun.id,
            listOf(TestSetRunStatus.READY_TO_PROCESS, TestSetRunStatus.PROCESSING),
            TestSetRunStatus.CANCELLED,
            null,
        )
    }

    @Transactional(rollbackFor = [Exception::class])
    fun finishTestSetRun(testSetRun: TestSetRunEntity, processingTimeMs: Long): Boolean {
        return testSetRunRepository.updateStatus(
            testSetRun.id,
            listOf(TestSetRunStatus.PROCESSING),
            TestSetRunStatus.FINISHED,
            null,
            processingTimeMs
        ) > 0
    }

    @Transactional(rollbackFor = [Exception::class])
    fun failTestSetRun(testSetRun: TestSetRunEntity, errorCode: String): Boolean {
        return testSetRunRepository.updateStatus(
            testSetRun.id,
            listOf(TestSetRunStatus.PROCESSING),
            TestSetRunStatus.FAILED,
            errorCode
        ) > 0
    }

    @Transactional(rollbackFor = [Exception::class])
    fun ensureSearchSettingsSaved(testSetRun: TestSetRunEntity, version: ProjectVersionEntity) {
        if (testSetRun.searchSettings == null) {
            testSetRun.searchSettings = version.searchSettings
            testSetRunRepository.saveAndFlush(testSetRun)
        }
    }

    @Transactional(readOnly = true)
    fun findTestSetRun(testSetRunId: Long, accountId: Long): TestSetRunEntity {
        val testSetRun = testSetRunRepository.findById(testSetRunId).getOrNull() ?: throw KhubException(ApiErrorCode.TESTSET_RUN_NOT_FOUND)
        if (testSetRun.testSet.createdBy.accountId != accountId) {
            throw KhubException(ApiErrorCode.ACCESS_DENIED)
        }
        return testSetRun
    }

    @Transactional(readOnly = true)
    fun addFinishedTestsAndUsage(
        testSetRun: TestSetRunEntity,
        testsIncrement: Int,
        promptTokensIncrement: Int,
        completionTokensIncrement: Int
    ) {
        testSetRunRepository.incrementFinishedTestsCountAndUsage(
            testSetRun.id,
            testsIncrement,
            promptTokensIncrement,
            completionTokensIncrement
        )
    }

    @Transactional(readOnly = true)
    fun isActive(testSetRunId: Long): Boolean {
        val testSetRun = testSetRunRepository.findById(testSetRunId).getOrNull() ?: return false
        return testSetRun.status == TestSetRunStatus.PROCESSING
    }

    @Transactional(rollbackFor = [Exception::class])
    fun updateMeanScore(testSetRun: TestSetRunEntity) {
        testSetRunRepository.updateMeanScore(testSetRun.id)
    }

    fun generateReport(testSetRunId: Long, currentAccountId: Long): Pair<String, ByteArray> {
        val testSetRun = transactionTemplate.execute { findTestSetRun(testSetRunId, currentAccountId) }!!
        if (testSetRun.status != TestSetRunStatus.FINISHED && testSetRun.status != TestSetRunStatus.FAILED && testSetRun.status != TestSetRunStatus.CANCELLED) {
            throw KhubException(ApiErrorCode.TESTSET_RUN_NOT_READY)
        }
        val reportContent = testRunReportGenerator.generateReport(testSetRun)
        return testSetRun.testSet.name to reportContent
    }

    @Transactional(readOnly = true)
    fun getTestSetRunsPage(
        projectId: Long,
        filter: TestSetRunFilter,
        pageable: PageRequest,
        accountId: Long
    ): Page<TestSetRunEntity> {
        val project = projectService.getProject(projectId, accountId) // to check access rights
        val specification = Specification
            .where(TestSetRunSpecifications.withProjectId(project.id))
            .and(TestSetRunSpecifications.withTestSetIds(filter.testSetIds))
            .and(TestSetRunSpecifications.withDateRange(filter.dateFrom, filter.dateTo))

        return testSetRunRepository.findAll(specification, pageable)
    }

    @Transactional(readOnly = true)
    fun findActiveTestSetRuns(testSetIds: List<Long>): List<TestSetRunEntity> {
        if (testSetIds.isEmpty()) {
            return listOf()
        }
        return testSetRunRepository.findLastActiveByTestSetIdsAndStatuses(testSetIds, listOf(TestSetRunStatus.READY_TO_PROCESS, TestSetRunStatus.PROCESSING))
    }

    @Transactional(readOnly = true)
    fun findActiveTestSetRun(testSetId: Long): TestSetRunEntity? {
        return findActiveTestSetRuns(listOf(testSetId)).firstOrNull()
    }

    @Transactional(readOnly = true)
    fun findLastFinishedTestSetRun(testSetId: Long): TestSetRunEntity? {
        val pagination = PageRequest.of(0, 1, Sort.by(Sort.Direction.DESC, "createdAt"))
        return testSetRunRepository.findAllByStatusIn(listOf(TestSetRunStatus.FINISHED), pagination).firstOrNull()
    }

    @Transactional(readOnly = true)
    fun findTestSetRunsCount(testSetIds: List<Long>): Map<Long, Long> {
        if (testSetIds.isEmpty()) {
            return mapOf()
        }
        return testSetRunRepository.countByTestSetIdIn(testSetIds).toMap()
    }

    @Transactional(readOnly = true)
    fun findTestSetRunsCount(testSetId: Long): Long {
        return findTestSetRunsCount(listOf(testSetId))[testSetId] ?: 0
    }
}
