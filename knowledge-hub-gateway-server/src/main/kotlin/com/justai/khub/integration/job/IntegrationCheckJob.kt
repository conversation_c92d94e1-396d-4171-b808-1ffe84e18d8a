package com.justai.khub.integration.job

import com.justai.khub.common.configuration.dto.IntegrationProperties
import com.justai.khub.common.configuration.dto.KHubUser
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.service.MetricsService
import com.justai.khub.common.util.CommonUtils.extractErrorCode
import com.justai.khub.common.util.LoggingUtils
import com.justai.khub.integration.dto.CloudConfluenceIntegrationSettings
import com.justai.khub.integration.dto.ConfluenceIntegrationSettings
import com.justai.khub.integration.dto.MinervaIntegrationSettings
import com.justai.khub.integration.entity.IntegrationEntity
import com.justai.khub.integration.service.ConfluenceIntegrationChecker
import com.justai.khub.integration.service.IntegrationService
import com.justai.khub.integration.service.MinervaIntegrationChecker
import com.justai.shutdown.CallbackAwaiter
import com.justai.shutdown.GracefulShutDownAware
import com.justai.shutdown.ShutDownLatch
import jakarta.annotation.PostConstruct
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Service
import java.util.*
import java.util.concurrent.*

@ConditionalOnProperty(value = ["scheduling.integration.enabled"], havingValue = "true", matchIfMissing = true)
@Service
class IntegrationCheckJob(
    private val integrationProperties: IntegrationProperties,
    private val integrationService: IntegrationService,
    private val integrationCheckThreadPool: ThreadPoolExecutor,
    private val integrationCheckExecutor: ExecutorService,
    private val metricsService: MetricsService,
    private val confluenceIntegrationChecker: ConfluenceIntegrationChecker,
    private val minervaIntegrationChecker: MinervaIntegrationChecker,
) : GracefulShutDownAware {
    private val log = LoggerFactory.getLogger(this::class.java)
    private val shutDownLatch = ShutDownLatch()

    private val activeJobs: ConcurrentHashMap<Long, Future<*>> = ConcurrentHashMap()

    private val authTimeoutJobs: ConcurrentHashMap<Long, ScheduledFuture<*>> = ConcurrentHashMap()
    private val authTimeoutExecutor: ScheduledExecutorService = Executors.newScheduledThreadPool(1)

    @PostConstruct
    fun init() {
        releaseActiveChecks()
    }

    fun releaseActiveChecks() {
        val releasedChecks = integrationService.releaseLockedActiveChecks()
        if (releasedChecks > 0) {
            log.warn("Released {} active integration checks", releasedChecks)
        }
    }

    @Scheduled(fixedDelay = 60_000)
    fun checkIntegrations() {
        shutDownLatch.run {
            val availableThreads =
                integrationProperties.checkIntegrationsJobPoolSize - integrationCheckThreadPool.activeCount
            if (availableThreads <= 0) {
                return@run
            }

            val jobsToProcess = integrationService
                .getReadyToCheck(availableThreads)
                .filterNot { activeJobs.containsKey(it.id) }
            jobsToProcess.forEach {
                val future = integrationCheckExecutor.submit { checkIntegration(it) }
                activeJobs[it.id] = future
            }
        }
    }

    fun scheduleManual(integration: IntegrationEntity): Boolean {
        return if (activeJobs.containsKey(integration.id)) {
            false
        } else {
            val future = integrationCheckExecutor.submit { checkIntegration(integration) }
            activeJobs[integration.id] = future
            true
        }
    }

    // WARN! Updates MDC and SecurityContext for background thread execution
    private fun checkIntegration(integration: IntegrationEntity) {
        try {
            SecurityContextHolder.getContext().authentication = KHubUser(accountId = integration.createdBy.accountId)
            LoggingUtils.addToMDC(accountId = integration.createdBy.accountId, requestId = UUID.randomUUID().toString())
            val updated = integrationService.startChecking(integration)
            if (!updated) {
                log.info("Skip checking integration: {}: check already started", integration.id)
                return
            }
            log.info("Started checking integration: {}", integration.id)
            val startedAt = System.currentTimeMillis()
            metricsService.integrationCheckTimer().record(Runnable {
                try {
                    runBlocking {
                        when (val settings = integration.settings) {
                            is ConfluenceIntegrationSettings -> confluenceIntegrationChecker.checkIntegration(integration, settings)
                            is CloudConfluenceIntegrationSettings -> confluenceIntegrationChecker.checkIntegration(integration, settings)
                            is MinervaIntegrationSettings -> minervaIntegrationChecker.checkIntegration(integration, settings)
                        }
                    }
                    integrationService.finishChecking(integration)
                } catch (ex: Exception) {
                    handleIntegrationCheckError(integration, ex)
                }
            })
            log.info("Finished checking integration: {}. processing time = {}ms", integration.id, System.currentTimeMillis() - startedAt)
        } finally {
            activeJobs.remove(integration.id)
            SecurityContextHolder.clearContext()
            LoggingUtils.clearAll()
        }
    }

    private fun handleIntegrationCheckError(integration: IntegrationEntity, ex: Exception) {
        log.error("Error checking integration {}", integration.id, ex)
        val errorCode = ex.extractErrorCode(ApiErrorCode.INTEGRATION_CHECK_ERROR.code)
        metricsService.incrementIntegrationCheckErrorsCounter()
        integrationService.failChecking(integration, errorCode)
    }

    /**
     * Schedules a job to fail an integration after the configured timeout period
     * if OAuth authentication is not completed.
     */
    fun scheduleAuthTimeout(integration: IntegrationEntity) {
        val authTimeoutSeconds = integrationProperties.atlassian.authTimeoutSeconds
        cancelAuthTimeout(integration.id)

        val future = authTimeoutExecutor.schedule({
            try {
                SecurityContextHolder.getContext().authentication = KHubUser(accountId = integration.createdBy.accountId)
                LoggingUtils.addToMDC(accountId = integration.createdBy.accountId, requestId = UUID.randomUUID().toString())

                log.info("Integration {} auth timed out after {} seconds", integration.id, authTimeoutSeconds)
                integrationService.oauthTimeout(integration, ApiErrorCode.INTEGRATION_OAUTH_TIMEOUT.code)
            } catch (ex: Exception) {
                log.error("Error processing auth timeout for integration {}", integration.id, ex)
            } finally {
                authTimeoutJobs.remove(integration.id)
                SecurityContextHolder.clearContext()
                LoggingUtils.clearAll()
            }
        }, authTimeoutSeconds.toLong(), TimeUnit.SECONDS)

        authTimeoutJobs[integration.id] = future
    }

    /**
     * Cancels a scheduled auth timeout job for an integration
     */
    fun cancelAuthTimeout(integrationId: Long) {
        authTimeoutJobs.remove(integrationId)?.let { future ->
            log.info("Cancelling auth timeout job for integration {}", integrationId)
            future.cancel(false)
        }
    }

    override fun shutDownGracefully(runnable: Runnable) {
        CallbackAwaiter.withCallback(runnable).awaitAsync {
            log.info("Stop IntegrationCheckJob...")
            integrationCheckExecutor.shutdownNow()
            authTimeoutExecutor.shutdownNow()
            shutDownLatch.awaitShutdown()
            if (!integrationCheckExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                log.warn("release active checks")
                releaseActiveChecks()
            }
            authTimeoutExecutor.awaitTermination(10, TimeUnit.SECONDS)
            log.info("Stop IntegrationCheckJob... DONE")
        }
    }
}
