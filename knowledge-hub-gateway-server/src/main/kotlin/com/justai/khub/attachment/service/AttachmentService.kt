package com.justai.khub.attachment.service

import com.justai.khub.attachment.mapper.AttachmentMapper
import com.justai.khub.attachment.mapper.AttachmentMapper.toAttachmentEntity
import com.justai.khub.attachment.properties.AttachmentsProperties
import com.justai.khub.attachment.repository.AttachmentLinkRepository
import com.justai.khub.attachment.repository.AttachmentRepository
import com.justai.khub.common.connector.LakefsConnector
import com.justai.khub.project.entity.ProjectFileEntity
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpHeaders
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import java.net.URI
import java.time.LocalDateTime

@Service
class AttachmentService(
    private val attachmentsProperties: AttachmentsProperties,
    private val attachmentRepository: AttachmentRepository,
    private val transactionTemplate: TransactionTemplate,
    private val attachmentLinkRepository: AttachmentLinkRepository,
    private val lakefsConnector: LakefsConnector,
    @Value("\${server.hostname}")
    private val hostname: String
) {
    companion object {
        const val ATTACHMENT_URL_PREFIX = "/api/khub/public/attachments"
        const val FILE_LINK_ATTACHMENT_PREFIX = "file-"
        val EXTENSION_REGEXP = "\\.([0-9a-z]+)(?=[?#])|(\\.)(?:[\\w]+)\$".toRegex()
    }

    val ATTACHMENT_LINK_REGEXP = "\\(?!?\\[[^\\]]+\\]\\(${attachmentsProperties.baseUrl}/(attach_[a-z0-9\\-]+)(\\.[a-zA-Z0-9]+)?\\)\\)?".toRegex()

    // TODO use batch
    fun adjustAttachmentLinks(source: String): String {
        return ATTACHMENT_LINK_REGEXP.replace(source) { buildMarkdownImageLink(it.groupValues[1], it.groupValues[2]) }
    }


    fun buildMarkdownImageLink(attachmentId: String, attachmentExtension: String): String {
        if (!attachmentsProperties.enabled) return ""
        return transactionTemplate.execute {
            val attachmentEntity = attachmentRepository.findByExternalId(attachmentId) ?: return@execute ""
            val newLink = AttachmentMapper.toAttachmentLinkEntity(attachmentEntity, attachmentExtension, attachmentsProperties.publicLinkDuration)
            val savedLink = attachmentLinkRepository.saveAndFlush(newLink)
            "![${savedLink.publicId}](https://${hostname}$ATTACHMENT_URL_PREFIX/${attachmentEntity.externalId}/${savedLink.publicId})"
        }!!
    }

    @Transactional(rollbackFor = [Exception::class])
    // expect 'attachmentExternalIds' to be attachment filenames in lakefs
    fun replaceAttachments(file: ProjectFileEntity, attachmentExternalIds: List<String>) {
        attachmentRepository.deleteByFileId(file.id)
        if (attachmentExternalIds.isEmpty()) return
        attachmentRepository.saveAllAndFlush(attachmentExternalIds.map { toAttachmentEntity(file, it) })
    }

    fun createAttachmentLinksForFiles(files: Iterable<ProjectFileEntity>): Map<Long, String> {
        if (!attachmentsProperties.enabled) return emptyMap()
        return transactionTemplate.execute {
            val filesById = files.associateBy { "${FILE_LINK_ATTACHMENT_PREFIX}${it.id}" }
            val existedAttachments = attachmentRepository.findAllByExternalIds(filesById.keys).associateBy { it.externalId }
            val filesWithoutAttachment = filesById.filterKeys { existedAttachments[it] == null }
            val addedAttachments = filesWithoutAttachment.takeIf { it.isNotEmpty() }?.let { files ->
                attachmentRepository.saveAllAndFlush(files.map {
                    toAttachmentEntity(
                        file = it.value,
                        externalAttachmentId = it.key
                    )
                }).associateBy { it.externalId }
            } ?: emptyMap()
            val attachments = existedAttachments + addedAttachments
            val fileLinks = filesById.mapNotNull {
                val fileAttachment = attachments[it.key] ?: return@mapNotNull null
                val extension = EXTENSION_REGEXP.find(it.value.relativePath)?.value ?: ""
                AttachmentMapper.toAttachmentLinkEntity(
                    attachment = fileAttachment,
                    attachmentExtension = extension,
                    availabilityDuration = attachmentsProperties.publicLinkDuration
                )
            }
            val savedLinks = attachmentLinkRepository.saveAllAndFlush(fileLinks)

            savedLinks.associateBy { it.attachment.file.id }.mapValues {
                "https://${hostname}$ATTACHMENT_URL_PREFIX/${it.value.attachment.externalId}/${it.value.publicId}"
            }
        } ?: emptyMap()
    }

    @Transactional(readOnly = true)
    fun findAttachmentByPublicLink(attachmentId: String, publicLinkId: String, currentAccountId: Long?): ByteArray? {
        val publicLink = attachmentLinkRepository.findByPublicId(publicLinkId)
        if (publicLink == null && currentAccountId == null) return null
        if (publicLink?.isExpired() == true && currentAccountId == null) return null
        val attachment = attachmentRepository.findByExternalId(attachmentId) ?: return null
        if (publicLink != null && publicLink.attachment.id != attachment.id) return null
        val sourceFile = attachment.file
        if (publicLink == null && sourceFile.createdBy.accountId != currentAccountId) return null

        val repositoryId = sourceFile.version.project.repositoryId ?: return null
        return if (attachmentId.startsWith(FILE_LINK_ATTACHMENT_PREFIX)) {
            lakefsConnector.getFileContent(repositoryId, sourceFile.version.name, sourceFile.pathInStorage)
        } else {
            lakefsConnector.getAttachmentContent(
                repositoryId,
                sourceFile.version.name,
                sourceFile.pathInStorage,
                attachment.externalId
            )
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    fun deleteExpiredLinks(): Int {
        return attachmentLinkRepository.deleteExpired(LocalDateTime.now())
    }
}
