package com.justai.khub.channel.endpoint

import com.justai.khub.api.fe.ChannelsApiService
import com.justai.khub.api.fe.model.*
import com.justai.khub.channel.mapper.ChannelMapper.toChannelInfo
import com.justai.khub.channel.mapper.ChannelMapper.toChannelsPage
import com.justai.khub.channel.service.ChannelService
import com.justai.khub.common.util.WebUtils.getCurrentUser
import com.justai.khub.project.mapper.ProjectVersionMapper
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service

@Service
class ChannelEndpoint(
    private val channelService: ChannelService
) : ChannelsApiService {

    override fun createChannel(projectId: Long, channelDTO: ChannelDTO, version: String?): ChannelInfoDTO {
        val channel = channelService.createChannel(
            channelDTO,
            projectId,
            version ?: ProjectVersionMapper.DEFAULT_VERSION,
            getCurrentUser()

        )
        return channel.toChannelInfo()
    }

    override fun getChannels(projectId: Long, pageSize: Int, version: String?, pageNum: Int): ChannelsPage {
        val pageable = PageRequest.of(pageNum, pageSize, Sort.by(Sort.Direction.ASC, "name"))
        val channels = channelService.getChannels(
            projectId,
            version ?: ProjectVersionMapper.DEFAULT_VERSION,
            getCurrentUser(),
            pageable
        )
        return channels.toChannelsPage()
    }

    override fun blockJaicpChannel(projectId: Long, channelId: Long, jaicpChannelId: Long, version: String?) {
        channelService.blockJaicpChannel(
            projectId,
            version ?: ProjectVersionMapper.DEFAULT_VERSION,
            channelId,
            jaicpChannelId,
            getCurrentUser()
        )
    }

    override fun unblockJaicpChannel(projectId: Long, channelId: Long, jaicpChannelId: Long, version: String?) {
        channelService.unblockJaicpChannel(
            projectId,
            version ?: ProjectVersionMapper.DEFAULT_VERSION,
            channelId,
            jaicpChannelId,
            getCurrentUser()
        )
    }
}
