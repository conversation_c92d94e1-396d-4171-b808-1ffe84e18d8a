package com.justai.khub.project.dto

import com.justai.khub.api.public.model.AddTextRequest
import com.justai.khub.api.public.model.UpdateTextRequest

data class AddOrUpdateTextRequest(
    val text: String,
    val name: String?,
    val segment: String?
) {
    constructor(addRequest: AddTextRequest) : this(
        text = addRequest.text,
        name = addRequest.name,
        segment = addRequest.segment
    )

    constructor(updateRequest: UpdateTextRequest) : this(
        text = updateRequest.text,
        name = updateRequest.name,
        segment = updateRequest.segment
    )
}
