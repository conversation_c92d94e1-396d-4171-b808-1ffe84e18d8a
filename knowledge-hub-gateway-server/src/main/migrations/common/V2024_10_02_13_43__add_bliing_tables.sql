CREATE TABLE public.account_balance
(
    id                                                   BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    account_id                                           BIGINT    NOT NULL UNIQUE,
    available_requests_by_tariff                         BIGINT    NOT NULL,
    available_requests_by_packages                       BIGINT    NOT NULL,
    tariff_version                                       INT       NOT NULL,
    operations_suspended                                 BOOLEAN   NOT NULL,

    used_requests_by_tariff                              BIGINT    NOT NULL,
    used_requests_by_packages                            BIGINT    NOT NULL,
    used_requests_by_packages_when_tariff_period_started BIGINT    NOT NULL,

    created_at                                           TIMESTAMP NOT NULL,
    updated_at                                           TIMESTAMP NOT NULL,

    CONSTRAINT positive_available_requests_by_tariff CHECK (available_requests_by_tariff >= 0),
    CONSTRAINT positive_available_requests_by_packages CHECK (available_requests_by_packages >= 0),
    CONSTRAINT positive_used_requests_by_tariff CHECK (used_requests_by_tariff >= 0),
    CONSTRAINT positive_used_requests_by_packages CHECK (used_requests_by_packages >= 0)
);

CREATE TABLE billable_operation
(
    id                        BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    account_id                BIGINT      NOT NULL,

    used_requests_by_tariff   BIGINT      NOT NULL,
    used_requests_by_packages BIGINT      NOT NULL,

    status                    VARCHAR(64) NOT NULL,
    comment                   VARCHAR(1024),
    metadata                  JSONB,

    created_at                TIMESTAMP   NOT NULL,
    updated_at                TIMESTAMP   NOT NULL,

    CONSTRAINT positive_used_requests_by_tariff CHECK (used_requests_by_tariff >= 0),
    CONSTRAINT positive_used_requests_by_packages CHECK (used_requests_by_packages >= 0)
);
CREATE INDEX idx_billable_operation_started_status ON billable_operation(status) WHERE status = 'STARTED';
