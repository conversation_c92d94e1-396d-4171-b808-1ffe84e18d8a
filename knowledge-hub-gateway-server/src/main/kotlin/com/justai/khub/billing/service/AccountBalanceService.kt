package com.justai.khub.billing.service

import com.justai.khub.billing.dto.TariffUpdateDto
import com.justai.khub.billing.entity.AccountBalanceEntity
import com.justai.khub.billing.mapper.BillingMapper.toAccountBalanceEntity
import com.justai.khub.billing.properties.BillingProperties
import com.justai.khub.billing.repository.AccountBalanceRepository
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import kotlin.math.max
import kotlin.math.min

@Service
class AccountBalanceService(
    private val billingProperties: BillingProperties,
    private val accountBalanceRepository: AccountBalanceRepository,
    private val transactionTemplate: TransactionTemplate,
) {
    private val log = LoggerFactory.getLogger(this::class.java)

    fun updateAccountBalances(update: List<TariffUpdateDto>): List<AccountBalanceEntity> {
        if (!billingProperties.enabled) {
            log.warn("Skipping balance update for accounts ${update.joinToString { "${it.accountId}" }}: billing disabled")
            return listOf()
        }
        return update.map { updateAccountBalance(it) }
    }

    fun updateAccountBalance(update: TariffUpdateDto): AccountBalanceEntity {
        return transactionTemplate.execute {
            val existedBalance = accountBalanceRepository.findForUpdateByAccountId(update.accountId)
            val updatedBalance = existedBalance?.applyUpdate(update) ?: update.toAccountBalanceEntity()
            accountBalanceRepository.saveAndFlush(updatedBalance)
        }!!
    }

    fun AccountBalanceEntity.applyUpdate(update: TariffUpdateDto): AccountBalanceEntity {
        return this.also {
            availableRequestsByPackages = update.totalRequestsByPackages
            operationsSuspended = update.operationsSuspended
            if (update.tariffVersion > tariffVersion) { // new tariff period started
                tariffVersion = update.tariffVersion
                availableRequestsByTariff = update.currentRequestsByTariff
                usedRequestsByTariff = 0
                // TODO should we update this property after processing overdraft?
                usedRequestsByPackagesWhenTariffPeriodStarted = usedRequestsByPackages
                processOverdraft()
            }
        }
    }
    fun AccountBalanceEntity.processOverdraft(){
        val overdraft = max(0, usedRequestsByPackages - availableRequestsByPackages)
        val overdraftUpdate = min(overdraft, availableRequestsByTariff)
        usedRequestsByTariff += overdraftUpdate
        usedRequestsByPackages -= overdraftUpdate
    }


    @Transactional(readOnly = true)
    fun getBalanceByAccountIds(accountIds: Collection<Long>): List<AccountBalanceEntity> {
        return accountBalanceRepository.findByAccountIds(accountIds)
    }
}
