package com.justai.khub.common.configuration.dto

import com.justai.khub.common.enumeration.AccountFeatures
import com.justai.security.session.data.JustSessionUserData
import org.springframework.security.core.Authentication
import org.springframework.security.core.GrantedAuthority
import org.springframework.security.core.authority.SimpleGrantedAuthority

data class KHubUser(
    val accountId: Long,
    val userId: Long? = null, // null for auth via api key
    val userData: JustSessionUserData? = null,
    val projectId: Long? = null,
    val sessionId: String? = null,
    val xsrfToken: String? = null
) : Authentication {
    private var authenticated = true
    private val authorities = userData?.permissions?.map { SimpleGrantedAuthority(it) } ?: listOf()

    override fun getName(): String {
        return userData?.login ?: "unknown"
    }

    override fun getAuthorities(): List<GrantedAuthority> {
        return authorities
    }

    override fun getCredentials(): Any? {
        return null
    }

    override fun getDetails(): Any? {
        return null
    }

    override fun getPrincipal(): Any? {
        return userData
    }

    override fun isAuthenticated(): Boolean {
        return authenticated
    }

    override fun setAuthenticated(isAuthenticated: Boolean) {
        authenticated = isAuthenticated
    }

    fun hasFeature(feature: AccountFeatures): Boolean {
        return userData?.features?.any { it == feature.rawValue } ?: false
    }
}
