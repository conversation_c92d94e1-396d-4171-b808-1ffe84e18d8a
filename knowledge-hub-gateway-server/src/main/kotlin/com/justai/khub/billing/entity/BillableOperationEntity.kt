package com.justai.khub.billing.entity

import com.fasterxml.jackson.databind.node.ObjectNode
import com.justai.khub.billing.enumeration.BillableOperationStatus
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EntityListeners
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import org.hibernate.annotations.Type
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

@Entity
@Table(name = "billable_operation")
@EntityListeners(AuditingEntityListener::class)
class BillableOperationEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = -1

    @Column
    var accountId: Long = -1

    @Column
    var usedRequestsByPackages: Long = 0

    @Column
    var usedRequestsByTariff: Long = 0

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    lateinit var status: BillableOperationStatus

    @Column
    var comment: String? = null

    @Type(JsonType::class)
    @Column(columnDefinition = "jsonb")
    var metadata: ObjectNode? = null

    @Column
    @CreatedDate
    lateinit var createdAt: LocalDateTime

    @Column
    @LastModifiedDate
    lateinit var updatedAt: LocalDateTime
}
