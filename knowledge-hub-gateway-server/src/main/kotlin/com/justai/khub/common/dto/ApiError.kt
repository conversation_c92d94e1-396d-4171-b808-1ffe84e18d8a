package com.justai.khub.common.dto

import com.justai.khub.common.enumeration.ApiErrorCode

class ApiError(
    val error: ApiErrorCode,
    val args: Map<String, Any?> = emptyMap()
) {
    constructor(error: ApiErrorCode, ex: Throwable) : this(error, ex.message.let { if (it.isNullOrBlank()) mapOf() else mapOf("message" to (it)) })

    constructor(error: ApiErrorCode, vararg pairs: Pair<String, Any?>) :
        this(error, pairs.toMap())
}
