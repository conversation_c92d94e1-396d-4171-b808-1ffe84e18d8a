package com.justai.khub.common.configuration.filter

import com.justai.khub.common.configuration.dto.KHubUser
import com.justai.khub.common.util.Base62Encoder
import com.justai.khub.project.service.ApiKeyService
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.slf4j.LoggerFactory
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Component
import org.springframework.web.filter.OncePerRequestFilter

@Component
class TokenAuthenticationFilter(
    private val apiKeyService: ApiKeyService
) : OncePerRequestFilter() {
    private val log = LoggerFactory.getLogger(this::class.java)

    override fun doFilterInternal(request: HttpServletRequest, response: HttpServletResponse, filterChain: Filter<PERSON>hain) {
        val securityContext = checkNotNull(SecurityContextHolder.getContext())
        val currentAuth = securityContext.authentication as? KHubUser
        if (currentAuth != null) {
            filterChain.doFilter(request, response)
            return
        }
        val authHeader = request.getHeader("Authorization")?.removePrefix("Bearer")?.trim()
        if (authHeader.isNullOrEmpty() || authHeader.startsWith("Basic")) {
            filterChain.doFilter(request, response)
            return
        }
        val keyWithId = authHeader.split(".")
        if (keyWithId.size != 2) {
            log.warn("Ignoring api key with invalid format")
            filterChain.doFilter(request, response)
            return
        }
        val apiKeyEntity = apiKeyService.touchAndValidateApiKey(Base62Encoder.decode(keyWithId[0]), keyWithId[1])
        if (apiKeyEntity != null) {
            securityContext.authentication = KHubUser(
                accountId = apiKeyEntity.createdBy.accountId,
                userId = null,
                userData = null,
                projectId = if (apiKeyEntity.project?.deletedAt == null) apiKeyEntity.project?.id else null
            )
        } else {
            log.warn("Ignoring invalid or inactive api key")
        }
        filterChain.doFilter(request, response)
    }
}
