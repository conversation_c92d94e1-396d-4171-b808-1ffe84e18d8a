package com.justai.khub.common.configuration

import org.springframework.context.annotation.Configuration
import org.springframework.web.servlet.config.annotation.CorsRegistry
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer

@Configuration
class WebMvcConfiguration : WebMvcConfigurer {
    override fun addResourceHandlers(registry: ResourceHandlerRegistry) {
        registry.addResourceHandler("/static/openapi/**")
            .addResourceLocations("classpath:/specs/")
        registry.addResourceHandler("/static/templates/**")
            .addResourceLocations("classpath:/templates/")
        registry.addResourceHandler("/static/images/**")
            .addResourceLocations("classpath:/images/")
        registry.addResourceHandler("/static/pages/**")
            .addResourceLocations("classpath:/pages/")

    }
}
