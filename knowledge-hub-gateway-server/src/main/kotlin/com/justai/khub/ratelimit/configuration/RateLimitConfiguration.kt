package com.justai.khub.ratelimit.configuration

import com.justai.khub.ratelimit.dto.RateLimitProperties
import com.justai.khub.ratelimit.dto.UsageLimit.Companion.asBandwidth
import com.justai.khub.ratelimit.service.LicensingService
import io.github.bucket4j.Bucket
import io.github.bucket4j.BucketConfiguration
import io.github.bucket4j.distributed.ExpirationAfterWriteStrategy.basedOnTimeForRefillingBucketUpToMax
import io.github.bucket4j.distributed.jdbc.PrimaryKeyMapper
import io.github.bucket4j.distributed.proxy.ProxyManager
import io.github.bucket4j.distributed.proxy.optimization.Optimizations
import io.github.bucket4j.postgresql.Bucket4jPostgreSQL
import io.github.bucket4j.postgresql.PostgreSQLSelectForUpdateBasedProxyManager
import org.springframework.boot.sql.init.dependency.DependsOnDatabaseInitialization
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import javax.sql.DataSource

@Configuration
class RateLimitConfiguration {
    @Bean
    @DependsOnDatabaseInitialization
    fun proxyManager(dataSource: DataSource, rateLimitProperties: RateLimitProperties): PostgreSQLSelectForUpdateBasedProxyManager<String> {
        return Bucket4jPostgreSQL.PostgreSQLSelectForUpdateBasedProxyManagerBuilder(dataSource, PrimaryKeyMapper.STRING)
            .expirationAfterWrite(basedOnTimeForRefillingBucketUpToMax(rateLimitProperties.bucketExpiredAfter))
            .build()
    }

    @Bean
    fun globalRateLimitingBucket(proxyManager: ProxyManager<String>, licensingService: LicensingService): Bucket {
        val bucketConfiguration = with(BucketConfiguration.builder()) {
            licensingService.getLimits().forEach { addLimit(it.asBandwidth()) }
            build()
        }

        return proxyManager.builder()
            .withOptimization(Optimizations.batching())
            .build("global-${licensingService.keyId}") { bucketConfiguration }
    }
}
