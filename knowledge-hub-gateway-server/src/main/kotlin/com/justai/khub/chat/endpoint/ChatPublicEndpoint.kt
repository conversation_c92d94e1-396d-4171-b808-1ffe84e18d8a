package com.justai.khub.chat.endpoint

import com.justai.khub.api.public.ChatsPublicApiService
import com.justai.khub.api.public.model.*
import com.justai.khub.chat.entity.ChatHistoryRecordEntity
import com.justai.khub.chat.mapper.ChatMapper
import com.justai.khub.chat.mapper.ChatPublicMapper.toChatQuery
import com.justai.khub.chat.service.ChatHistoryService
import com.justai.khub.chat.service.ChatService
import com.justai.khub.common.mapper.CommonMapper.toRetrievedChunks
import com.justai.khub.common.mapper.SearchConfigMapper
import com.justai.khub.common.util.WebUtils.withUserAndProject
import com.justai.khub.project.mapper.ProjectVersionMapper.Companion.DEFAULT_VERSION
import com.justai.khub.project.service.ProjectVersionService
import com.justai.khub.project.service.RetrieveChunksService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.Instant
import kotlin.time.Duration
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds

@Service
class ChatsPublicEndpoint(
    private val chatService: ChatService,
    private val chatHistoryService: ChatHistoryService,
    private val projectVersionService: ProjectVersionService,
    private val retrieveChunksService: RetrieveChunksService,
    private val searchConfigMapper: SearchConfigMapper,
    private val chatMapper: ChatMapper,
) : ChatsPublicApiService {
    private val log = LoggerFactory.getLogger(this::class.java)

    override fun createChat(createChatRequest: CreateChatRequest): Chat = withUserAndProject { currentUser ->
        val chat = if (createChatRequest.settings != null) {
            val defaultSearchSettings = projectVersionService.getDefaultVersionSearchSettings(currentUser.projectId!!, currentUser.accountId)
            val searchSettings = searchConfigMapper.run {
                createChatRequest.settings.toSearchSettings(defaultSearchSettings, true)
            }
            chatService.createChatWithSearchSettings(
                projectId = currentUser.projectId,
                accountId = currentUser.accountId,
                versionName = DEFAULT_VERSION,
                settings = searchSettings,
                chatName = createChatRequest.name
            )
        } else {
            chatService.createChat(
                projectId = currentUser.projectId!!,
                versionName = DEFAULT_VERSION,
                chatName = createChatRequest.name,
                accountId = currentUser.accountId
            )
        }
        chatMapper.run {
            chat.toPublicDto()
        }
    }

    override fun getChat(chatId: Long): Chat = withUserAndProject { currentUser ->
        val chat = chatService.getChat(
            projectId = currentUser.projectId!!,
            accountId = currentUser.accountId,
            chatId = chatId
        )
        chatMapper.run {
            chat.toPublicDto()
        }
    }

    override fun processChatQuery(chatId: Long, chatRequest: ChatRequest): ChatQueryProcessingResult = withUserAndProject { currentUser ->
        projectVersionService.getDefaultVersionSearchSettings(currentUser.projectId!!, currentUser.accountId)
        val chatSettings = chatService.getChat(
            projectId = currentUser.projectId,
            accountId = currentUser.accountId,
            chatId = chatId
        ).searchSettings
        val searchSettings = searchConfigMapper.run {
            chatRequest.settings?.toSearchSettings(chatSettings, true)
        }
        val record = chatHistoryService.createRecord(
            currentUser.projectId,
            chatId,
            chatRequest.query,
            chatRequest.settings?.search?.segment,
            currentUser.accountId,
            searchSettings
        )
        val result = runBlocking { waitChatQueryFinish(record) }
        result.toChatQuery()
    }

    override fun processChatQueryAsync(chatId: Long, chatRequest: ChatRequest): ChatQueryProcessingResult = withUserAndProject { currentUser ->
        projectVersionService.getDefaultVersionSearchSettings(currentUser.projectId!!, currentUser.accountId)
        val chatSettings = chatService.getChat(
            projectId = currentUser.projectId,
            accountId = currentUser.accountId,
            chatId = chatId
        ).searchSettings
        val searchSettings = searchConfigMapper.run {
            chatRequest.settings?.toSearchSettings(chatSettings, true)
        }
        val record = chatHistoryService.createRecord(
            currentUser.projectId,
            chatId,
            chatRequest.query,
            chatRequest.settings?.search?.segment,
            currentUser.accountId,
            searchSettings
        )
        record.toChatQuery()
    }

    override fun getChatQueryAnswer(chatId: Long, queryId: Long, waitTimeSeconds: Int): ChatQueryProcessingResult = withUserAndProject { currentUser ->
        val chatRecords = chatHistoryService.getChatRecord(currentUser.projectId!!, chatId, queryId, currentUser.accountId)
        val result = runBlocking { waitChatQueryFinish(chatRecords, timeout = waitTimeSeconds.seconds) }
        result.toChatQuery()
    }

    override fun cancelRecordProcessing(chatId: Long, queryId: Long): ChatQueryProcessingResult = withUserAndProject { currentUser ->
        val result = chatHistoryService.cancelRecordProcessing(currentUser.projectId!!, chatId, queryId, currentUser.accountId)
        result.toChatQuery()
    }

    override fun retrieveChunksFromChat(chatId: Long, retrieveChunksFromChatRequest: RetrieveChunksFromChatRequest): RetrievedChunks = withUserAndProject { currentUser ->
        val version = projectVersionService.getVersion(currentUser.projectId!!, DEFAULT_VERSION, currentUser.accountId)
        val chatSettings = chatService.getChat(
            projectId = currentUser.projectId,
            accountId = currentUser.accountId,
            chatId = chatId
        ).searchSettings
        val searchSettings = searchConfigMapper.run {
            retrieveChunksFromChatRequest.settings?.toSearchSettings(chatSettings, true) ?: chatSettings
        }
        val usedChunks = retrieveChunksService.retrieveChunks(
            query = retrieveChunksFromChatRequest.query,
            version = version,
            customSearchSettings = searchSettings,
            conversationId = "$chatId",
            createPublicLinksForSources = true
        )
        usedChunks.toRetrievedChunks()

    }

    private suspend fun waitChatQueryFinish(
        chatRecord: ChatHistoryRecordEntity,
        initialTime: Instant = Instant.now(),
        timeout: Duration = 2.minutes
    ): ChatHistoryRecordEntity {
        val expirationTime = initialTime.plusSeconds(timeout.inWholeSeconds)
        return with(Dispatchers.IO) {
            var chatRecordProcessing = chatHistoryService.getChatRecord(
                chatRecord.chat.version.project.id,
                chatRecord.chat.id,
                chatRecord.id,
                chatRecord.chat.createdBy.accountId
            )
            while (Instant.now().isBefore(expirationTime) && !chatRecordProcessing.status.isFinished()) {
                delay(1000)
                chatRecordProcessing = chatHistoryService.getChatRecord(
                    chatRecord.chat.version.project.id,
                    chatRecord.chat.id,
                    chatRecord.id,
                    chatRecord.chat.createdBy.accountId
                )
            }
            log.info(
                "Got chat record processing result: id={}, status={}, response={}",
                chatRecordProcessing.id,
                chatRecordProcessing.status,
                chatRecordProcessing.response
            )
            chatRecordProcessing
        }
    }

}
