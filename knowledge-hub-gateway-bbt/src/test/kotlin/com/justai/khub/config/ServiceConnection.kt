package com.justai.khub.config

import org.springframework.http.HttpHeaders
import java.util.*

data class ServiceConnection(
    val baseUrl: String,
    val username: String,
    val password: String
) {
    fun asAuthHeaders(): HttpHeaders {
        val encodedCreds = Base64.getEncoder().encodeToString("$username:$password".toByteArray())
        val headers = HttpHeaders()
        headers.set(HttpHeaders.AUTHORIZATION, "Basic $encodedCreds")
        return headers
    }
}
