openapi: 3.0.1
info:
  title: ""
  version: ""
paths: { }

components:
  schemas:
    LongId:
      type: integer
      format: int64

    IntegrationAction:
      type: string
      enum:
        - RUN
        - STOP

    SourceType:
      type: string
      enum:
        - FILE
        - INTEGRATION

    IngestionStatus:
      type: string
      enum:
        - IDLE
        - FINISHED
        - IN_PROGRESS
        - ERROR

    QueryStatus:
      type: string
      enum:
        - IN_PROGRESS
        - DONE
        - ERROR

    IntegrationStatus:
      type: string
      enum:
        - IDLE
        - FINISHED
        - IN_PROGRESS
        - ERROR
        - STOP

    ProviderType:
      type: string
      enum:
        - CONFLUENCE
        - JIRA
        - GIT
        - S3

    UserInfoDTO:
      required:
        - accountId
        - login
        - email
        - name
      properties:
        accountId:
          $ref: '#/components/schemas/LongId'
        login:
          type: string
        email:
          type: string
        name:
          type: string
        features:
          type: array
          items:
            type: string

    IngestionJobDTO:
      type: object
      required:
        - id
        - createdAt
        - status
        - branchId
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        createdAt:
          type: object
        status:
          $ref: '#/components/schemas/IngestionStatus'
        cancelReason:
          nullable: true
          type: string
        branchId:
          $ref: '#/components/schemas/LongId'

    CreateProjectDTO:
      type: object
      required:
        - projectName
        - configDetail
      properties:
        projectName:
          type: string
        configDetail:
          $ref: '#/components/schemas/ProjectConfigDTO'
        status:
          nullable: true
          type: string

    CreateIntegrationDTO:
      type: object
      required:
        - name
        - type
        - url
        - credentials
      properties:
        name:
          type: string
        type:
          $ref: '#/components/schemas/ProviderType'
        url:
          type: string
        credentials:
          type: string


    IntegrationDTO:
      type: object
      required:
        - id
        - name
        - type
        - url
        - credentials
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        name:
          type: string
        type:
          $ref: '#/components/schemas/ProviderType'
        url:
          type: string
        credentials:
          type: string
        status:
          $ref: '#/components/schemas/IntegrationStatus'
        reason:
          nullable: true
          type:
            string

    ChatQueryDTO:
      type: object
      required:
        - message
        - chatSettings
      properties:
        message:
          type: string
        chatSettings:
          type: array
          items:
            $ref: '#/components/schemas/SettingsDTO'  

    ChatQueryResultDTO:
      type: object
      required:
        - originalQuery
        - queryId
        - status
      properties:
        originalQuery:
          $ref: '#/components/schemas/ChatQueryDTO'
        queryId:
          type: string
        status:
          $ref: '#/components/schemas/QueryStatus'  

    ChunkDTO:
      type: object
      required:
        - sourceId
        - chunkId
      properties:
        chunkId:
          $ref: '#/components/schemas/LongId'
        sourceId:
          $ref: '#/components/schemas/LongId'
        textContent:
          type: string
        binaryEncodedContent:
          type: string
        score:
          type:
            number  

    RetrieveAnswerDTO:
      type: object
      required:
        - queryId
        - chunks
      properties:
        queryId:
          type: string
        chunks:
          type: array
          items:
            $ref: '#/components/schemas/ChunkDTO'

    ChatHistoryDTO:
      type: object
      required:
        - records
      properties:
        records:
          type: array
          items:
            $ref: '#/components/schemas/ChatHistoryRecordDTO'

    ChatHistoryRecordDTO:
      type: object
      required:
        - queryMsg
        - llmAnswer
      properties:
        queryMsg:
          type: string
        llmAnswer:
          type: string
        retrieveAnswer:
          $ref: '#/components/schemas/RetrieveAnswerDTO'

    ProjectDTO:
      type: object
      required:
        - projectId
        - projectName
        - configDetail
      properties:
        projectId:
            $ref: '#/components/schemas/LongId'
        projectName:
          type: string
        configDetail:
          $ref: '#/components/schemas/ProjectConfigDTO'
        status:
          nullable: true
          type: string

    ProjectConfigDTO:
      required:
        - llm
        - embeddingModel
      properties:
        llm:
          type: string
        embeddingModel:
          type: string

    SettingsDTO:
      required:
        - setting_code
        - setting_value
        properties:
          setting_name:
            type: string
          setting_value:
            type: string


    SourceDTO:
      required:
        - id
        - name
        - createdDate
        - lastUpdateDate
        - type
        - branch
        - ingestionStatus
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        name:
          type: string
        createdDate:
          type: string
          format: 'date-time'
        lastUpdateDate:
          type: string
          format: 'date-time'
        type:
          $ref: '#/components/schemas/SourceType'
        format:
          type: string
        url:
          type: string
        credentials:
          type: string
        branch:
          type: string
        IngestionStatus:
          $ref: '#/components/schemas/IngestionStatus'

  parameters:
    accountId:
      name: accountId
      in: header
      description: Account identifier.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'

    projectId:
      name: projectId
      in: path
      description: Project identifier.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'

    queryId:
      name: queryId
      in: path
      description: Chat query identifier.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'

    branch:
      name: branch
      in: header
      description: Project data branch.
      required: false
      schema:
        type: string  
    
    optionalProjectId:
      name: projectId
      in: header
      description: Optional project identifier.
      schema:
        $ref: '#/components/schemas/LongId'   

    ingestJobId:
      name: ingestJobId
      in: header
      description: Ingestion job identifier.
      required: false
      schema:
        $ref: '#/components/schemas/LongId'  

    integrationId:
      name: integrationId
      in: path
      description: integration identifier.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'  

    integrationJobId:
      name: integrationJobId
      in: header
      description: integration job identifier.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'  

    metadataStr:
      name: metadataStr
      in: header
      description: Source meta.
      required: true
      schema:
        type: string  

    action:
      name: action
      in: header
      description: Integration action.
      required: true
      schema:
        $ref: '#/components/schemas/IntegrationAction'  
