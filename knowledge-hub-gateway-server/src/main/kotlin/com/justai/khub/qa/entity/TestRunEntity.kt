package com.justai.khub.qa.entity

import com.justai.khub.chat.entity.UsedChunksEntity
import jakarta.persistence.*
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

@Entity
@Table(name = "test_run")
@EntityListeners(AuditingEntityListener::class)
class TestRunEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = -1

    @ManyToOne
    @JoinColumn(name = "test_set_run_id", nullable = false)
    lateinit var testSetRun: TestSetRunEntity

    @ManyToOne
    @JoinColumn(name = "test_id", nullable = false)
    lateinit var test: TestEntity

    @Column
    var actualAnswer: String? = null

    @OneToOne(mappedBy = "testRun", cascade = [CascadeType.ALL])
    var usedChunks: UsedChunksEntity? = null

    @Column(nullable = false)
    var usedCompletionTokens: Int = 0

    @Column(nullable = false)
    var usedPromptTokens: Int = 0

    @Column(nullable = false)
    var processingTimeMs: Int = 0

    @Column(nullable = false)
    @CreatedDate
    lateinit var createdAt: LocalDateTime

    @Column(name = "last_error")
    var lastError: String? = null
}
