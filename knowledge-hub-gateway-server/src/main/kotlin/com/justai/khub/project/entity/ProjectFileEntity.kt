package com.justai.khub.project.entity

import com.justai.khub.common.entity.AuditableEntity
import com.justai.khub.project.dto.ProjectFileMetadata
import com.justai.khub.project.enumeration.ProjectFileType
import com.justai.khub.project.enumeration.ProjectSourceStatus
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import org.hibernate.annotations.Type

@Entity
@Table(name = "project_file")
class ProjectFileEntity : AuditableEntity() {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = -1

    @ManyToOne
    lateinit var version: ProjectVersionEntity

    @Column(nullable = false)
    lateinit var relativePath: String // user-friendly path. Can be changed on file move, ancestors rename, etc

    @Column(nullable = false)
    lateinit var pathInStorage: String // actual path in lakefs\s3

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    lateinit var status: ProjectSourceStatus

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    lateinit var fileType: ProjectFileType

    // if parent exists, the entity is attachment for it
    @ManyToOne
    var parent: ProjectFileEntity? = null

    @Column
    var lastError: String? = null

    @Column
    var sizeBytes: Int? = null

    @Column
    var sizeChars: Int? = null

    @Column
    var usedCompletionTokens: Int? = null

    @Column
    var usedPromptTokens: Int? = null

    @Column
    var parsedFileName: String? = null

    @Type(JsonType::class)
    @Column(columnDefinition = "jsonb")
    lateinit var metadata: ProjectFileMetadata

    @Column
    var contentVersion: String? = null

    @Column
    var externalId: String? = null

    @Column
    var integrationId: Long? = null

    @Column
    var lastLockedBy: String? = null
}
