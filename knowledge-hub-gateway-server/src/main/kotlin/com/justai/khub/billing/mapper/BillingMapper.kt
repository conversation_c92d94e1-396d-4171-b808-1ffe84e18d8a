package com.justai.khub.billing.mapper

import com.fasterxml.jackson.databind.node.ObjectNode
import com.justai.khub.billing.dto.AccountBalanceResponse
import com.justai.khub.billing.dto.AccountBalancesResponse
import com.justai.khub.billing.dto.TariffUpdateDto
import com.justai.khub.billing.entity.AccountBalanceEntity
import com.justai.khub.billing.entity.BillableOperationEntity
import com.justai.khub.billing.enumeration.BillableOperationStatus

object BillingMapper {

    fun List<AccountBalanceEntity>.toAccountBalancesResponse(): AccountBalancesResponse {
        return AccountBalancesResponse(
            limits = this.map { it.toAccountBalanceResponse() }
        )
    }

    fun AccountBalanceEntity.toAccountBalanceResponse(): AccountBalanceResponse {
        return AccountBalanceResponse(
            khubRequestsTariffLimit = this.availableRequestsByTariff,
            khubRequestsPackageLimit = this.availableRequestsByPackages,
            khubRequestsUsedTariffLimit = this.usedRequestsByTariff,
            khubRequestsUsedPackageLimit = this.usedRequestsByPackages,
            khubRequestsUsedPackageLimitWhenTariffPeriodStarted = this.usedRequestsByPackagesWhenTariffPeriodStarted,
            accountId = this.accountId,
            tariffVersion = this.tariffVersion
        )
    }

    fun TariffUpdateDto.toAccountBalanceEntity(): AccountBalanceEntity {
        return AccountBalanceEntity().also {
            it.accountId = this.accountId
            it.availableRequestsByTariff = this.currentRequestsByTariff
            it.tariffVersion = this.tariffVersion
            it.availableRequestsByPackages = this.totalRequestsByPackages
            it.operationsSuspended = this.operationsSuspended
        }
    }

    fun initBillableOperation(
        balance: AccountBalanceEntity,
        usedByTariff: Long,
        usedByPackage: Long,
        metadata: ObjectNode? = null
    ): BillableOperationEntity {
        return BillableOperationEntity().also {
            it.accountId = balance.accountId
            it.usedRequestsByTariff = usedByTariff
            it.usedRequestsByPackages = usedByPackage
            it.status = BillableOperationStatus.STARTED
            it.metadata = metadata
        }
    }
}
