package com.justai.khub.chat.repository

import com.justai.khub.chat.entity.UsedChunksEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface UsedChunksRepository : JpaRepository<UsedChunksEntity, Long> {
    @Query("SELECT uce FROM UsedChunksEntity uce WHERE uce.chatHistoryRecord.id = :recordId")
    fun findByChatHistoryRecordId(recordId: Long): UsedChunksEntity?
}
