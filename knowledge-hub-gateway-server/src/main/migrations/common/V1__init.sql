CREATE TABLE public.project
(
    id                    BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    repository_id         VARCHAR(255) UNIQUE,
    name                  VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    status                VARCHAR(255) NOT NULL,

    created_at            TIMESTAMP    NOT NULL,
    created_by_account_id BIGINT       NOT NULL,
    created_by_user_id    BIGINT,
    updated_at            TIMESTAMP    NOT NULL,
    updated_by_account_id BIGINT       NOT NULL,
    updated_by_user_id    BIGINT,
    deleted_at            TIMESTAMP,
    deleted_by_account_id BIGINT,
    deleted_by_user_id    BIGINT
);
ALTER SEQUENCE public.project_id_seq RESTART WITH 11611;
CREATE INDEX idx_projects_account_id ON public.project (created_by_account_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_projects_status ON public.project (status) WHERE deleted_at IS NULL;

CREATE TABLE public.project_version
(
    id                    BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    name                  <PERSON><PERSON>HAR(255) NOT NULL,
    project_id            BIGINT       NOT NULL REFERENCES public.project (id),
    ingest_settings       JSONB        NOT NULL,
    search_settings       JSONB        NOT NULL,
    index_name            VARCHAR      NOT NULL,

    created_at            TIMESTAMP    NOT NULL,
    created_by_account_id BIGINT       NOT NULL,
    created_by_user_id    BIGINT,
    updated_at            TIMESTAMP    NOT NULL,
    updated_by_account_id BIGINT       NOT NULL,
    updated_by_user_id    BIGINT,
    deleted_at            TIMESTAMP,
    deleted_by_account_id BIGINT,
    deleted_by_user_id    BIGINT
);

ALTER SEQUENCE public.project_version_id_seq RESTART WITH 10022;

CREATE INDEX idx_project_version_project_id ON public.project_version (project_id) WHERE deleted_at IS NULL;
CREATE UNIQUE INDEX idx_project_version_name ON public.project_version (project_id, name) WHERE deleted_at IS NULL;


CREATE TABLE public.project_file
(
    id                    BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    relative_path         VARCHAR      NOT NULL,
    status                VARCHAR(255) NOT NULL,
    version_id            BIGINT       NOT NULL REFERENCES public.project_version (id),

    created_at            TIMESTAMP    NOT NULL,
    created_by_account_id BIGINT       NOT NULL,
    created_by_user_id    BIGINT,
    updated_at            TIMESTAMP    NOT NULL,
    updated_by_account_id BIGINT       NOT NULL,
    updated_by_user_id    BIGINT
);
ALTER SEQUENCE public.project_file_id_seq RESTART WITH 13675;

CREATE INDEX idx_project_file_version_id ON public.project_file (version_id);
CREATE UNIQUE INDEX idx_project_file_relative_path ON public.project_file (version_id, relative_path);


CREATE TABLE public.ingestion_job
(
    id                    BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    reason                VARCHAR,
    status                VARCHAR(255) NOT NULL,
    version_id            BIGINT       NOT NULL REFERENCES public.project_version (id),

    created_at            TIMESTAMP    NOT NULL,
    created_by_account_id BIGINT       NOT NULL,
    created_by_user_id    BIGINT,
    updated_at            TIMESTAMP    NOT NULL,
    updated_by_account_id BIGINT       NOT NULL,
    updated_by_user_id    BIGINT
);
ALTER SEQUENCE public.ingestion_job_id_seq RESTART WITH 14644;
CREATE INDEX idx_ingestion_job_version_id ON public.project_file (version_id);
CREATE INDEX idx_ingestion_job_version_id_status ON public.project_file (version_id, status);

CREATE TABLE public.chat
(
    id                    BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    name                  VARCHAR   NOT NULL,
    search_settings       JSONB     NOT NULL,
    version_id            BIGINT    NOT NULL REFERENCES public.project_version (id),

    created_at            TIMESTAMP NOT NULL,
    created_by_account_id BIGINT    NOT NULL,
    created_by_user_id    BIGINT,
    updated_at            TIMESTAMP NOT NULL,
    updated_by_account_id BIGINT    NOT NULL,
    updated_by_user_id    BIGINT
);
ALTER SEQUENCE public.chat_id_seq RESTART WITH 13985;
CREATE INDEX idx_chats_version_id ON public.chat (version_id);

CREATE TABLE public.chat_history
(
    id         BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    request    VARCHAR   NOT NULL,
    response   VARCHAR,
    status     VARCHAR   NOT NULL,
    chat_id    BIGINT    NOT NULL REFERENCES public.chat (id) ON DELETE CASCADE,

    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);
ALTER SEQUENCE public.chat_history_id_seq RESTART WITH 11530;
CREATE INDEX idx_chat_history_chat_id ON public.chat_history (chat_id);
CREATE INDEX idx_chat_history_status ON public.chat_history (status);
