package com.justai.khub.project.mapper

import com.justai.khub.common.util.StringUtils
import com.justai.khub.project.entity.ProjectEntity
import com.justai.khub.project.entity.ProjectVersionEntity
import org.springframework.stereotype.Component

@Component
class ProjectVersionMapper(
    private val projectSettingsMapper: ProjectSettingsMapper
) {
    companion object {
        const val DEFAULT_VERSION = "main"
    }

    fun initVersion(project: ProjectEntity, name: String?): ProjectVersionEntity {
        val actualVersion = name ?: DEFAULT_VERSION
        val indexName = StringUtils.generateIndexName(project, actualVersion)
        val settings = projectSettingsMapper.defaultSettings(indexName)
        val (ingest, search) = projectSettingsMapper.mapUIProjectSettingsToInternalConfig(settings, indexName, null)
        return ProjectVersionEntity().also {
            it.name = actualVersion
            it.project = project
            it.ingestSettings = ingest
            it.searchSettings = search
        }
    }
}
