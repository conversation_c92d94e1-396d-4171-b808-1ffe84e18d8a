package com.justai.khub.test

import com.fasterxml.jackson.databind.node.IntNode
import com.justai.khub.base.BaseBBT
import com.justai.khub.util.BBTContextExtensions.addFile
import com.justai.khub.util.BBTContextExtensions.createProjectWithFile
import com.justai.khub.util.BBTContextExtensions.getFiles
import com.justai.khub.util.BBTContextExtensions.getSettings
import com.justai.khub.util.BBTContextExtensions.ingestSync
import com.justai.khub.util.BBTContextExtensions.sendMessageAndExpectError
import com.justai.khub.util.BBTContextExtensions.sendMessageAndExpectSuccess
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class IngestBBT : BaseBBT() {

    @Test
    fun testIngest() = withDefaultAuth {
        val project = createProjectWithFile()
        val updatedProject = ingestSync(project.id)
        assertEquals("ACTIVE", updatedProject.status)
    }

    @Test
    fun testIngestWithSegment() = withDefaultAuth {
        val project = createProjectWithFile(fileName = "segment-1-test.md", segment = "segment-1")
        addFile(project.id, fileName = "segment-2-test.md", segment = "segment-2")
        ingestSync(project.id)

        val segment1Response = sendMessageAndExpectSuccess(project.id, "Что лежит на полу?", "segment-1")
        assertTrue(segment1Response.response!!.contains("красный мяч", ignoreCase = true), "Unexpected response: ${segment1Response.response}")
        assertFalse(segment1Response.response!!.contains("синий", ignoreCase = true), "Unexpected response: ${segment1Response.response}")

        val segment2Response = sendMessageAndExpectSuccess(project.id, "Что лежит на полу?", "segment-2")
        assertTrue(segment2Response.response!!.contains("синий мяч", ignoreCase = true), "Unexpected response: ${segment2Response.response}")
        assertFalse(segment2Response.response!!.contains("красный", ignoreCase = true), "Unexpected response: ${segment2Response.response}")
    }

    @Test
    fun testUpdateIngestSettingsCauseFilesStateReset() = withDefaultAuth {
        val project = createProjectWithFile()
        ingestSync(project.id)
        val ingestedFiles = getFiles(project.id)
        assertFalse(ingestedFiles.isEmpty())
        ingestedFiles.forEach { assertEquals("INGESTED", it.status) }
        sendMessageAndExpectSuccess(project.id, "Тест")

        val projectSettings = getSettings(project.id)
        projectSettings["indexation"]?.get("chunker")?.set("chunk_size", IntNode(1234))
        projectSettings["search"]?.remove("abbreviations")

        projectsApi.setDefaultVersionSettings(project.id, projectSettings as Map<String, Any>?)

        val updatedFiles = getFiles(project.id)
        assertFalse(updatedFiles.isEmpty())
        updatedFiles.forEach { assertEquals("NOT_INGESTED", it.status) }

        val record = sendMessageAndExpectError(project.id, "Тест")
        assertEquals("khub.common.search_index_not_found", record.response)
    }
}
