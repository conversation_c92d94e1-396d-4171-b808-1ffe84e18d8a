package com.justai.khub.test

import com.justai.khub.base.BaseBBT
import com.justai.khub.util.TestDataGenerator
import com.justai.khub.util.TestDataGenerator.DEFAULT_VERSION
import org.junit.jupiter.api.Assertions.assertArrayEquals
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.http.HttpStatus
import org.springframework.web.client.HttpClientErrorException

class ProjectFilesBBT : BaseBBT() {

    @Test
    fun testProjectFilesCRUD() = withDefaultAuth {
        val project = projectsApi.createProject(TestDataGenerator.testProject())
        val testFile = TestDataGenerator.testFile("test.md")
        val uploadedFile = projectFilesApi.addFile(project.id, DEFAULT_VERSION, null, testFile)
        assertEquals("test.md", uploadedFile.name)
        assertEquals("NOT_INGESTED", uploadedFile.status)

        val filesPage = projectFilesApi.getFiles(project.id, 10, DEFAULT_VERSION, 0, null, null, null, null)
        assertTrue(filesPage.content.any { it.id == uploadedFile.id })

        val filteredFilesPage = projectFilesApi.getFiles(project.id, 10, DEFAULT_VERSION, 0, "test", null, null, null)
        assertTrue(filteredFilesPage.content.any { it.id == uploadedFile.id })

        val downloadedFile = projectFilesApi.downloadFile(project.id, uploadedFile.id)
        assertArrayEquals(testFile.contentAsByteArray, downloadedFile.contentAsByteArray)

        projectFilesApi.deleteFile(project.id, uploadedFile.id)
        val ex = assertThrows<HttpClientErrorException> { projectFilesApi.downloadFile(project.id, uploadedFile.id) }
        assertEquals(HttpStatus.NOT_FOUND, ex.statusCode)
    }
}
