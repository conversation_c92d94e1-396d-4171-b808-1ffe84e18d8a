package com.justai.khub.project.mapper

import com.justai.khub.common.dto.rag.ChunkResponse
import com.justai.khub.project.dto.LLMConfig
import com.justai.khub.qa.dto.TestEvaluationRequest
import com.justai.khub.qa.entity.TestEntity

object EvaluationServiceMapper {
    fun toEvaluationRequest(
        test: TestEntity,
        actualAnswer: String,
        usedChunks: List<ChunkResponse>,
        prompt: String,
        llm: LLMConfig
    ): TestEvaluationRequest {
        return TestEvaluationRequest(
            prompt = prompt,
            llm = llm,
            question = test.question,
            usedChunks = usedChunks,
            groundTruthAnswer = test.groundTruthAnswer,
            actualAnswer = actualAnswer
        )
    }
}
