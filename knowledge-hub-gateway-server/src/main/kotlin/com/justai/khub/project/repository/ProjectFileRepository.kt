package com.justai.khub.project.repository

import com.justai.khub.project.dto.ProjectFileMetadata
import com.justai.khub.project.entity.ProjectFileEntity
import com.justai.khub.project.enumeration.ProjectFileType
import com.justai.khub.project.enumeration.ProjectSourceStatus
import jakarta.persistence.LockModeType
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.*
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface ProjectFileRepository : JpaRepository<ProjectFileEntity, Long> {
    @Query("SELECT COUNT(f) FROM ProjectFileEntity f WHERE f.version.id=:versionId AND f.status IN :statuses AND f.fileType=:fileType")
    fun countByVersionIdAndStatusIn(versionId: Long, statuses: List<ProjectSourceStatus>, fileType: ProjectFileType = ProjectFileType.DOCUMENT): Long

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @EntityGraph(attributePaths = ["version", "version.project"])
    fun findForUpdateById(id: Long): ProjectFileEntity?

    @EntityGraph(attributePaths = ["version", "version.project"])
    @Query("SELECT f FROM ProjectFileEntity f WHERE f.status = :status AND f.fileType=:fileType")
    fun findAllByStatus(status: ProjectSourceStatus, pageable: Pageable, fileType: ProjectFileType = ProjectFileType.DOCUMENT): List<ProjectFileEntity>

    @EntityGraph(attributePaths = ["version", "version.project"])
    @Query("SELECT f FROM ProjectFileEntity f WHERE f.version.id = :versionId AND f.status IN :statuses AND f.fileType=:fileType")
    fun findAllByVersionIdAndStatusIn(
        versionId: Long,
        statuses: List<ProjectSourceStatus>,
        pageable: Pageable,
        fileType: ProjectFileType = ProjectFileType.DOCUMENT
    ): Page<ProjectFileEntity>

    @EntityGraph(attributePaths = ["version", "version.project"])
    @Query("SELECT f FROM ProjectFileEntity f WHERE f.version.id = :versionId AND f.fileType=:fileType AND f.status IN :statuses AND LOWER(f.relativePath) LIKE CONCAT('%', LOWER(:namePart), '%') ESCAPE '\\'")
    fun findAllByVersionIdAndNameContainsAndStatusIn(
        versionId: Long,
        statuses: List<ProjectSourceStatus>,
        namePart: String,
        pageable: Pageable,
        fileType: ProjectFileType = ProjectFileType.DOCUMENT
    ): Page<ProjectFileEntity>

    @EntityGraph(attributePaths = ["version", "version.project"])
    @Query("SELECT f FROM ProjectFileEntity f WHERE f.version.id = :versionId AND f.relativePath=:relativePath AND f.fileType=:fileType")
    fun findByVersionIdAndRelativePath(versionId: Long, relativePath: String, fileType: ProjectFileType = ProjectFileType.DOCUMENT): ProjectFileEntity?

    @EntityGraph(attributePaths = ["version", "version.project"])
    @Query("SELECT COUNT(f) FROM ProjectFileEntity f WHERE f.version.id=:versionId AND f.relativePath LIKE CONCAT('%', :extension)")
    fun countByVersionIdAndExtension(versionId: Long, extension: String): Long

    fun findByVersionIdAndRelativePath(versionId: Long, relativePath: String): ProjectFileEntity?

    @Query("SELECT f FROM ProjectFileEntity f WHERE f.version.id = :versionId AND f.pathInStorage in :pathsInStorage AND f.fileType=:fileType")
    fun findAllByVersionIdAndPathInStorage(versionId: Long, pathsInStorage: Iterable<String>, fileType: ProjectFileType = ProjectFileType.DOCUMENT): List<ProjectFileEntity>

    @Modifying
    @Query(
        "UPDATE ProjectFileEntity f" +
            " SET f.status=:newStatus, f.lastError=:errorMessage, f.updatedAt=:updatedAt" +
            " WHERE f.id = :id AND f.status IN :expectedStatuses AND f.fileType=:fileType"
    )
    fun updateStatusAndErrorMessage(
        id: Long,
        expectedStatuses: List<ProjectSourceStatus>,
        newStatus: ProjectSourceStatus,
        errorMessage: String? = null,
        updatedAt: LocalDateTime = LocalDateTime.now(),
        fileType: ProjectFileType = ProjectFileType.DOCUMENT
    ): Int

    @Modifying
    @Query("UPDATE ProjectFileEntity f SET f.status=:newStatus, f.updatedAt=:updatedAt WHERE f.version.id = :versionId AND f.fileType=:fileType")
    fun updateStatusByVersionId(
        versionId: Long,
        newStatus: ProjectSourceStatus,
        updatedAt: LocalDateTime = LocalDateTime.now(),
        fileType: ProjectFileType = ProjectFileType.DOCUMENT
    ): Int

    @Modifying
    @Query(
        "UPDATE ProjectFileEntity f " +
            "SET f.status=:newStatus, f.updatedAt=:updatedAt " +
            "WHERE f.status = :oldStatus AND f.lastLockedBy = :lastLockedBy AND f.fileType=:fileType"
    )
    fun updateDocumentsByStatusAndLastLockedBy(
        oldStatus: ProjectSourceStatus,
        newStatus: ProjectSourceStatus,
        lastLockedBy: String,
        updatedAt: LocalDateTime = LocalDateTime.now(),
        fileType: ProjectFileType = ProjectFileType.DOCUMENT
    ): Int

    @Modifying
    @Query(
        "UPDATE ProjectFileEntity f " +
            "SET f.status=:newStatus, f.lastError = :lastError, f.updatedAt=:updatedAt " +
            "WHERE f.version.id = :versionId AND f.status IN :oldStatuses AND f.fileType=:fileType"
    )
    fun updateByStatusIn(
        versionId: Long,
        oldStatuses: List<ProjectSourceStatus>,
        newStatus: ProjectSourceStatus,
        lastError: String? = null,
        updatedAt: LocalDateTime = LocalDateTime.now(),
        fileType: ProjectFileType = ProjectFileType.DOCUMENT
    ): Int

    @Modifying
    @Query(
        "UPDATE ProjectFileEntity f " +
            "SET f.status=:newStatus, f.updatedAt=:updatedAt " +
            "WHERE f.status = :oldStatus AND f.updatedAt < :lastUpdateBefore AND f.fileType=:fileType"
    )
    fun releaseTimedOutFiles(
        oldStatus: ProjectSourceStatus,
        newStatus: ProjectSourceStatus,
        lastUpdateBefore: LocalDateTime,
        updatedAt: LocalDateTime = LocalDateTime.now(),
        fileType: ProjectFileType = ProjectFileType.DOCUMENT
    ): Int

    @Modifying
    @Query("UPDATE ProjectFileEntity f SET f.sizeChars=:sizeChars, f.parsedFileName=:parsedFileName, f.updatedAt=:updatedAt WHERE f.id = :fileId AND f.fileType=:fileType")
    fun setSizeCharsAndParsedFileName(
        fileId: Long,
        sizeChars: Int,
        parsedFileName: String,
        updatedAt: LocalDateTime = LocalDateTime.now(),
        fileType: ProjectFileType = ProjectFileType.DOCUMENT
    )

    @Modifying
    @Query(
        "UPDATE ProjectFileEntity f" +
            " SET f.usedPromptTokens=:promptTokens, f.usedCompletionTokens=:completionTokens, f.updatedAt=:updatedAt" +
            " WHERE f.id = :fileId"
    )
    fun setTokensUsage(
        fileId: Long,
        promptTokens: Int,
        completionTokens: Int,
        updatedAt: LocalDateTime = LocalDateTime.now()
    )

    @Modifying
    @Query(
        "UPDATE ProjectFileEntity f" +
            " SET f.metadata=:metadata, f.updatedAt=:updatedAt" +
            " WHERE f.id = :fileId"
    )
    fun setMetadata(
        fileId: Long,
        metadata: ProjectFileMetadata,
        updatedAt: LocalDateTime = LocalDateTime.now()
    )

    @Query(
        "SELECT COUNT(f) AS totalFiles," +
            " SUM(COALESCE(f.sizeBytes, 0)) AS totalFilesSizeBytes," +
            " SUM(COALESCE(f.sizeChars, 0)) AS totalFilesSizeChars" +
            " FROM ProjectFileEntity f" +
            " WHERE f.version.id = :versionId AND f.fileType=:fileType"
    )
    fun getFilesSummary(versionId: Long, fileType: ProjectFileType = ProjectFileType.DOCUMENT): List<Array<Any>>

    @Query(
        "SELECT f.version.id, f.status, COUNT(f) AS count, MAX(f.updatedAt) as max_updated_at" +
            " FROM ProjectFileEntity f" +
            " WHERE f.version.id IN :versionIds AND f.fileType=:fileType" +
            " GROUP BY f.version.id, f.status"
    )
    fun getFilesSummaryByStatus(versionIds: List<Long>, fileType: ProjectFileType = ProjectFileType.DOCUMENT): List<Array<Any>>

    @EntityGraph(attributePaths = ["version", "version.project"])
    @Query(
        "SELECT f FROM ProjectFileEntity f WHERE f.integrationId = :integrationId AND f.contentVersion IS NOT NULL AND f.externalId IS NOT NULL"
    )
    fun getFilesByIntegrationId(integrationId: Long): List<ProjectFileEntity>

    @EntityGraph(attributePaths = ["version", "version.project"])
    @Query("SELECT f FROM ProjectFileEntity f WHERE f.version.id = :versionId AND f.fileType=:fileType")
    fun getFilesByVersionId(versionId: Long, fileType: ProjectFileType = ProjectFileType.DOCUMENT): List<ProjectFileEntity>

    @Query("SELECT COUNT(f) FROM ProjectFileEntity f WHERE f.version.id = :versionId AND f.status IN :statuses AND f.fileType=:fileType")
    fun getFilesCountByVersionIdAndStatuses(versionId: Long, statuses: List<ProjectSourceStatus>, fileType: ProjectFileType = ProjectFileType.DOCUMENT): Long


    @Modifying
    @Query(
        "UPDATE ProjectFileEntity f" +
            " SET f.contentVersion=:newContentVersion, f.sizeBytes = :newContentSize, f.status=:newStatus, f.updatedAt=:updatedAt" +
            " WHERE f.id = :fileId"
    )
    fun setSizeContentVersionAndStatus(
        fileId: Long,
        newContentVersion: String,
        newContentSize: Int,
        newStatus: ProjectSourceStatus,
        updatedAt: LocalDateTime = LocalDateTime.now()
    )

    @Modifying
    @Query(
        "UPDATE ProjectFileEntity f " +
            "SET f.relativePath = CONCAT(:newPrefix, SUBSTRING(f.relativePath, LENGTH(:oldPrefix) + 1)) " +
            "WHERE f.integrationId = :integrationId"
    )
    fun updateRelativePathForIntegration(integrationId: Long, oldPrefix: String, newPrefix: String)

    @Query(
        "SELECT COALESCE(SUM(pf.sizeChars), 0)" +
            " FROM ProjectFileEntity pf" +
            " WHERE pf.version.id = :versionId AND pf.status = :status AND pf.sizeChars IS NOT NULL AND pf.fileType=:fileType"
    )
    fun findIngestedCharsByVersionIdAndStatus(versionId: Long, status: ProjectSourceStatus, fileType: ProjectFileType = ProjectFileType.DOCUMENT): Int

    @EntityGraph(attributePaths = ["version", "version.project"])
    @Query("SELECT f FROM ProjectFileEntity f WHERE f.version.id = :versionId AND f.parent IS NOT NULL AND f.parent.id = :parentId")
    fun findAllByParentIdAndVersionId(
        versionId: Long,
        parentId: Long
    ): List<ProjectFileEntity>


    @Modifying
    @Query(
        "UPDATE ProjectFileEntity pfe " +
            "SET pfe.status = :newStatus, pfe.updatedAt = :updatedAt " +
            "WHERE pfe.status = :oldStatus " +
            "AND pfe.updatedAt < :lastUpdateBefore " +
            "AND pfe.fileType = :fileType"
    )
    fun releaseTimedOutJobs(
        oldStatus: ProjectSourceStatus,
        newStatus: ProjectSourceStatus,
        lastUpdateBefore: LocalDateTime,
        updatedAt: LocalDateTime = LocalDateTime.now(),
        fileType: ProjectFileType = ProjectFileType.DOCUMENT
    ): Int

}
