package com.justai.khub.project.endpoint

import com.justai.khub.api.public.IntegrationsPublicApiService
import com.justai.khub.api.public.model.IntegrationCreateRequest
import com.justai.khub.api.public.model.IntegrationDTO
import com.justai.khub.api.public.model.ProjectIntegrations
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.common.util.WebUtils.withUserAndProject
import com.justai.khub.integration.dto.IntegrationFilter
import com.justai.khub.integration.mapper.IntegrationMapper
import com.justai.khub.integration.service.IntegrationService
import com.justai.khub.project.mapper.ProjectPublicMapper
import com.justai.khub.project.mapper.ProjectPublicMapper.toPublicResponse
import com.justai.khub.project.mapper.ProjectVersionMapper.Companion.DEFAULT_VERSION
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service

@Service
class IntegrationPublicEndpoint(
    private val integrationService: IntegrationService,
) : IntegrationsPublicApiService {

    override fun createIntegration(integrationCreateRequest: IntegrationCreateRequest): IntegrationDTO = withUserAndProject { currentUser ->
        val request = ProjectPublicMapper.toInternalCreateIntegrationRequest(integrationCreateRequest)
        val newIntegration = integrationService.createIntegration(request, currentUser.projectId!!, DEFAULT_VERSION, currentUser.accountId)
        toPublicResponse(newIntegration)
    }

    override fun deleteIntegration(integrationId: Long) = withUserAndProject { currentUser ->
        if (!integrationService.deleteIntegration(integrationId, currentUser.accountId)) {
            throw KhubException(ApiErrorCode.INTEGRATION_NOT_FOUND)
        }
        Unit
    }

    override fun getIntegration(integrationId: Long): IntegrationDTO = withUserAndProject { currentUser ->
        val integration = integrationService.getIntegration(integrationId, currentUser.accountId)
        toPublicResponse(integration)
    }

    override fun getIntegrations(): ProjectIntegrations = withUserAndProject { currentUser ->
        val filter = IntegrationFilter(
            versionName = DEFAULT_VERSION,
            accountId = currentUser.accountId,
            pageable = PageRequest.of(0, Integer.MAX_VALUE, Sort.Direction.ASC, IntegrationMapper.DEFAULT_SORT),
            projectId = currentUser.projectId!!
        )
        val integrations = integrationService.getIntegrations(filter)
        toPublicResponse(integrations)
    }
}
