package com.justai.khub.integration

import com.fasterxml.jackson.databind.node.ObjectNode
import com.justai.khub.BaseProjectIT
import com.justai.khub.common.connector.IngesterConnector
import com.justai.khub.common.connector.LakefsConnector
import com.justai.khub.common.util.JSON
import com.justai.khub.integration.dto.AttachmentResponse
import com.justai.khub.integration.dto.ListResponse
import com.justai.khub.integration.dto.PageResponse
import com.justai.khub.integration.dto.SpaceResponse
import com.justai.khub.integration.entity.IntegrationEntity
import com.justai.khub.integration.repository.IntegrationRepository
import com.justai.khub.integration.service.ConfluenceIntegrationChecker
import com.justai.khub.project.enumeration.ProjectFileType
import com.justai.khub.project.enumeration.ProjectSourceStatus
import com.justai.khub.project.repository.ProjectFileRepository
import com.justai.khub.util.TestDataFactory.CONFLUENCE_TEST_URL
import com.justai.khub.util.TestDataFactory.ConfluenceTestData
import com.justai.khub.util.TestDataFactory.confluenceTestAttachment
import com.justai.khub.util.TestDataFactory.confluenceTestData
import com.justai.khub.util.TestDataFactory.confluenceTestPage
import com.justai.loadbalancer.annotations.HttpExternal
import com.justai.loadbalancer.annotations.HttpInternal
import jakarta.annotation.PostConstruct
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.mockito.Mockito.contains
import org.mockito.Mockito.`when`
import org.mockito.kotlin.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.core.ParameterizedTypeReference
import org.springframework.core.io.Resource
import org.springframework.http.HttpEntity
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatusCode
import org.springframework.http.ResponseEntity
import org.springframework.web.client.RestTemplate
import java.net.URI
import java.nio.charset.StandardCharsets
import java.nio.file.Path

class BaseConfluenceIT(val confluenceBaseUrl: String = CONFLUENCE_TEST_URL) : BaseProjectIT() {
    @Autowired
    protected lateinit var confluenceIntegrationChecker: ConfluenceIntegrationChecker

    @Autowired
    protected lateinit var integrationRepository: IntegrationRepository

    @Autowired
    protected lateinit var projectFileRepository: ProjectFileRepository

    @Autowired
    protected lateinit var lakefsConnector: LakefsConnector

    @Autowired
    protected lateinit var ingesterConnector: IngesterConnector

    @MockBean(name = "parsingServiceRestTemplate")
    @HttpInternal
    protected lateinit var parsingServiceRestTemplate: RestTemplate

    @MockBean(name = "unbufferedRestTemplate")
    @HttpExternal
    protected lateinit var unbufferedRestTemplate: RestTemplate

    @MockBean(name = "confluenceRestTemplate")
    @HttpExternal
    protected lateinit var confluenceRestTemplate: RestTemplate

    @Value("classpath:confluence/page.html")
    protected lateinit var confluencePage: Resource

    protected lateinit var confluenceTestData: ConfluenceTestData

    @PostConstruct
    fun testDataInit() {
        confluenceTestData = confluenceTestData(confluencePage.getContentAsString(StandardCharsets.UTF_8))
    }

    @BeforeEach
    fun initMocks() {
        `when`(
            confluenceRestTemplate.exchange(
                eq("$CONFLUENCE_TEST_URL/rest/api/user/current"),
                eq(HttpMethod.GET),
                any<HttpEntity<Void>>(),
                any<Class<ObjectNode>>(),
            )
        ).thenReturn(
            ResponseEntity(
                JSON.objectNode(),
                HttpStatusCode.valueOf(200)
            )
        )


        `when`(
            confluenceRestTemplate.exchange(
                contains("$confluenceBaseUrl/rest/api/space?"),
                eq(HttpMethod.GET),
                any<HttpEntity<Void>>(),
                any<ParameterizedTypeReference<ListResponse<SpaceResponse>>>(),
                eq("testspace")
            )
        ).thenReturn(
            ResponseEntity(
                ListResponse(start = 0, limit = 1, size = 1, results = listOf(confluenceTestData.space)),
                HttpStatusCode.valueOf(200)
            )
        )

        mockConfluencePages(confluenceTestData.pageWithAttachments.keys)
        confluenceTestData.pageWithAttachments.entries.forEach { (page, attachments) ->
            mockConfluenceAttachments(page.id, attachments)
        }
    }

    fun mockConfluencePages(pages: Collection<PageResponse>) {
        `when`(
            confluenceRestTemplate.exchange(
                contains("$confluenceBaseUrl/rest/api/content/?"),
                eq(HttpMethod.GET),
                any<HttpEntity<Void>>(),
                any<ParameterizedTypeReference<ListResponse<PageResponse>>>(),
                any<String>(),
                any<Int>(),
                any<Int>()
            )
        ).thenReturn(
            ResponseEntity(
                ListResponse(
                    start = 0,
                    limit = 1,
                    size = 1,
                    results = pages.toList()
                ),
                HttpStatusCode.valueOf(200)
            )
        )
    }

    fun mockConfluenceAttachments(pageId: String, attachments: List<AttachmentResponse>) {
        `when`(
            confluenceRestTemplate.exchange(
                contains("$confluenceBaseUrl/rest/api/content/{id}/child/attachment?"),
                eq(HttpMethod.GET),
                any<HttpEntity<Void>>(),
                any<ParameterizedTypeReference<ListResponse<AttachmentResponse>>>(),
                eq(pageId),
                any<Int>(),
                any<Int>()
            )
        ).thenReturn(
            ResponseEntity(
                ListResponse(
                    start = 0,
                    limit = 1,
                    size = 1,
                    results = attachments
                ),
                HttpStatusCode.valueOf(200)
            )
        )
    }

    @AfterEach
    fun resetMocks() {
        reset(confluenceRestTemplate, parsingServiceRestTemplate, unbufferedRestTemplate)
    }

    protected fun updatePageContent(newContent: String): Map<PageResponse, List<AttachmentResponse>> {
        val updPage = confluenceTestData.pageWithAttachments.mapKeys { (page, _) ->
            confluenceTestPage(
                content = newContent,
                id = page.id,
                title = page.title!!,
                version = page.version.number + 1
            )
        }
        mockConfluencePages(updPage.keys)
        return updPage
    }

    protected fun updateAttachments(): Map<PageResponse, List<AttachmentResponse>> {
        val updAttachments = confluenceTestData.pageWithAttachments.mapValues { (page, attachments) ->
            val updAttachments = attachments.map {
                confluenceTestAttachment(it.id + 1000, it.title!!)
            }
            mockConfluenceAttachments(page.id, updAttachments)
            updAttachments
        }
        return updAttachments
    }

    protected fun List<AttachmentResponse>.checkUpdates(
        integration: IntegrationEntity,
        page: PageResponse,
        expectedStatus: ProjectSourceStatus,
        expectedAddNewVersionCount: Int,
        expectedDeletionPreviousVersionCount: Int
    ) {
        this.forEach { attachment ->
            val integrationFiles = projectFileRepository.getFilesByIntegrationId(integration.id)
            val pageEntity = integrationFiles.firstOrNull {
                attachment.id.toString() == it.externalId
            }
            assertNotNull(pageEntity, "Attachment does not found")
            assertEquals(
                1,
                integrationFiles.count { it.pathInStorage == "${integration.id}/${page.title}.html/${attachment.title}" })
            assertEquals(expectedStatus, pageEntity!!.status)
            //check deletion previous version of attachments
            verify(ingesterConnector, times(expectedDeletionPreviousVersionCount)).deleteFile(
                any<String>(),
                eq("${integration.id}/${page.title}.html/${attachment.title}"),
            )
            verify(lakefsConnector, times(expectedDeletionPreviousVersionCount)).deleteFile(
                any<String>(),
                any<String>(),
                eq("${integration.id}/${page.title}.html/${attachment.title}")
            )
            //check add new version of attachments
            verify(unbufferedRestTemplate, times(expectedAddNewVersionCount)).execute<Any>(
                eq(URI.create("${confluenceBaseUrl}/download/attachment/${attachment.id}")),
                eq(HttpMethod.GET),
                org.mockito.kotlin.any(),
                org.mockito.kotlin.any()
            )
            verify(lakefsConnector, times(expectedAddNewVersionCount)).addFile(
                any<String>(),
                any<String>(),
                eq("${integration.id}/${page.title}.html/${attachment.title}"),
                any<Path>()
            )
        }
    }

    protected fun PageResponse.checkUpdate(
        integration: IntegrationEntity,
        expectedStatus: ProjectSourceStatus,
        addFileCount: Int,
        ingestDeleteCount: Int
    ) {
        val pageEntity = projectFileRepository.getFilesByIntegrationId(integration.id).firstOrNull {
            this.id.toString() == it.externalId
        }
        assertNotNull(pageEntity, "Page does not found")
        assertEquals(expectedStatus, pageEntity!!.status)
        verify(lakefsConnector, times(addFileCount)).addFile(
            any<String>(),
            any<String>(),
            eq("${integration.id}/${this.title}.html"),
            any<Path>()
        )
        verify(ingesterConnector, times(ingestDeleteCount)).deleteFile(
            any<String>(),
            eq("${integration.id}/${this.title}.html")
        )
    }

    fun verifyIntegrationFiles(integration: IntegrationEntity, countOfDocuments: Int, countOfAttachment: Int) {
        val confluenceFiles = projectFileRepository.getFilesByIntegrationId(integration.id)
        assertEquals(countOfAttachment, confluenceFiles.filter { it.fileType == ProjectFileType.ATTACHMENT }.size)
        assertEquals(countOfDocuments, confluenceFiles.filter { it.fileType == ProjectFileType.DOCUMENT }.size)
    }

    protected fun imitateIngestion(integration: IntegrationEntity) {
        val confluenceFiles = projectFileRepository.getFilesByIntegrationId(integration.id)
        val ingestedFiles = confluenceFiles.map {
            it.status = ProjectSourceStatus.INGESTED
            it.sizeBytes = 10
            it.sizeChars = 10
            it.lastError = null
            it
        }
        projectFileRepository.saveAll(ingestedFiles)
    }

    fun verifyIntegration(
        integration: IntegrationEntity,
        pageWithAttachments: Map<PageResponse, List<AttachmentResponse>>,
        pageAddCount: Int,
        pageIngestDeleteCount: Int,
        attachmentsAddNewVersionCount: Int,
        attachmentDeletePreviousVersionCount: Int,
        pagesStatus: ProjectSourceStatus,
        attachmentsStatus: ProjectSourceStatus
    ) {
        val docsSize = pageWithAttachments.keys.size +
            pageWithAttachments.values.flatten().count { it.title?.endsWith("pdf") == true }
        val attachmentsSize =
            pageWithAttachments.values.flatten().count { it.title?.endsWith("png") == true }
        verifyIntegrationFiles(integration, docsSize, attachmentsSize)

        pageWithAttachments.forEach { (page, attachments) ->
            page.checkUpdate(integration, pagesStatus, pageAddCount, pageIngestDeleteCount)
            attachments.checkUpdates(
                integration,
                page,
                attachmentsStatus,
                attachmentsAddNewVersionCount,
                attachmentDeletePreviousVersionCount
            )
        }
    }
}
