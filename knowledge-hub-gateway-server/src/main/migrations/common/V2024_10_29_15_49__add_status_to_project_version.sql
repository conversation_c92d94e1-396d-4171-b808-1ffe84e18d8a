ALTER TABLE public.project_version ADD COLUMN status VARCHAR(255);
UPDATE public.project_version this_version SET status = this_project.status FROM public.project this_project WHERE this_version.project_id = this_project.id;
ALTER TABLE public.project_version ALTER COLUMN status SET NOT NULL;
DROP INDEX IF EXISTS idx_projects_status;
ALTER TABLE public.project DROP COLUMN status;
CREATE INDEX idx_project_versions_status ON public.project_version (status) WHERE deleted_at IS NULL;
