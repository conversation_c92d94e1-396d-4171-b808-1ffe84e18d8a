package com.justai.khub.ratelimit.dto

import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Duration

@ConfigurationProperties("rate-limit")
data class RateLimitProperties(
    val bucketExpiredAfter: Duration,
    val removeExpiredBuckets: RemoveExpiredBucketsProperties,
)

data class RemoveExpiredBucketsProperties(
    val enabled: <PERSON><PERSON><PERSON>,
    val maxToRemoveInOneTransaction: Int,
    val thresholdToContinueRemoving: Int,
)
