package com.justai.khub.chat.mapper

import com.justai.khub.api.fe.model.*
import com.justai.khub.api.public.model.Chat
import com.justai.khub.attachment.service.AttachmentService.Companion.ATTACHMENT_URL_PREFIX
import com.justai.khub.chat.entity.ChatEntity
import com.justai.khub.chat.entity.ChatHistoryRecordEntity
import com.justai.khub.chat.entity.UsedChunksEntity
import com.justai.khub.chat.enumeration.ChatHistoryRecordStatus
import com.justai.khub.common.dto.DocumentChunk
import com.justai.khub.common.dto.DocumentChunkWithSource
import com.justai.khub.common.dto.DocumentChunks
import com.justai.khub.common.dto.RelevantSource
import com.justai.khub.common.dto.rag.ChunkResponse
import com.justai.khub.common.mapper.CommonMapper.toAuditDTO
import com.justai.khub.common.mapper.SearchConfigMapper
import com.justai.khub.project.dto.SearchPipelineConfig
import com.justai.khub.project.entity.ProjectFileEntity
import com.justai.khub.project.entity.ProjectVersionEntity
import com.justai.khub.query.dto.UserHistory
import org.springframework.data.domain.Page
import org.springframework.stereotype.Component
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*

@Component
class ChatMapper(
    private val searchConfigMapper: SearchConfigMapper
) {
    val DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm-ss").withZone(ZoneId.systemDefault())
    val INITIAL_MESSAGE_RU = "Привет! Задай любой вопрос!"
    val INITIAL_MESSAGE_EN = "Hello! Ask any question!"

    val DEFAULT_LOCALE = Locale("ru")

    fun ChatEntity.toDTO(): ChatDTO {
        return ChatDTO(
            id = this.id,
            name = this.name,
            audit = this.toAuditDTO()
        )
    }

    fun ChatEntity.toPublicDto(): Chat {
        val search = this.searchSettings
        return Chat(
            this.id,
            searchConfigMapper.run { search.toRagSettings() },
            this.name
        )
    }

    fun ChatHistoryRecordEntity.toDTO(): ChatHistoryRecordDTO {
        return ChatHistoryRecordDTO(
            id = this.id,
            chatId = this.chat.id,
            request = this.request,
            response = this.response,
            status = this.status.toString(),
            createdAt = this.createdAt.atZone(ZoneId.systemDefault()).toInstant(),
            updatedAt = this.updatedAt.atZone(ZoneId.systemDefault()).toInstant(),
            relevantSources = this.addInfo?.relevantSources
                ?.takeIf { this.chat.searchSettings.showSources ?: false }
                ?.map { RelevantSourceDTO(it.sourceId, it.path, it.externalLink?.takeIf { link -> !link.contains(ATTACHMENT_URL_PREFIX) }) }
        )
    }

    fun initChatEntity(version: ProjectVersionEntity, chatName: String?, internal: Boolean = false): ChatEntity {
        return ChatEntity().apply {
            this.version = version
            this.name = chatName ?: DATE_FORMATTER.format(Instant.now())
            this.searchSettings = version.searchSettings
            this.internal = internal
        }
    }

    fun initChatRecordEntity(chat: ChatEntity, request: String, segment: String?, settings: SearchPipelineConfig?, userHistory: UserHistory? = null): ChatHistoryRecordEntity {
        return ChatHistoryRecordEntity().apply {
            this.chat = chat
            this.request = request
            this.segment = segment
            this.status = ChatHistoryRecordStatus.READY_TO_PROCESS
            this.createdAt = LocalDateTime.now()
            this.updatedAt = LocalDateTime.now()
            this.searchSettings = settings
            this.userHistory = userHistory
        }
    }

    fun initialChatRecord(chat: ChatEntity, locale: Locale = DEFAULT_LOCALE): ChatHistoryRecordEntity {
        return ChatHistoryRecordEntity().apply {
            this.chat = chat
            this.request = ""
            this.response = if (locale == DEFAULT_LOCALE) INITIAL_MESSAGE_RU else INITIAL_MESSAGE_EN
            this.status = ChatHistoryRecordStatus.FINISHED
            this.createdAt = LocalDateTime.now()
            this.updatedAt = LocalDateTime.now()
        }
    }

    fun toHistoryPage(page: Page<ChatHistoryRecordEntity>): ChatHistoryPage {
        return ChatHistoryPage(
            content = page.content.map { it.toDTO() }.sortedBy { it.createdAt },
            paging = PagingResponse(
                totalCount = page.totalElements,
                pageNum = page.number,
                pageSize = page.size
            )
        )
    }

    fun toChatPage(page: Page<ChatEntity>): ChatsPage {
        return ChatsPage(
            content = page.content.map { it.toDTO() },
            paging = PagingResponse(
                totalCount = page.totalElements,
                pageNum = page.number,
                pageSize = page.size
            )
        )
    }

    fun initUsedChunksEntity(chunks: List<ChunkResponse>): UsedChunksEntity {
        return UsedChunksEntity().apply {
            this.chunks = DocumentChunks(chunks = chunks.map { toDocumentChunk(it) })
        }
    }

    fun toDocumentChunk(chunkResponse: ChunkResponse): DocumentChunk {
        return DocumentChunk(
            id = chunkResponse.id,
            docId = chunkResponse.docId,
            content = chunkResponse.content ?: "",
            summary = chunkResponse.summary,
            questions = chunkResponse.questions,
            keywords = chunkResponse.keywords,
            score = chunkResponse.score,
        )
    }

    fun toChunkWithSource(chunkResponse: ChunkResponse, source: RelevantSource): DocumentChunkWithSource {
        return DocumentChunkWithSource(
            toDocumentChunk(chunkResponse),
            source = source
        )
    }

    fun toChunkWithSource(
        chunk: DocumentChunk,
        sourceFile: ProjectFileEntity,
        sourceFileLink: String?
    ): DocumentChunkWithSource {
        return DocumentChunkWithSource(
            chunk,
            source = RelevantSource(
                sourceId = sourceFile.id,
                path = sourceFile.relativePath,
                externalLink = sourceFileLink
            )
        )
    }
}
