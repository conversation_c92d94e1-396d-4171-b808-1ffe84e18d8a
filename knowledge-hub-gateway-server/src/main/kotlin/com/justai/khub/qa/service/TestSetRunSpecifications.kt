package com.justai.khub.qa.service

import com.justai.khub.project.entity.ProjectEntity
import com.justai.khub.qa.entity.TestSetEntity
import com.justai.khub.qa.entity.TestSetRunEntity
import jakarta.persistence.criteria.Predicate
import org.springframework.data.jpa.domain.Specification
import java.time.LocalDate

object TestSetRunSpecifications {
    fun withProjectId(projectId: Long?): Specification<TestSetRunEntity> {
        return Specification { root, _, criteriaBuilder ->
            if (projectId == null) {
                criteriaBuilder.conjunction()
            } else {
                criteriaBuilder.equal(
                    root.get<TestSetEntity>("testSet")
                        .get<ProjectEntity>("project")
                        .get<Long>("id"),
                    projectId
                )
            }
        }
    }

    fun withTestSetIds(testSetIds: Set<Long>): Specification<TestSetRunEntity> {
        return Specification { root, _, criteriaBuilder ->
            if (testSetIds.isEmpty()) {
                criteriaBuilder.conjunction()
            } else {
                root.get<TestSetEntity>("testSet").get<Long>("id").`in`(testSetIds)
            }
        }
    }

    fun withDateRange(dateFrom: LocalDate?, dateTo: LocalDate?): Specification<TestSetRunEntity> {
        return Specification { root, _, criteriaBuilder ->
            val predicates = mutableListOf<Predicate>()

            dateFrom?.let { from ->
                predicates.add(
                    criteriaBuilder.greaterThanOrEqualTo(
                        root.get("updatedAt"),
                        from.atStartOfDay()
                    )
                )
            }

            dateTo?.let { to ->
                predicates.add(
                    criteriaBuilder.lessThanOrEqualTo(
                        root.get("updatedAt"),
                        to.plusDays(1).atStartOfDay()
                    )
                )
            }

            when {
                predicates.isEmpty() -> criteriaBuilder.conjunction()
                predicates.size == 1 -> predicates.first()
                else -> criteriaBuilder.and(*predicates.toTypedArray())
            }
        }
    }
}
