package com.justai.khub.util

import com.justai.khub.report.dto.ReportEntry
import com.justai.khub.report.dto.ReportFilter
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVParser
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.httpBasic
import org.springframework.stereotype.Component
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import java.io.ByteArrayInputStream
import java.io.InputStreamReader
import java.nio.charset.StandardCharsets
import java.time.LocalDate


@Component
class ReportHelper {

    @Value("\${server.username}")
    private lateinit var basicAuthUser: String

    @Value("\${server.password}")
    private lateinit var basicAuthPassword: String

    @Autowired
    protected lateinit var mvc: MockMvc

    fun generateReport(filter: ReportFilter): ByteArray {
        val result = mvc.perform(
            MockMvcRequestBuilders.get("/api/khub/internal/report")
                .param("accountId", "${filter.accountId}")
                .param("period", "${filter.period}")
                .param("dateFrom", filter.dateFrom?.toString())
                .param("dateTo", filter.dateTo?.toString())
                .with(httpBasic(basicAuthUser, basicAuthPassword))
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn()
        return result.response.contentAsByteArray
    }

    fun parseReport(content: ByteArray): List<ReportEntry> {
        val reportEntries: MutableList<ReportEntry> = ArrayList()

        ByteArrayInputStream(content).use { input ->
            InputStreamReader(input, StandardCharsets.UTF_8).use { reader ->
                CSVParser(reader, CSVFormat.DEFAULT.builder().setHeader().setSkipHeaderRecord(true).build()).use { csvParser ->
                    for (csvRecord in csvParser) {
                        reportEntries.add(
                            ReportEntry(
                                date = LocalDate.parse(csvRecord["Date"]),
                                recordsCount = csvRecord["RecordsCount"].toInt(),
                                usedCompletionTokens = csvRecord["UsedCompletionTokens"].toInt(),
                                usedPromptTokens = csvRecord["UsedPromptTokens"].toInt()
                            )
                        )
                    }
                }
            }
        }
        return reportEntries
    }
}
