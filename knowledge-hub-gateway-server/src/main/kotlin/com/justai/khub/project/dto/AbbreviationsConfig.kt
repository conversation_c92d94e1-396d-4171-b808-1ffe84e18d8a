package com.justai.khub.project.dto

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class Abbreviation(
    val name: String,
    val variations: List<String> = emptyList(),
    val description: String? = ""
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class AbbreviationsConfig(
    val fileName: String,
    val values: Map<String, Abbreviation>
)
