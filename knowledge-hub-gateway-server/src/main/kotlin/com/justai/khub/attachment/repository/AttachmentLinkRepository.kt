package com.justai.khub.attachment.repository

import com.justai.khub.attachment.entity.AttachmentLinkEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface AttachmentLinkRepository : JpaRepository<AttachmentLinkEntity, Long> {
    fun findByPublicId(linkId: String): AttachmentLinkEntity?

    @Modifying
    @Query("DELETE FROM AttachmentLinkEntity ale WHERE ale.expiredAt <= :now")
    fun deleteExpired(now: LocalDateTime): Int
}
