package com.justai.khub.project.mapper

import com.fasterxml.jackson.core.type.TypeReference
import com.justai.khub.common.configuration.dto.IngestProperties
import com.justai.khub.common.configuration.dto.KHubUser
import com.justai.khub.common.configuration.dto.RagProperties
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.common.service.PromptsService
import com.justai.khub.common.util.JSON
import com.justai.khub.project.dto.*
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class ProjectSettingsMapper(
    private val promptsService: PromptsService,
    private val indexationSettingsMapper: IndexationSettingsMapper,
    private val searchSettingsMapper: SearchSettingsMapper,
    private val generationSettingsMapper: GenerationSettingsMapper,
    private val ragProperties: RagProperties,
    private val ingestProperties: IngestProperties,
) {
    private val log = LoggerFactory.getLogger(this::class.java)

    companion object {
        const val DEFAULT_SIMILARITY_TOP_K = 5
        const val DEFAULT_NUM_CANDIDATES = 50
        const val DEFAULT_CANDIDATE_RADIUS = 0
        const val DEFAULT_CHUNK_SIZE = 1000
        const val DEFAULT_TOKENS_SOFT_LIMIT = 1000
        const val DEFAULT_CONTEXT_WINDOW = 16000
        const val DEFAULT_MAX_TOKENS = 1000
        const val DEFAULT_TEMPERATURE = 0.0
        const val DEFAULT_OPENAI_EMBEDDINGS_DIMENSION = 3072
        const val DEFAULT_RERANKER_MODEL = "BAAI/bge-reranker-v2-m3"
        const val DEFAULT_HF_EMBEDDINGS_DIMENSION = 1024
        const val DEFAULT_HF_MAX_SEQUENCE_LENGTH = 512
        const val DEFAULT_OPENAI_MAX_SEQUENCE_LENGTH = 8191
        const val DEFAULT_MIN_SCORE = -5
        const val DEFAULT_MAX_CHUNKS = 5
        const val DEFAULT_SCORE_REDUCTION_LIMIT = 1
        const val DEFAULT_MAX_CHUNKS_PER_DOC = 1

        val abbreviationsTypeRef: TypeReference<HashMap<String, Abbreviation>> = object : TypeReference<HashMap<String, Abbreviation>>() {}
    }

    fun toDTO(source: SearchPipelineConfig, currentUser: KHubUser): Map<String, Any> {
        val schema = SettingsSchema(
            partitions = listOf(
                searchSettingsMapper.searchPartition(source, currentUser),
                generationSettingsMapper.generationPartition(source, currentUser)
            )
        )
        return JSON.toMap(schema)
    }

    fun toDTO(
        ingestionConfig: IngestPipelineConfig,
        searchConfig: SearchPipelineConfig,
        currentUser: KHubUser
    ): Map<String, Any> {
        val settingsSchema = SettingsSchema(
            partitions = listOf(
                indexationSettingsMapper.indexPartition(ingestionConfig),
                searchSettingsMapper.searchPartition(searchConfig, currentUser),
                generationSettingsMapper.generationPartition(searchConfig, currentUser)
            )
        )
        return JSON.toMap(settingsSchema)
    }

    fun defaultSettings(indexName: String): UISettings {
        val defaultLlmProvider = if (ragProperties.llm.models.first().value.startsWith("openai/")) LLMProvider.openai else LLMProvider.mlp
        val defaultModel = ragProperties.llm.models.first().value

        val indexation = UIIndexation(
            type = ingestProperties.vectorizer.options.first().value,
            llm = LLMConfig(
                llmProvider = defaultLlmProvider,
                model = defaultModel,
            )
        )
        val search = UISearch(
            type = SearchPipelineType.semantic,
            retrieving = UIRetrieving(
                history = UIHistory(
                    historyCondensePrompt = promptsService.rephraseWithHistoryPrompt(),
                ),
            ),
            advanced = UISearchAdvancedSettings(csvProcessingEnabled = true)
        )
        val generation = UIGeneration(
            llm = LLMConfig(
                llmProvider = defaultLlmProvider,
                model = defaultModel,
            ),
            systemPrompt = promptsService.responseGenerationPrompt()
        )
        return UISettings(indexation = indexation, search = search, generation = generation)
    }

    fun mapUIRagSettingsToInternalSearchConfig(
        uiSettings: UISettings,
        currentIngestSettings: IngestPipelineConfig,
        currentSearchSettings: SearchPipelineConfig?
    ): SearchPipelineConfig {
        val searchPipelineConfig = when (uiSettings.search.type) {
            SearchPipelineType.semantic -> SemanticSearchPipelineConfig(
                dao = DaoConfig(currentIngestSettings.dao!!.indexName),
                llm = uiSettings.generation.llm,
                search = uiSettings.search.retrieving.toRetrievingConfig(),
                reranker = if (uiSettings.search.reranker?.useReranker == true) {
                    uiSettings.search.reranker.toRerankerConfig()
                } else {
                    null
                },
                abbreviations = uiSettings.search.abbreviations?.toAbbreviationsConfig(),
                showSources = uiSettings.generation.sources.showSources,
                responseGenerator = ResponseGeneratorConfig(
                    prompt = if (uiSettings.generation.sources.showSources) {
                        promptsService.addRulesAndGet(
                            userGenerationPrompt = uiSettings.generation.systemPrompt ?: promptsService.responseGenerationPrompt(),
                            rules = listOf(promptsService.relevantDocsInAnswerRule())
                        )
                    } else {
                        promptsService.removeRulesAndGet(
                            userGenerationPrompt = uiSettings.generation.systemPrompt ?: promptsService.responseGenerationPrompt(),
                            rules = listOf(promptsService.relevantDocsInAnswerRule())
                        )

                    },
                    mode = if (uiSettings.generation.sources.showSources) ResponseMode.compact_with_relevant_sources else ResponseMode.compact
                ),
                csvProcessorConfig = uiSettings.toCsvProcessorConfig() ?: currentSearchSettings?.csvProcessorConfig
            )

            SearchPipelineType.agent -> AgentSearchPipelineConfig(
                dao = DaoConfig(currentIngestSettings.dao!!.indexName),
                llm = uiSettings.generation.llm,
                search = uiSettings.search.retrieving.toRetrievingConfig(),
                reranker = if (uiSettings.search.reranker?.useReranker == true) {
                    uiSettings.search.reranker.toRerankerConfig()
                } else {
                    null
                },
                abbreviations = uiSettings.search.abbreviations?.toAbbreviationsConfig(),
                showSources = uiSettings.generation.sources.showSources,
                systemPrompt = uiSettings.generation.systemPrompt,
                csvProcessorConfig = uiSettings.toCsvProcessorConfig() ?: currentSearchSettings?.csvProcessorConfig
            )
        }
        return searchPipelineConfig
    }

    fun mapUIProjectSettingsToInternalConfig(
        newSettings: UISettings,
        defaultIndexName: String,
        currentSearchSettings: SearchPipelineConfig?
    ): Pair<IngestPipelineConfig, SearchPipelineConfig> {
        val ingestSettings = IngestPipelineConfig(
            chunker = newSettings.indexation!!.toChunker(),
            vectorizer = VectorizerConfig(model = newSettings.indexation.type),
            dao = DaoConfig(defaultIndexName)
        )
        val searchPipelineConfig = mapUIRagSettingsToInternalSearchConfig(newSettings, ingestSettings, currentSearchSettings)
        return ingestSettings to searchPipelineConfig
    }

    fun validateSearchPipelineWithGenerationLLM(projectSettings: UISettings): UISettings {
        return projectSettings.generation.llm.model?.let { llm ->
            val availableModel = ragProperties.llm.models.firstOrNull { it.value == llm }
            if (availableModel != null
                && availableModel.pipelineTypes.isNotEmpty()
                && !availableModel.pipelineTypes.contains(projectSettings.search.type.name)
            ) {
                val defaultAvailableLlm = ragProperties.llm.models.firstOrNull {
                    it.pipelineTypes.isEmpty() || it.pipelineTypes.contains(projectSettings.search.type.name)
                }
                projectSettings.copy(
                    generation = projectSettings.generation.copy(
                        llm = projectSettings.generation.llm.copy(
                            model = defaultAvailableLlm?.value
                        )
                    )
                )
            } else {
                projectSettings
            }
        } ?: projectSettings
    }

    private fun UIReranker.toRerankerConfig(): RerankerConfig {
        return when(this.type) {
            RerankerType.manual -> ManualRerankerConfig(
                minScore = this.minScore ?: DEFAULT_MIN_SCORE,
                maxChunks = this.maxChunks ?: DEFAULT_MAX_CHUNKS,
                scoreReductionLimit = this.scoreReductionLimit ?: DEFAULT_SCORE_REDUCTION_LIMIT,
                maxChunksPerDoc = this.maxChunksPerDoc ?: DEFAULT_MAX_CHUNKS_PER_DOC
            )
            else -> ModelRerankerConfig(
                minScore = this.minScore ?: DEFAULT_MIN_SCORE,
                maxChunks = this.maxChunks ?: DEFAULT_MAX_CHUNKS,
                model = this.model ?: ragProperties.reranker[RerankerType.model]?.value ?: DEFAULT_RERANKER_MODEL
            )
        }
    }

    private fun UIRetrieving.toRetrievingConfig(): RetrievingConfig {
        return RetrievingConfig(
            rephraseQuery = this.rephrase?.rephraseQuery ?: false,
            queryRephrasePrompt = this.rephrase?.prompt,
            useHistory = this.history?.useHistory ?: false,
            historyCondensePrompt = this.history?.historyCondensePrompt,
            historyMaxTokens = this.history?.historyMaxTokens,
            historyMinUserMessages = this.history?.historyMinUserMessages,
            similarityTopK = this.similarityTopK,
            numCandidates = this.similarityTopK * 10,
            candidateRadius = this.candidateRadius,
            useAllEmbeddings = this.metadataSearch,
            fullTextSearch = this.fullText
        )
    }

    private fun UIFileData.toAbbreviationsConfig(): AbbreviationsConfig {
        return try {
            AbbreviationsConfig(this.fileName, JSON.parse(this.rawData, abbreviationsTypeRef))
        } catch (ex: Exception) {
            log.warn("Incorrect abbreviation: ${this.rawData}", ex)
            throw KhubException(ApiErrorCode.INCORRECT_ABBREVIATION_FORMAT)
        }
    }

    private fun UISettings.toCsvProcessorConfig(): CSVProcessorConfig? {
        // Skip resetting to defaults when no updates received. User might lack permission to modify these settings
        if (search.advanced == null || generation.advanced == null) {
            return null
        }
        return CSVProcessorConfig(
            enabled = this.search.advanced.csvProcessingEnabled ?: true,
            promptAdjustment = this.generation.advanced.csvProcessorPromptAdjustment
        )
    }

    private fun UIIndexation.toChunker(): ChunkerConfig {
        return ChunkerConfig(
            type = this.chunkMethod.type,
            chunkSize = this.chunker.chunkSize,
            tokensSoftLimit = this.chunker.tokensSoftLimit,
            maxLlmChunkerTokens = this.chunker.maxLlmChunkerTokens,
            useForLargeTables = this.chunker.useForLargeTables,
            language = this.chunker.language,
            model = this.llm.model,
            frequencyPenalty = this.llm.frequencyPenalty,
            maxTokens = this.llm.maxTokens,
            presencePenalty = this.llm.presencePenalty,
            temperature = this.llm.temperature,
            topP = this.llm.topP,
            contextWindow = this.llm.contextWindow
        )
    }

}

