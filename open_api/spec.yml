openapi: 3.0.1

info:
  title: khub-gw api
  version: 2.0.0

servers:
  - url: http://localhost:10026
    description: Generated server url

paths:
  /api/khub/web/user/me:
    get:
      tags:
        - User
      operationId: getMe
      summary: 'Send cookie and get user info'
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserInfoDTO'

  /api/khub/projects:
    get:
      tags:
        - Projects
      operationId: getProjects
      summary: ''
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProjectDTO'

    post:
      tags:
        - Projects
      operationId: createProject
      summary: ''
      parameters:
        - $ref: '#/components/parameters/accountId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateProjectDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectDTO'

    delete:
      tags:
        - Projects
      operationId: deleteProject
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProjectDTO'

  /api/khub/projects/{projectId}:
    get:
      tags:
        - Projects
      operationId: getProject
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectDTO'

  /api/khub/projects/{projectId}/files:
    post:
      tags:
        - Files
      operationId: addFiles
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/metadataStr'
        - $ref: '#/components/parameters/branch'
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                filename:
                  type: array
                  items:
                    type: string
                    format:
                      binary
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SourceDTO'
    get:
      tags:
        - Files
      operationId: getAll
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/branch'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SourceDTO'

    delete:
      tags:
        - Files
      operationId: deleteFile
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: integer
                format: integer

  /api/khub/projects/{projectId}/integration:
    post:
      tags:
        - Integrations
      operationId: addExternalIntegration
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateIntegrationDTO'
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntegrationDTO'
    get:
      tags:
        - Integrations
      operationId: getExternalIntegration
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/integrationId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntegrationDTO'
    put:
      tags:
        - Integrations
      operationId: putAction
      summary: ''
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/integrationId'
        - $ref: '#/components/parameters/action'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntegrationDTO'


  /api/khub/projects/{projectId}/ingest:
    post:
      tags:
        - Ingest
      operationId: ingest
      summary: 'Ingest rag project by new data'
      parameters:
        - $ref: '#/components/parameters/accountId'
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/branch'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IngestionJobDTO'
    get:
      tags:
        - Ingest
      operationId: getIngestionJobs
      summary: 'Get active ingestion jobs'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/ingestJobId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/IngestionJobDTO'

  /api/khub/projects/{projectId}/chat:
    post:
      tags:
        - Chat
      operationId: chatQuery
      summary: 'Chat query to project'
      parameters:
        - $ref: '#/components/parameters/projectId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChatQueryDTO'
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatQueryResultDTO'
    get:
      tags:
        - Chat
      operationId: getQueryProcessingStatus
      summary: 'Check chat query status'
      parameters:
        - $ref: '#/components/parameters/projectId'
        - $ref: '#/components/parameters/queryId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatQueryResultDTO'

  /api/khub/projects/{projectId}/chatHistory:
    get:
      tags:
        - Chat
      operationId: history
      summary: 'Chat historyto project'
      parameters:
        - $ref: '#/components/parameters/projectId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChatHistoryDTO'

  /api/khub/projects/{projectId}/settings/chatSettings:
    get:
      tags:
        - Settings
      operationId: getChatSettings
      summary: 'Chat settings constants'
      parameters:
        - $ref: '#/components/parameters/projectId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SettingsDTO'

    put:
      tags:
        - Settings
      operationId: putChatSettings
      summary: 'Chat settings constants'
      parameters:
        - $ref: '#/components/parameters/projectId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/SettingsDTO'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SettingsDTO'

  /api/khub/projects/{projectId}/settings/llm:
    get:
      tags:
        - Settings
      operationId: getAvailableLLMs
      summary: 'Available LLM constants'
      parameters:
        - $ref: '#/components/parameters/projectId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SettingsDTO'

  /api/khub/projects/{projectId}/settings/embeddingModel:
    get:
      tags:
        - Settings
      operationId: getAvailableEmbeddingModels
      summary: 'Available embedding models constants'
      parameters:
        - $ref: '#/components/parameters/projectId'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SettingsDTO'

components:
  schemas:
    LongId:
      type: integer
      format: int64

    IntegrationAction:
      type: string
      enum:
        - RUN
        - STOP

    SourceType:
      type: string
      enum:
        - FILE
        - INTEGRATION

    IngestionStatus:
      type: string
      enum:
        - IDLE
        - FINISHED
        - IN_PROGRESS
        - ERROR

    QueryStatus:
      type: string
      enum:
        - IN_PROGRESS
        - DONE
        - ERROR

    IntegrationStatus:
      type: string
      enum:
        - IDLE
        - FINISHED
        - IN_PROGRESS
        - ERROR
        - STOP

    ProviderType:
      type: string
      enum:
        - CONFLUENCE
        - JIRA
        - GIT
        - S3

    UserInfoDTO:
      required:
        - accountId
        - login
        - email
        - name
      properties:
        accountId:
          $ref: '#/components/schemas/LongId'
        login:
          type: string
        email:
          type: string
        name:
          type: string
        features:
          type: array
          items:
            type: string

    IngestionJobDTO:
      type: object
      required:
        - id
        - createdAt
        - status
        - branchId
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        createdAt:
          type: object
        status:
          $ref: '#/components/schemas/IngestionStatus'
        cancelReason:
          nullable: true
          type: string
        branchId:
          $ref: '#/components/schemas/LongId'

    CreateProjectDTO:
      type: object
      required:
        - projectName
        - configDetail
      properties:
        projectName:
          type: string
        configDetail:
          $ref: '#/components/schemas/ProjectConfigDTO'
        status:
          nullable: true
          type: string

    CreateIntegrationDTO:
      type: object
      required:
        - name
        - type
        - url
        - credentials
      properties:
        name:
          type: string
        type:
          $ref: '#/components/schemas/ProviderType'
        url:
          type: string
        credentials:
          type: string


    IntegrationDTO:
      type: object
      required:
        - id
        - name
        - type
        - url
        - credentials
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        name:
          type: string
        type:
          $ref: '#/components/schemas/ProviderType'
        url:
          type: string
        credentials:
          type: string
        status:
          $ref: '#/components/schemas/IntegrationStatus'
        reason:
          nullable: true
          type:
            string

    ChatQueryDTO:
      type: object
      required:
        - message
        - chatSettings
      properties:
        message:
          type: string
        chatSettings:
          type: array
          items:
            $ref: '#/components/schemas/SettingsDTO'

    ChatQueryResultDTO:
      type: object
      required:
        - originalQuery
        - queryId
        - status
      properties:
        originalQuery:
          $ref: '#/components/schemas/ChatQueryDTO'
        queryId:
          type: string
        status:
          $ref: '#/components/schemas/QueryStatus'

    ChunkDTO:
      type: object
      required:
        - sourceId
        - chunkId
      properties:
        chunkId:
          $ref: '#/components/schemas/LongId'
        sourceId:
          $ref: '#/components/schemas/LongId'
        textContent:
          type: string
        binaryEncodedContent:
          type: string
        score:
          type:
            number

    RetrieveAnswerDTO:
      type: object
      required:
        - queryId
        - chunks
      properties:
        queryId:
          type: string
        chunks:
          type: array
          items:
            $ref: '#/components/schemas/ChunkDTO'

    ChatHistoryDTO:
      type: object
      required:
        - records
      properties:
        records:
          type: array
          items:
            $ref: '#/components/schemas/ChatHistoryRecordDTO'

    ChatHistoryRecordDTO:
      type: object
      required:
        - queryMsg
        - llmAnswer
        - createdAt
        - id
      properties:
        queryMsg:
          type: string
        llmAnswer:
          type: string
        createdAt:
          type: string
        id:
          type: number
        retrieveAnswer:
          $ref: '#/components/schemas/RetrieveAnswerDTO'

    ProjectDTO:
      type: object
      required:
        - projectId
        - projectName
        - configDetail
        - createdAt
      properties:
        createdAt:
          type: string
        projectId:
          $ref: '#/components/schemas/LongId'
        projectName:
          type: string
        configDetail:
          $ref: '#/components/schemas/ProjectConfigDTO'
        status:
          nullable: true
          type: string

    ProjectConfigDTO:
      required:
        - llm
        - embeddingModel
      properties:
        llm:
          type: string
        embeddingModel:
          type: string

    SettingsDTO:
      required:
        - setting_name
        - setting_value
      properties:
        setting_name:
          type: string
        setting_value:
          type: string


    SourceDTO:
      required:
        - id
        - name
        - createdDate
        - lastUpdateDate
        - type
        - branch
        - ingestionStatus
      properties:
        id:
          $ref: '#/components/schemas/LongId'
        name:
          type: string
        createdDate:
          type: string
          format: 'date-time'
        lastUpdateDate:
          type: string
          format: 'date-time'
        type:
          $ref: '#/components/schemas/SourceType'
        format:
          type: string
        url:
          type: string
        credentials:
          type: string
        branch:
          type: string
        IngestionStatus:
          $ref: '#/components/schemas/IngestionStatus'

  parameters:
    accountId:
      name: accountId
      in: header
      description: Account identifier.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'

    projectId:
      name: projectId
      in: path
      description: Project identifier.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'

    projectId_notReq:
      name: projectId_notReq
      in: path
      description: Project identifier.
      required: false
      schema:
        $ref: '#/components/schemas/LongId'

    queryId:
      name: queryId
      in: path
      description: Chat query identifier.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'

    branch:
      name: branch
      in: header
      description: Project data branch.
      required: false
      schema:
        type: string

    optionalProjectId:
      name: projectId
      in: header
      description: Optional project identifier.
      schema:
        $ref: '#/components/schemas/LongId'

    ingestJobId:
      name: ingestJobId
      in: header
      description: Ingestion job identifier.
      required: false
      schema:
        $ref: '#/components/schemas/LongId'

    integrationId:
      name: integrationId
      in: path
      description: integration identifier.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'

    integrationJobId:
      name: integrationJobId
      in: header
      description: integration job identifier.
      required: true
      schema:
        $ref: '#/components/schemas/LongId'

    metadataStr:
      name: metadataStr
      in: header
      description: Source meta.
      required: true
      schema:
        type: string

    action:
      name: action
      in: header
      description: Integration action.
      required: true
      schema:
        $ref: '#/components/schemas/IntegrationAction'
