package com.justai.khub.channel

import com.justai.khub.BaseProjectIT
import com.justai.khub.api.fe.model.ChannelType
import com.justai.khub.channel.enumeration.ChannelStatus
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.util.*

class ChannelIT : BaseProjectIT() {

    @Test
    fun testJaicpChannelIntegration() {
        val channelName = UUID.randomUUID().toString()
        val channel = channelHelper.createChannel(
            projectId = project.id,
            channelName = channelName,
            channelType = ChannelType.JAICP
        )
        assertEquals(ChannelType.JAICP, channel.type)
        assertEquals("/$channelName", channel.externalLink)
        assertEquals(ChannelStatus.ACTIVE.toString(), channel.status)

        val channelPage = channelHelper.getChannelsPage(projectId = project.id)
        assertEquals(listOf(channel).map { it.id to it.type }, channelPage.content.map { it.id to it.type })
        assertEquals(2, ((channelPage.content.first().settings as Map<*, *>).get("channels") as List<*>).size)
    }


    @Test
    fun testJaicpChannelRemovedWhenProjectIsDeleted() {
        val newProject = projectHelper.createProject()
        val channel = channelHelper.createChannel(
            projectId = newProject.id,
            channelName = "test",
            channelType = ChannelType.JAICP
        )
        val channelPage = channelHelper.getChannelsPage(projectId = newProject.id)
        assertEquals(listOf(channel).map { it.id to it.type }, channelPage.content.map { it.id to it.type })

        projectHelper.deleteProjectAndExpectOk(newProject.id)
    }
}
