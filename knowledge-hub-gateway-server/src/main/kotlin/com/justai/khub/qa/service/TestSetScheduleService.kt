package com.justai.khub.qa.service

import com.justai.khub.api.fe.model.TestSetRunRequest
import com.justai.khub.api.fe.model.TestSetScheduleDTO
import com.justai.khub.common.configuration.dto.QAProperties
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.project.entity.ProjectVersionEntity
import com.justai.khub.project.service.ProjectVersionService
import com.justai.khub.qa.entity.TestSetRunEntity
import com.justai.khub.qa.entity.TestSetScheduleEntity
import com.justai.khub.qa.mapper.QAMapper
import com.justai.khub.qa.repository.TestSetScheduleRepository
import org.slf4j.LoggerFactory
import org.springframework.scheduling.support.CronExpression
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.ZoneId
import java.time.ZonedDateTime

@Service
class TestSetScheduleService(
    private val testSetService: TestSetService,
    private val testSetScheduleRepository: TestSetScheduleRepository,
    private val testSetRunService: TestSetRunService,
    private val qaMapper: QAMapper,
    private val projectVersionService: ProjectVersionService,
    private val qaProperties: QAProperties,
) {

    private val log = LoggerFactory.getLogger(this::class.java)

    @Transactional(readOnly = true)
    fun getTestSetSchedule(testSetId: Long, accountId: Long): TestSetScheduleEntity? {
        val testSet = testSetService.getTestSet(testSetId, accountId)
        return testSetScheduleRepository.findByTestSetId(testSet.id)
    }

    @Transactional(readOnly = true)
    fun getReadyToSchedule(): List<TestSetScheduleEntity> {
        val now = ZonedDateTime.now(ZoneId.of(qaProperties.schedulingTimezone)).toEpochSecond()
        return testSetScheduleRepository.findScheduledTasksForActiveProjects(now)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun deleteTestSetSchedule(testSetId: Long, accountId: Long) {
        val testSet = testSetService.getTestSet(testSetId, accountId)
        val schedule = testSet.schedule
        if (schedule != null) {
            testSet.schedule = null
            testSetScheduleRepository.delete(schedule)
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    fun setTestSetSchedule(
        testSetId: Long,
        request: TestSetScheduleDTO,
        accountId: Long
    ): TestSetScheduleEntity {
        val cronExpression = validate(request.schedule)
        val testSet = testSetService.getTestSet(testSetId, accountId)
        val existed = testSetScheduleRepository.findByTestSetId(testSet.id)
        val updated = qaMapper.applyUpdate(existed, testSet, request, cronExpression)
        return testSetScheduleRepository.save(updated)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun trySchedule(schedule: TestSetScheduleEntity) {
        val lockedSchedule = testSetScheduleRepository.findForUpdateSkipLocked(schedule.id)
        if (lockedSchedule == null) {
            log.warn("Skip scheduling test set {}: already processing", schedule.testSet.id)
            return
        }
        try {
            val activeTestSetRun = testSetRunService.findActiveTestSetRun(lockedSchedule.testSet.id)
            if (activeTestSetRun != null) {
                log.warn("Skip scheduling test set {}: test set run already started", schedule.testSet.id)
                return
            }
            val lastFinishedTestSetRun = testSetRunService.findLastFinishedTestSetRun(lockedSchedule.testSet.id)
            val defaultVersion = projectVersionService.getDefaultVersion(lockedSchedule.testSet.project.id)
            val settingsChanged = isSettingsChanged(lastFinishedTestSetRun, defaultVersion)
            val sourcesChanged = isSourcesChanged(lastFinishedTestSetRun, defaultVersion)
            if (!schedule.runOnSettingsChange && !schedule.runOnSourcesChange) {
                log.info("Scheduled running test set {}", schedule.testSet.id)
                testSetService.runTestSetInternal(schedule.testSet, TestSetRunRequest(true))
            } else if (schedule.runOnSettingsChange && settingsChanged) {
                log.info("Scheduled running test set {}: settings changed", schedule.testSet.id)
                testSetService.runTestSetInternal(schedule.testSet, TestSetRunRequest(true))
            } else if (schedule.runOnSourcesChange && sourcesChanged) {
                log.info("Scheduled running test set {}: sources changed", schedule.testSet.id)
                testSetService.runTestSetInternal(schedule.testSet, TestSetRunRequest(true))
            } else {
                log.debug("Skip scheduling test set {}", schedule.testSet.id)
            }
        } catch (ex: Exception) {
            log.error("Failed to schedule test set {}", schedule.testSet.id, ex)
        } finally {
            reschedule(lockedSchedule)
        }
    }

    private fun isSettingsChanged(
        lastFinishedTestSetRun: TestSetRunEntity?,
        defaultVersion: ProjectVersionEntity
    ): Boolean {
        if (lastFinishedTestSetRun == null) return true
        val settingsUpdatedAt = defaultVersion.settingsUpdatedAt ?: defaultVersion.createdAt
        return settingsUpdatedAt > lastFinishedTestSetRun.updatedAt
    }

    private fun isSourcesChanged(
        lastFinishedTestSetRun: TestSetRunEntity?,
        defaultVersion: ProjectVersionEntity
    ): Boolean {
        if (lastFinishedTestSetRun == null) return true
        val settingsUpdatedAt = defaultVersion.sourcesUpdatedAt ?: defaultVersion.createdAt
        return settingsUpdatedAt > lastFinishedTestSetRun.updatedAt
    }

    private fun validate(schedule: String): CronExpression {
        try {
            return CronExpression.parse(schedule)
        } catch (ex: Exception) {
            throw KhubException(ApiErrorCode.TESTSET_SCHEDULE_INVALID_FORMAT, ex)
        }
    }

    private fun reschedule(locked: TestSetScheduleEntity) {
        locked.scheduledAt = CronExpression.parse(locked.schedule).next(ZonedDateTime.now(ZoneId.of(qaProperties.schedulingTimezone)))!!.toEpochSecond()
        testSetScheduleRepository.save(locked)
    }
}
