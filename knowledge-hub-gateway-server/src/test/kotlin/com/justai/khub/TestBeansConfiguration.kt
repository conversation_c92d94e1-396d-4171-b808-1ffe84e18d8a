package com.justai.khub

import com.justai.khub.attachment.job.ExpiredLinksCleanupJob
import com.justai.khub.attachment.service.AttachmentService
import com.justai.khub.billing.job.RollbackOutdatedOperationsJob
import com.justai.khub.channel.dto.BotConfigDto
import com.justai.khub.channel.dto.BotProjectReadDto
import com.justai.khub.channel.dto.DeploymentResult
import com.justai.khub.common.configuration.dto.IntegrationProperties
import com.justai.khub.common.connector.IngesterConnector
import com.justai.khub.common.connector.JaicpServiceConnector
import com.justai.khub.common.connector.LakefsConnector
import com.justai.khub.common.connector.RagServiceConnector
import com.justai.khub.common.job.TimedOutEntitiesReleaseJob
import com.justai.khub.mock.MockRagServiceConnector
import com.justai.khub.project.mapper.RagServiceMapper
import com.justai.khub.qa.job.TestSetProcessingJob
import com.justai.khub.qa.job.TestSetScheduleProcessingJob
import com.justai.khub.util.AuthUtils
import com.justai.khub.util.JsonDefaultContentTypeMockMvcConfigurer
import com.justai.khub.util.TestDataFactory
import com.justai.loadbalancer.annotations.HttpInternal
import com.justai.onprem.license.KeyV1
import com.justai.security.session.auth.JustSessionAuthentication
import com.justai.zb.migrations.SpringMigrations
import io.lakefs.clients.sdk.ApiClient
import jakarta.annotation.PostConstruct
import org.apache.commons.lang3.RandomStringUtils
import org.mockito.Mockito
import org.mockito.kotlin.any
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.setup.DefaultMockMvcBuilder
import org.springframework.test.web.servlet.setup.MockMvcBuilders
import org.springframework.web.client.RestTemplate
import org.springframework.web.context.WebApplicationContext
import java.nio.file.Path
import java.time.Instant
import java.util.*


@TestConfiguration
class TestBeansConfiguration {
    private val log = LoggerFactory.getLogger(this::class.java)

    @Autowired
    private lateinit var wac: WebApplicationContext

    @PostConstruct
    fun init() {
        SpringMigrations.ofEnvironment(wac.environment).cleanMigrate()
    }

    @Bean
    fun defaultAuth(): JustSessionAuthentication {
        return AuthUtils.mockCCUser()
    }

    @Bean
    fun mockMvc(wac: WebApplicationContext): MockMvc {
        return MockMvcBuilders
            .webAppContextSetup(wac)
            .apply<DefaultMockMvcBuilder>(SecurityMockMvcConfigurers.springSecurity())
            .apply<DefaultMockMvcBuilder>(JsonDefaultContentTypeMockMvcConfigurer())
            .build()
    }

    @Bean
    @Primary
    fun lakefsConnector(lakeFSClient: ApiClient,
                        integrationProperties: IntegrationProperties): LakefsConnector {
        val spy = Mockito.spy(LakefsConnector(lakeFSClient, integrationProperties))
        Mockito.doAnswer {  "repository-id-${RandomStringUtils.randomAlphabetic(10)}" }.`when`(spy).createRepository(any())
        Mockito.doAnswer { }.`when`(spy).deleteRepository(any())
        Mockito.doAnswer { }.`when`(spy).createBranch(any(), any())
        Mockito.doAnswer { }.`when`(spy).deleteFile(any(), any(), any())
        Mockito.doAnswer { }.`when`(spy).addFile(any(), any(), any(), any<Path>())
        Mockito.doReturn(byteArrayOf()).`when`(spy).getFileContent(any(), any(), any())
        return spy
    }

    @Bean
    @Primary
    fun ragServiceConnector(
        ragServiceMapper: RagServiceMapper,
        @HttpInternal ragServiceRestTemplate: RestTemplate,
        attachmentService: AttachmentService,
        integrationProperties: IntegrationProperties
    ): RagServiceConnector {
        log.info("Creating MockRagServiceConnector")
        return MockRagServiceConnector(ragServiceMapper, ragServiceRestTemplate, attachmentService, integrationProperties)
    }


    @Bean
    @Primary
    fun ingesterConnector(): IngesterConnector {
        val mock = Mockito.mock(IngesterConnector::class.java)
        Mockito.`when`(mock.deleteFile(any(), any())).thenAnswer { }
        return mock
    }

    @Bean
    @Primary
    fun testSetProcessingJob(): TestSetProcessingJob {
        return Mockito.mock(TestSetProcessingJob::class.java)
    }

    @Bean
    @Primary
    fun testSetScheduleProcessingJob(): TestSetScheduleProcessingJob {
        return Mockito.mock(TestSetScheduleProcessingJob::class.java)
    }

    @Bean
    @Primary
    fun expiredLinksCleanupJob(): ExpiredLinksCleanupJob {
        return Mockito.mock(ExpiredLinksCleanupJob::class.java)
    }

    @Bean
    @Primary
    fun rollbackOutdatedOperationsJob(): RollbackOutdatedOperationsJob {
        return Mockito.mock(RollbackOutdatedOperationsJob::class.java)
    }

    @Bean
    @Primary
    fun timedOutEntitiesReleaseJob(): TimedOutEntitiesReleaseJob {
        return Mockito.mock(TimedOutEntitiesReleaseJob::class.java)
    }

    @Bean
    @Primary
    fun jaicpServiceConnector(): JaicpServiceConnector {
        val mock = Mockito.mock(JaicpServiceConnector::class.java)
        setupJaicpServiceMock(mock)
        return mock
    }

    @ConditionalOnProperty(prefix = "test-license-key", name = ["enabled"], havingValue = "true")
    @Primary
    @Bean
    fun testLicenseKey(): KeyV1 {
        return TestDataFactory.createTestLicenseKey()
    }

    companion object {
        val JAICP_PROJECT_ID = 123L

        fun setupJaicpServiceMock(jaicpServiceConnector: JaicpServiceConnector) {
            Mockito.`when`(jaicpServiceConnector.createProjectFromKhubTemplate(any(), any(), any())).thenAnswer { invocation ->
                val projName = invocation.arguments[0] as String
                BotProjectReadDto(
                    JAICP_PROJECT_ID,
                    name = projName,
                    shortName = projName,
                    defaultBranch = null,
                    product = "jaicp",
                    botType = null,
                    webHookUrl = null,
                    templateName = "khub-template-dafault",
                    lastModificationData = Instant.now()
                )
            }
            val name = UUID.randomUUID().toString()
            Mockito.`when`(jaicpServiceConnector.getKhubProjects(any())).thenReturn(
                listOf(
                    BotProjectReadDto(
                        JAICP_PROJECT_ID,
                        name = name,
                        shortName = name,
                        defaultBranch = null,
                        product = "jaicp",
                        botType = null,
                        webHookUrl = null,
                        templateName = "khub-template-dafault",
                        lastModificationData = Instant.now(),
                        botConfigs = listOf(
                            BotConfigDto(
                                id = 1,
                                channelType = "TEST_WIDGET",
                                botserverBotId = UUID.randomUUID().toString(),
                                removed = false,
                                blocked = false,
                                lastDeployResult = DeploymentResult(11, status = "OK"),
                                description = null
                            ),
                            BotConfigDto(
                                id = 2,
                                channelType = "YANDEX",
                                botserverBotId = UUID.randomUUID().toString(),
                                removed = false,
                                blocked = false,
                                lastDeployResult = DeploymentResult(12, status = "OK"),
                                description = null
                            ),
                            BotConfigDto(
                                id = 3,
                                channelType = "BITRIX",
                                botserverBotId = UUID.randomUUID().toString(),
                                removed = false,
                                blocked = false,
                                lastDeployResult = DeploymentResult(13, status = "OK"),
                                description = null
                            )
                        )
                    )
                )
            )
            Mockito.`when`(jaicpServiceConnector.deleteProject(any(), any())).thenAnswer { invocation ->
                val id = invocation.arguments[1] as Long
                BotProjectReadDto(id = id, name = "i`m deleted")
            }
        }
    }
}
