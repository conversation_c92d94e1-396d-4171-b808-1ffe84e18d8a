package com.justai.khub.qa.service

import com.justai.khub.api.fe.model.GenerateTestSetRequest
import com.justai.khub.api.fe.model.TestSetRunRequest
import com.justai.khub.api.fe.model.TestSetUpdateRequest
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.common.util.JSON
import com.justai.khub.project.service.ProjectFileService
import com.justai.khub.project.service.ProjectService
import com.justai.khub.project.service.ProjectVersionService
import com.justai.khub.qa.entity.TestSetEntity
import com.justai.khub.qa.entity.TestSetRunEntity
import com.justai.khub.qa.enumeration.TestSetRunStatus
import com.justai.khub.qa.enumeration.TestSetStatus
import com.justai.khub.qa.mapper.QAMapper
import com.justai.khub.qa.repository.TestSetRepository
import com.justai.khub.qa.repository.TestSetRunRepository
import jakarta.annotation.PostConstruct
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import kotlin.jvm.optionals.getOrNull

@Service
class TestSetService(
    private val testSetRepository: TestSetRepository,
    private val testSetRunRepository: TestSetRunRepository,
    private val projectService: ProjectService,
    private val projectVersionService: ProjectVersionService,
    private val projectFileService: ProjectFileService,
    private val testService: TestService,
    private val testSetReportGenerator: TestSetReportGenerator,
    private val transactionTemplate: TransactionTemplate,
    private val testSetParser: TestSetParser,
    private val qaMapper: QAMapper,
) {

    private lateinit var testSetSampleContent: ByteArray

    @PostConstruct
    fun init() {
        testSetSampleContent = TestSetService::class.java.getResourceAsStream("/templates/testSet.xlsx")?.use { it.readAllBytes() }
            ?: throw IllegalStateException("Missed resource '/templates/testSet.xlsx'")
    }

    @Transactional(rollbackFor = [Exception::class])
    fun createTestSet(projectId: Long, request: GenerateTestSetRequest, accountId: Long): TestSetEntity {
        val project = projectService.getProject(projectId, accountId)
        checkNameUnique(request.name, projectId)
        checkHasSources(project.id)
        val newTestSet = qaMapper.toGeneratedTestSet(request, project)
        return testSetRepository.saveAndFlush(newTestSet)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun createTestSet(projectId: Long, name: String, file: ByteArray, accountId: Long): TestSetEntity {
        val project = projectService.getProject(projectId, accountId)
        checkNameUnique(name, projectId)
        val tests = testSetParser.parse(file)
        val newTestSet = qaMapper.toManualTestSet(name, tests.size, project)
        val createdTestSet = testSetRepository.saveAndFlush(newTestSet)
        tests.forEach { it.testSet = createdTestSet }
        testService.createTests(tests)
        return createdTestSet
    }

    private fun checkNameUnique(newTestSetName: String, projectId: Long) {
        if (testSetRepository.findByProjectIdAndArchivedFalseAndName(projectId, newTestSetName).isNotEmpty()) {
            throw KhubException(ApiErrorCode.DUPLICATE_TESTSET_NAME)
        }
    }


    @Transactional(rollbackFor = [Exception::class])
    fun deleteTestSet(testSetId: Long, accountId: Long): Boolean {
        // TODO check has active test runs and generating processes
        val testSet = findTestSetAndCheckAccess(testSetId, accountId)
        testSetRepository.delete(testSet)
        return true
    }

    @Transactional(rollbackFor = [Exception::class])
    fun runTestSet(
        testSetId: Long,
        testSetRunRequest: TestSetRunRequest,
        accountId: Long
    ): TestSetRunEntity {
        val testSet = findTestSetAndCheckAccess(testSetId, accountId)
        return runTestSetInternal(testSet, testSetRunRequest)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun runTestSetInternal(testSet: TestSetEntity, testSetRunRequest: TestSetRunRequest): TestSetRunEntity {
        checkHasSources(testSet.project.id)
        if (testSet.status != TestSetStatus.ACTIVE) {
            throw KhubException(ApiErrorCode.TESTSET_NOT_READY)
        }
        val existed = testSetRunRepository.findByTestSetIdAndStatusIn(
            testSet.id,
            listOf(TestSetRunStatus.READY_TO_PROCESS, TestSetRunStatus.PROCESSING)
        )
        if (existed.isNotEmpty()) {
            return existed.first()
        }
        return testSetRunRepository.saveAndFlush(qaMapper.toNewRun(testSet, testSetRunRequest))
    }

    private fun checkHasSources(projectId: Long) {
        val defaultVersion = projectVersionService.getDefaultVersion(projectId)
        if (projectFileService.findIngestedFilesCount(defaultVersion.id) <= 0) {
            throw KhubException(ApiErrorCode.TESTSET_MISSING_SOURCES)
        }
    }

    private fun findTestSetAndCheckAccess(testSetId: Long, accountId: Long): TestSetEntity {
        val testSet = testSetRepository.findById(testSetId).getOrNull() ?: throw KhubException(ApiErrorCode.TESTSET_NOT_FOUND)
        if (testSet.createdBy.accountId != accountId) {
            throw KhubException(ApiErrorCode.ACCESS_DENIED)
        }
        return testSet
    }

    @Transactional(readOnly = true)
    fun getTestSetsPage(projectId: Long, accountId: Long, pageable: PageRequest): Page<TestSetEntity> {
        val project = projectService.getProject(projectId, accountId) // to check access rights
        return testSetRepository.findByProjectIdAndArchived(project.id, false, pageable)
    }

    @Transactional(readOnly = true)
    fun getArchivedTestSetsPage(projectId: Long, accountId: Long, pageable: PageRequest): Page<TestSetEntity> {
        val project = projectService.getProject(projectId, accountId) // to check access rights
        return testSetRepository.findByProjectIdAndArchived(project.id, true, pageable)
    }

    @Transactional(readOnly = true)
    fun getReadyToGenerate(limit: Int): List<TestSetEntity> {
        val pagination = PageRequest.of(0, limit, Sort.by(Sort.Direction.ASC, "createdAt"))
        return testSetRepository.findAllByStatus(TestSetStatus.READY_TO_PROCESS, pagination)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun startGeneration(testSet: TestSetEntity): Boolean {
        return testSetRepository.updateStatusAndLastError(
            testSet.id,
            listOf(TestSetStatus.READY_TO_PROCESS),
            TestSetStatus.PROCESSING,
            null
        ) > 0
    }

    @Transactional(rollbackFor = [Exception::class])
    fun finishGeneration(testSet: TestSetEntity): Boolean {
        return testSetRepository.updateStatusAndLastError(
            testSet.id,
            listOf(TestSetStatus.PROCESSING),
            TestSetStatus.ACTIVE,
            null
        ) > 0
    }

    @Transactional(rollbackFor = [Exception::class])
    fun failGeneration(testSet: TestSetEntity, errorCode: String): Boolean {
        return testSetRepository.updateStatusAndLastError(
            testSet.id,
            listOf(TestSetStatus.PROCESSING),
            TestSetStatus.FAILED,
            errorCode
        ) > 0
    }

    @Transactional(readOnly = true)
    fun getTestSet(testSetId: Long, accountId: Long): TestSetEntity {
        return findTestSetAndCheckAccess(testSetId, accountId)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun changeArchivedStatus(testSetId: Long, archived: Boolean, accountId: Long) {
        val testSet = findTestSetAndCheckAccess(testSetId, accountId)
        testSetRepository.changeArchivedStatus(testSet.id, archived)
    }

    fun generateFile(testSetId: Long, accountId: Long): Pair<String, ByteArray> {
        val testSet = transactionTemplate.execute { getTestSet(testSetId, accountId) }!!
        val tests = testService.findAllByTestSetId(testSet.id)
        val reportContent = testSetReportGenerator.generateReport(tests)
        return testSet.name to reportContent
    }

    fun getTestSetSample(): ByteArray {
        return testSetSampleContent
    }

    @Transactional(rollbackFor = [Exception::class])
    fun cancelGeneration(testSetId: Long, accountId: Long) {
        val testSet = findTestSetAndCheckAccess(testSetId, accountId)
        testSetRepository.updateStatusAndLastError(
            testSet.id,
            listOf(TestSetStatus.READY_TO_PROCESS, TestSetStatus.PROCESSING),
            TestSetStatus.CANCELLED,
            ApiErrorCode.TESTSET_GENERATION_CANCELLED.code
        )
    }

    @Transactional(rollbackFor = [Exception::class])
    fun updateTestSet(
        testSetId: Long,
        request: TestSetUpdateRequest,
        accountId: Long
    ): TestSetEntity {
        val testSet = findTestSetAndCheckAccess(testSetId, accountId)
        val newName = request.name ?: testSet.name
        val newEvaluationModel = request.evaluationModel ?: testSet.properties.evaluationModel
        val newEvaluationPrompt = request.evaluationPrompt ?: testSet.properties.evaluationPrompt
        val newProperties = testSet.properties.copy(
            evaluationPrompt = if (newEvaluationPrompt?.isEmpty() == true) null else newEvaluationPrompt,
            evaluationModel = if (newEvaluationModel?.isEmpty() == true) null else newEvaluationModel,
        )
        testSetRepository.updateNameAndProperties(testSet.id, newName, JSON.stringify(newProperties))
        return findTestSetAndCheckAccess(testSetId, accountId)
    }

    fun isProcessing(testSetId: Long): Boolean {
        val testSet = testSetRepository.findById(testSetId).getOrNull() ?: return false
        return testSet.status == TestSetStatus.PROCESSING
    }
}
