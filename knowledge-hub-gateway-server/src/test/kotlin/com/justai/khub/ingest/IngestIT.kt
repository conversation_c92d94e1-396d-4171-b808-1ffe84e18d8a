package com.justai.khub.ingest

import com.justai.khub.BaseProjectIT
import com.justai.khub.api.public.model.ProjectStatus
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class IngestIT : BaseProjectIT() {

    @Test
    fun testCreate() {
        projectFileHelper.uploadFile(project.id)
        val updatedProject = ingestHelper.startIngest(project.id)
        assertEquals(ProjectStatus.INGESTING_DOCUMENTS.toString(), updatedProject.status)
    }

    @Test
    fun testCancel() {
        projectFileHelper.uploadFile(project.id)
        ingestHelper.startIngest(project.id)
        ingestHelper.cancelIngestAndExpectOk(project.id, "test reason")
        val updatedProject = projectHelper.getProjectAndExpectOk(project.id)
        assertEquals(ProjectStatus.CREATED.toString(), updatedProject.status)
    }
}
