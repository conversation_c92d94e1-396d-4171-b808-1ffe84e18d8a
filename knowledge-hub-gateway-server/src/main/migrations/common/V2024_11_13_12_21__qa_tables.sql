CREATE TABLE public.test_set
(
    id                     BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    name                   <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    type                   VA<PERSON>HAR(64)  NOT NULL,
    status                 VARCHAR(64)  NOT NULL,
    properties             JSONB        NOT NULL,
    description            VARCHAR(8192),
    archived               <PERSON><PERSON><PERSON><PERSON>N      NOT NULL DEFAULT FALSE,
    last_error             VARCHAR(512),

    total_tests            INTEGER      NOT NULL DEFAULT 0,
    used_prompt_tokens     INTEGER      NOT NULL DEFAULT 0,
    used_completion_tokens INTEGER      NOT NULL DEFAULT 0,

    project_id             BIGINT       NOT NULL REFERENCES public.project (id) ON DELETE CASCADE,

    created_at             TIMESTAMP    NOT NULL,
    updated_at             TIMESTAMP,
    created_by_account_id  BIGINT       NOT NULL,
    created_by_user_id     BIGINT
);

ALTER SEQUENCE public.test_set_id_seq RESTART WITH 15200;
CREATE INDEX idx_test_set_project_id ON public.test_set (project_id);


CREATE TABLE public.test_set_schedule
(
    id                  BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    schedule            VARCHAR(128)  NOT NULL,
    test_set_id         BIGINT UNIQUE NOT NULL REFERENCES public.test_set (id) ON DELETE CASCADE,
    run_on_settings_change BOOLEAN       NOT NULL,
    run_on_sources_change  BOOLEAN       NOT NULL
);

CREATE TABLE public.test
(
    id                   BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    test_set_id          BIGINT         NOT NULL REFERENCES public.test_set (id) ON DELETE CASCADE,

    segment              VARCHAR DEFAULT NULL,
    question             VARCHAR(16384) NOT NULL,
    ground_truth_answer  VARCHAR(16384) NOT NULL,
    ground_truth_file_id BIGINT         REFERENCES public.project_file (id) ON DELETE SET NULL
);

ALTER SEQUENCE public.test_id_seq RESTART WITH 11225;
CREATE INDEX idx_test_test_set_id ON public.test (test_set_id);


CREATE TABLE public.test_set_run
(
    id                     BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    test_set_id            BIGINT      NOT NULL REFERENCES public.test_set (id) ON DELETE CASCADE,
    status                 VARCHAR(64) NOT NULL,
    last_error             VARCHAR(512),
    processing_time_ms     BIGINT,
    evaluation_model       VARCHAR(128),
    mean_score             DOUBLE PRECISION,
    search_settings        JSONB,
    auto_evaluate          BOOLEAN     NOT NULL DEFAULT FALSE,

    finished_tests         INTEGER     NOT NULL DEFAULT 0,
    used_prompt_tokens     INTEGER     NOT NULL DEFAULT 0,
    used_completion_tokens INTEGER     NOT NULL DEFAULT 0,


    created_at             TIMESTAMP   NOT NULL,
    updated_at             TIMESTAMP   NOT NULL
);

ALTER SEQUENCE public.test_set_run_id_seq RESTART WITH 14147;
CREATE INDEX idx_test_set_run_test_set_id ON public.test_set_run (test_set_id);


CREATE TABLE public.test_run
(
    id                     BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    test_set_run_id        BIGINT    NOT NULL REFERENCES public.test_set_run (id) ON DELETE CASCADE,
    test_id                BIGINT    NOT NULL REFERENCES public.test (id) ON DELETE CASCADE,
    actual_answer          VARCHAR,
    last_error             VARCHAR(512),
    used_prompt_tokens     INTEGER   NOT NULL DEFAULT 0,
    used_completion_tokens INTEGER   NOT NULL DEFAULT 0,
    processing_time_ms     INTEGER   NOT NULL DEFAULT 0,

    created_at             TIMESTAMP NOT NULL
);

ALTER SEQUENCE public.test_run_id_seq RESTART WITH 12507;
CREATE UNIQUE INDEX idx_uniq_test_in_run ON public.test_run (test_set_run_id, test_id);
CREATE INDEX idx_test_run_test_set_run_id ON public.test_run (test_set_run_id);
CREATE INDEX idx_test_run_test_id ON public.test_run (test_id);

CREATE TABLE public.test_evaluation
(
    id                     BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    test_run_id            BIGINT        NOT NULL REFERENCES public.test_run (id) ON DELETE CASCADE,
    comment                VARCHAR(1024),
    score                  DOUBLE PRECISION,
    last_error             VARCHAR(512),
    used_prompt_tokens     INTEGER       NOT NULL DEFAULT 0,
    used_completion_tokens INTEGER       NOT NULL DEFAULT 0,
    created_at             TIMESTAMP     NOT NULL,
    updated_at             TIMESTAMP     NOT NULL
);

ALTER SEQUENCE public.test_evaluation_id_seq RESTART WITH 10555;
CREATE INDEX idx_test_evaluation_test_run_id ON public.test_evaluation (test_run_id);


