package com.justai.khub.util

import com.fasterxml.jackson.annotation.JsonAutoDetect
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.PropertyAccessor
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.KotlinModule
import org.openapitools.jackson.nullable.JsonNullableModule
import org.slf4j.LoggerFactory
import kotlin.reflect.KClass

object JSON {
    private val log = LoggerFactory.getLogger(this::class.java)

    val mapper = ObjectMapper()

    init {
        mapper.registerModule(JavaTimeModule())
        mapper.registerModule(JsonNullableModule())
        mapper.registerModule(KotlinModule.Builder().build())

        mapper.enable(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES)
        mapper.enable(JsonParser.Feature.ALLOW_NON_NUMERIC_NUMBERS)
        mapper.enable(JsonParser.Feature.ALLOW_COMMENTS)
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL)
        mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
        mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)

        mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.NONE)
        mapper.setVisibility(PropertyAccessor.FIELD, JsonAutoDetect.Visibility.ANY)
    }

    fun parse(json: String) = mapper.readTree(json)

    inline fun <reified T> parse(json: String): T =
        mapper.readValue(json, T::class.java)

    fun <T> parse(json: String, clazz: Class<T>): T =
        mapper.readValue(json, clazz)

    fun <T : Any> parse(json: String, clazz: KClass<T>): T =
        mapper.readValue(json, clazz.java)

    fun <T> parse(json: String, tr: TypeReference<T>): T =
        mapper.readValue(json, tr)

    inline fun <reified T> parse(json: JsonNode): T =
        mapper.treeToValue(json, T::class.java)

    fun <T> parse(json: JsonNode, clazz: Class<T>): T =
        mapper.treeToValue(json, clazz)

    fun parseObjectNode(json: String): ObjectNode {
        return try {
            mapper.readValue(json, ObjectNode::class.java)
        } catch (e: Exception) {
            log.error("", e)
            objectNode()
        }
    }

    fun <T> stringify(data: T): String =
        mapper.writeValueAsString(data)

    fun <T> prettyStringify(data: T): String =
        mapper.writerWithDefaultPrettyPrinter().writeValueAsString(data)

    fun toNode(data: Any): JsonNode =
        mapper.valueToTree(data)


    fun toMap(data: Any): Map<String, Any> {
        return mapper.readValue(stringify(data), object : TypeReference<Map<String, Any>>() {})
    }

    fun toObject(data: Any): ObjectNode =
        mapper.valueToTree(data)

    fun objectNode() = mapper.createObjectNode()

    fun arrayNode() = mapper.createArrayNode()

    fun Any.toObjectNode(): ObjectNode {
        try {
            if (this is String) {
                return mapper.readValue(this, ObjectNode::class.java)
            }
            return toObject(this)
        } catch (e: Exception) {
            log.error("", e)
            return objectNode()
        }
    }

    fun Any.toJsonNode(): JsonNode {
        try {
            if (this is String) {
                return mapper.readValue(this, JsonNode::class.java)
            }
            return mapper.valueToTree(this)
        } catch (e: Exception) {
            log.error("", e)
            return mapper.valueToTree<JsonNode>(this)
        }
    }

    fun <T : Any> JsonNode.toObject(kClass: KClass<T>): T {
        return parse(this, kClass.java)
    }
}

