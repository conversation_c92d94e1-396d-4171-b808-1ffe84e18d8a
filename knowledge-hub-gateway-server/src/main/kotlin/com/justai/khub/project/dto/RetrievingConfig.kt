package com.justai.khub.project.dto

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming


@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
enum class SearchStrategy {
    hybrid,
    weighted,
    threshold
}

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class FullTextSearchConfig(
    val ftSearch: Boolean = false,
    val strategy: SearchStrategy = SearchStrategy.hybrid,
    val semanticPortion: Int? = 10,
    val ftsPortion: Int? = 2,
    val threshold: Double? = 1.0,
    val useOnlyInSegment: Boolean? = false
)

enum class ImageProcessingStrategy {
    skip,
    add_parent,
    replace_to_parent
}

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class RetrievingConfig(
    val rephraseQuery: Boolean? = false,
    val queryRephrasePrompt: String? = null,

    var useHistory: Boolean? = false,
    val historyCondensePrompt: String? = null,

    val historyMaxTokens: Int? = null,
    val historyMinUserMessages: Int? = null,
    val similarityTopK: Int = 5,
    val numCandidates: Int = 50,
    val candidateRadius: Int = 0,
    val useAllEmbeddings: Boolean? = false,
    val fullTextSearch: FullTextSearchConfig? = null,
    val transformGenerativeMessage: Boolean? = true
)
