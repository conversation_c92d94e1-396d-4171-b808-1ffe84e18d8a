package com.justai.khub.report.service

import com.justai.khub.chat.enumeration.ChatHistoryRecordStatus
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.report.ReportRepository
import com.justai.khub.report.dto.ReportFilter
import com.justai.khub.report.enumeration.ReportPeriod
import com.justai.khub.report.mapper.ReportMapper.toReportEntry
import org.springframework.stereotype.Service
import java.time.LocalDate


@Service
class ReportService(
    private val csvReportGenerator: CSVReportGenerator,
    private val reportRepository: ReportRepository
) {

    fun generateReport(filter: ReportFilter): ByteArray {
        val (dateFrom, dateTo) = determineDates(filter)
        val reportEntries = reportRepository
            .findReportEntries(ChatHistoryRecordStatus.FINISHED.name, dateFrom, dateTo, filter.accountId)
            .map { toReportEntry(it) }
        return csvReportGenerator.generateReport(reportEntries)

    }

    private fun determineDates(filter: ReportFilter): Pair<LocalDate, LocalDate> {
        return when (filter.period) {
            ReportPeriod.day -> LocalDate.now() to LocalDate.now()
            ReportPeriod.week -> LocalDate.now().minusWeeks(1) to LocalDate.now()
            ReportPeriod.month -> LocalDate.now().minusMonths(1) to LocalDate.now()
            ReportPeriod.custom -> assertDatePresent("dateFrom", filter.dateFrom) to assertDatePresent("dateTo", filter.dateTo)
        }
    }

    private fun assertDatePresent(paramName: String, paramValue: LocalDate?): LocalDate {
        return paramValue ?: throw KhubException(
            ApiErrorCode.MISSING_REQUIRED_PARAMETER,
            mapOf("parameterName" to paramName, "parameterType" to "date")
        )
    }
}
