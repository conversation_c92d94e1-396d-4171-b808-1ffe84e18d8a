package com.justai.khub.report.mapper

import com.justai.khub.report.dto.ReportEntry
import java.sql.Date

object ReportMapper {

    fun toReportEntry(resultRow: Array<Any>): ReportEntry {
        if (resultRow.size != 4) {
            throw IllegalArgumentException("Unexpected report generation query. Expected 4 columns but has ${resultRow.size}")
        }
        return ReportEntry(
            (resultRow[0] as Date).toLocalDate(),
            (resultRow[1] as Number).toInt(),
            (resultRow[2] as Number).toInt(),
            (resultRow[3] as Number).toInt()
        )
    }
}
