package com.justai.khub.project.mapper

import com.justai.khub.common.configuration.dto.KHubUser
import com.justai.khub.common.configuration.dto.ModelProperties
import com.justai.khub.common.configuration.dto.RagProperties
import com.justai.khub.common.enumeration.AccountFeatures
import com.justai.khub.common.service.MessageService
import com.justai.khub.common.service.PromptsService
import com.justai.khub.project.dto.*
import org.springframework.stereotype.Component

@Component
class GenerationSettingsMapper(
    private val messageService: MessageService,
    private val promptsService: PromptsService,
    private val ragProperties: RagProperties
) {

    fun generationPartition(source: SearchPipelineConfig, currentUser: KHubUser): SettingPartition {
        return SettingPartition(
            name = "generation",
            title = LocaleObject(
                ru = messageService.getMessage("project.settings.search.response_generator.title.ru"),
                en = messageService.getMessage("project.settings.search.response_generator.title.en")
            ),
            menuTitle = LocaleObject(
                ru = messageService.getMessage("project.settings.search.response_generator.menutitle.ru"),
                en = messageService.getMessage("project.settings.search.response_generator.menutitle.en")
            ),
            description = LocaleObject(
                ru = messageService.getMessage("project.settings.search.response_generator.hint.ru"),
                en = messageService.getMessage("project.settings.search.response_generator.hint.en")
            ),
            more = MoreObject(
                label = LocaleObject(
                    ru = messageService.getMessage("project.settings.search.docs.label.ru"),
                    en = messageService.getMessage("project.settings.search.docs.label.en")
                ),
                link = LocaleObject(
                    ru = messageService.getMessage("project.settings.search.response_generator.docs.link.ru"),
                    en = messageService.getMessage("project.settings.search.response_generator.docs.link.ru")
                )
            ),
            sections = mutableMapOf(
                "main" to mainGenerationSection(source, currentUser),
                "llm" to llmSection(source.llm),
                "sources" to sourcesSection(source)
            ).apply {
                if (currentUser.hasFeature(AccountFeatures.advancedSettings)) {
                    put("advanced", advancedSection(source))
                }
            }
        )
    }

    private fun mainGenerationSection(currentValue: SearchPipelineConfig, currentUser: KHubUser): SettingsSection {
        val currentPrompt = when (currentValue) {
            is AgentSearchPipelineConfig -> currentValue.systemPrompt?.takeIf { it.isNotBlank() } ?: promptsService.agentPipelinePrompt()
            is SemanticSearchPipelineConfig -> currentValue.responseGenerator.prompt?.takeIf { it.isNotBlank() }
                ?: promptsService.responseGenerationPrompt()
        }
        return SettingsSection(
            isCard = false,
            title = null,
            description = null,
            settings = mapOf(
                "system_prompt" to PromptSettingDescription(
                    label = LocaleObject(
                        ru = messageService.getMessage("project.settings.search.response_generator.systemPrompt.label.ru"),
                        en = messageService.getMessage("project.settings.search.response_generator.systemPrompt.label.en")
                    ),
                    value = currentPrompt,
                    required = true
                ),
            )
        )
    }

    private fun sourcesSection(currentValue: SearchPipelineConfig): SettingsSection {
        return SettingsSection(
            isCard = false,
            title = null,
            settings = mapOf(
                "show_sources" to SwitchSettingDescription(
                    label = LocaleObject(
                        ru = messageService.getMessage("project.settings.search.sources.label.ru"),
                        en = messageService.getMessage("project.settings.search.sources.label.en")
                    ),
                    value = currentValue.showSources ?: false,
                    hint = LocaleObject(
                        ru = messageService.getMessage("project.settings.search.sources.hint.ru"),
                        en = messageService.getMessage("project.settings.search.sources.hint.en")
                    )
                ),
            )
        )
    }

    private fun advancedSection(source: SearchPipelineConfig): SettingsSection {
        return SettingsSection(
            isCard = false,
            title = LocaleObject(
                ru = messageService.getMessage("project.settings.advanced.title.ru"),
                en = messageService.getMessage("project.settings.advanced.title.en")
            ),
            settings = mapOf(
                "csv_processor_prompt_adjustment" to PromptSettingDescription(
                    label = LocaleObject(
                        ru = messageService.getMessage("project.settings.search.response_generator.csv_processor_prompt_adjustment.label.ru"),
                        en = messageService.getMessage("project.settings.search.response_generator.csv_processor_prompt_adjustment.label.en")
                    ),
                    hint = LocaleObject(
                        ru = messageService.getMessage("project.settings.search.response_generator.csv_processor_prompt_adjustment.hint.ru"),
                        en = messageService.getMessage("project.settings.search.response_generator.csv_processor_prompt_adjustment.hint.en")
                    ),
                    value = source.csvProcessorConfig?.promptAdjustment,
                    required = false
                )
            )
        )
    }

    private fun llmSection(currentValue: LLMConfig): SettingsSection {
        return SettingsSection(
            isCard = false,
            title = LocaleObject(
                ru = messageService.getMessage("project.settings.llm.title.ru"),
                en = messageService.getMessage("project.settings.llm.title.en")
            ),
            settings = mapOf(
                "model" to SelectSettingDescription(
                    required = true,
                    value = currentValue.model ?: ragProperties.llm.models.first().value,
                    label = LocaleObject(
                        ru = messageService.getMessage("project.settings.llm.model.label.ru"),
                        en = messageService.getMessage("project.settings.llm.model.label.en")
                    ),
                    options = ragProperties.llm.models.map { it.toOption() }
                ),
                "context_window" to SliderSettingDescription(
                    format = NumberFormat.int,
                    label = LocaleObject(
                        ru = messageService.getMessage("project.settings.llm.contextWindow.label.ru"),
                        en = messageService.getMessage("project.settings.llm.contextWindow.label.en")
                    ),
                    value = currentValue.contextWindow.toDouble(),
                    min = 4000.0,
                    max = 128000.0,
                    step = 800.0,
                    visibleStep = 24800.0,
                    hint = LocaleObject(
                        ru = messageService.getMessage("project.settings.llm.contextWindow.hint.ru"),
                        en = messageService.getMessage("project.settings.llm.contextWindow.hint.en")
                    )
                ),
                "max_tokens" to SliderSettingDescription(
                    format = NumberFormat.int,
                    label = LocaleObject(
                        ru = messageService.getMessage("project.settings.llm.maxTokens.label.ru"),
                        en = messageService.getMessage("project.settings.llm.maxTokens.label.en")
                    ),
                    value = currentValue.maxTokens.toDouble(),
                    min = 500.0,
                    max = 16000.0,
                    step = 250.0,
                    visibleStep = 2000.0,
                    hint = LocaleObject(
                        ru = messageService.getMessage("project.settings.llm.maxTokens.hint.ru"),
                        en = messageService.getMessage("project.settings.llm.maxTokens.hint.en")
                    )
                ),
                "temperature" to SliderSettingDescription(
                    format = NumberFormat.double,
                    label = LocaleObject(
                        ru = messageService.getMessage("project.settings.llm.temperature.label.ru"),
                        en = messageService.getMessage("project.settings.llm.temperature.label.en")
                    ),
                    value = currentValue.temperature,
                    min = 0.0,
                    max = 1.0,
                    step = 0.01,
                    visibleStep = 0.25,
                    hint = LocaleObject(
                        ru = messageService.getMessage("project.settings.llm.temperature.hint.ru"),
                        en = messageService.getMessage("project.settings.llm.temperature.hint.en")
                    )
                ),
                "extended" to AccordionSettingDescription(
                    label = LocaleObject(
                        ru = messageService.getMessage("project.settings.llm.titleextended.ru"),
                        en = messageService.getMessage("project.settings.llm.titleextended.en")
                    ),
                    nestedSettings = mapOf(
                        "top_p" to SliderSettingDescription(
                            format = NumberFormat.double,
                            label = LocaleObject(
                                ru = messageService.getMessage("project.settings.llm.topP.label.ru"),
                                en = messageService.getMessage("project.settings.llm.topP.label.en")
                            ),
                            value = currentValue.topP,
                            min = 0.0,
                            max = 1.0,
                            step = 0.01,
                            visibleStep = 0.25,
                            hint = LocaleObject(
                                ru = messageService.getMessage("project.settings.llm.topP.label.ru"),
                                en = messageService.getMessage("project.settings.llm.topP.label.en")
                            )
                        ),
                        "presence_penalty" to SliderSettingDescription(
                            format = NumberFormat.double,
                            label = LocaleObject(
                                ru = messageService.getMessage("project.settings.llm.presencePenalty.label.ru"),
                                en = messageService.getMessage("project.settings.llm.presencePenalty.label.en")
                            ),
                            value = currentValue.presencePenalty,
                            min = -2.0,
                            max = 2.0,
                            step = 0.01,
                            visibleStep = 0.5,
                            hint = LocaleObject(
                                ru = messageService.getMessage("project.settings.llm.presencePenalty.hint.ru"),
                                en = messageService.getMessage("project.settings.llm.presencePenalty.hint.en")
                            )
                        ),
                        "frequency_penalty" to SliderSettingDescription(
                            format = NumberFormat.double,
                            label = LocaleObject(
                                ru = messageService.getMessage("project.settings.llm.frequencyPenalty.label.ru"),
                                en = messageService.getMessage("project.settings.llm.frequencyPenalty.label.en")
                            ),
                            value = currentValue.frequencyPenalty,
                            min = -2.0,
                            max = 2.0,
                            step = 0.01,
                            visibleStep = 0.5,
                            hint = LocaleObject(
                                ru = messageService.getMessage("project.settings.llm.frequencyPenalty.hint.ru"),
                                en = messageService.getMessage("project.settings.llm.frequencyPenalty.hint.en")
                            )
                        ),
                    )
                )
            )
        )
    }

    private fun ModelProperties.toOption(): Option {
        return Option(
            title = LocaleObject(
                ru = this.title,
                en = this.title
            ),
            value = this.value,
            dependsOn = if (this.pipelineTypes.isNotEmpty()) {
                listOf(DependencyConfig("search.main.type", this.pipelineTypes))
            } else emptyList()
        )
    }
}
