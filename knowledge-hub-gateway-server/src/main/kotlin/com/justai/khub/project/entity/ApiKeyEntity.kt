package com.justai.khub.project.entity

import com.justai.khub.common.entity.AuditableEntity
import com.justai.khub.user.enumeration.ApiKeyStatus
import jakarta.persistence.*
import java.time.LocalDateTime

@Entity
@Table(name = "api_key")
class ApiKeyEntity : AuditableEntity() {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = -1

    @Column(nullable = false)
    lateinit var name: String

    @Column(nullable = false)
    lateinit var prefix: String

    @Column(nullable = false)
    lateinit var hash: String

    @Column(nullable = false)
    lateinit var ownerLogin: String

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    lateinit var status: ApiKeyStatus

    @Column
    var expiredAt: LocalDateTime? = null

    @Column
    var lastUsedAt: LocalDateTime? = null

    @ManyToOne
    var project: ProjectEntity? = null

    @Column(nullable = true)
    var value: String? = null
}
