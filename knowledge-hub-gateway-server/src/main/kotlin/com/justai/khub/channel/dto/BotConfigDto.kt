package com.justai.khub.channel.dto

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(PropertyNamingStrategies.LowerCamelCaseStrategy::class)
data class DeploymentResult(
    val id: Long,
    val status: String? = null
)

@JsonNaming(PropertyNamingStrategies.LowerCamelCaseStrategy::class)
data class BotConfigDto(
    val id: Long,
    val channelType: String,
    val botserverBotId: String?,
    val removed: Boolean?,
    val blocked: Boolean?,
    val lastDeployResult: DeploymentResult?,
    val description: String?,
    var externalLink: String? = null,
    var actualStatus: String? = null
)
