@Library('justai-common') _

mavenBuild(
    [
        jobParams: [
            booleanParam: [SKIP_IT_TESTS: true],
        ],
        sonarEnabled: true,
        lambdaStages: [
            "Build":
            {
                withMaven(maven: 'Maven 3', jdk: '17'){
                    sh "echo ${BRANCH_NAME}-${TRUNK_ID}-${BUILD_TIMESTAMP} > knowledge-hub-gateway-server/src/main/conf/version.txt"
                    sh "mvn clean deploy -pl knowledge-hub-gateway-server,knowledge-hub-gateway-client -U -e -P docker -DskipITs=${params.SKIP_IT_TESTS} -Dbranch.name=${BRANCH_NAME} -Dbuild.number=${TRUNK_ID} -Dbuild.date=${BUILD_TIMESTAMP} -DuseRedHatBaseImage=false -DskipDocker=false"
                }
            }
        ]
    ])
