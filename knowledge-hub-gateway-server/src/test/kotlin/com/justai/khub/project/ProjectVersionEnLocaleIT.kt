package com.justai.khub.project

import com.justai.khub.BaseProjectIT
import com.justai.khub.project.dto.CardsSettingDescription
import com.justai.khub.project.dto.ChunkerConfig
import com.justai.khub.project.dto.Language
import com.justai.khub.project.mapper.ProjectSettingsMapper
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.TestPropertySource

@TestPropertySource(properties = ["spring.messages.default-locale=en"])
class ProjectVersionEnLocaleIT : BaseProjectIT() {

    @Autowired
    private lateinit var projectSettingsMapper: ProjectSettingsMapper


    @Test
    fun testOnlyEngLanguageInDefaultVersionSettings() {
        val initialSettings = projectHelper.getDefaultVersionSettingsAndExpectOk(project.id)
        assertEquals(3, initialSettings.partitions.size)
        val defaultSettings = projectSettingsMapper.defaultSettings("unused")
        val settingsUpdateRequest = defaultSettings.copy(
            indexation = defaultSettings.indexation?.copy(
                chunker = ChunkerConfig(chunkSize = 1234)
            ),
            search = defaultSettings.search.copy(
                retrieving = defaultSettings.search.retrieving.copy(similarityTopK = 12)
            )
        )

        projectHelper.setDefaultVersionSettings(project.id, settingsUpdateRequest)

        val updatedSettings = projectHelper.getDefaultVersionSettingsAndExpectOk(project.id)
        val languageSetting = updatedSettings.partitions.firstOrNull { it.name == "indexation" }
            ?.sections?.getOrDefault("chunker", null)?.settings?.getOrDefault("language", null) as? CardsSettingDescription
        assertNull(languageSetting?.options?.firstOrNull() { it.value == Language.russian.toString() })
        assertNotNull(languageSetting?.options?.firstOrNull() { it.value == Language.english.toString() })
    }

}
