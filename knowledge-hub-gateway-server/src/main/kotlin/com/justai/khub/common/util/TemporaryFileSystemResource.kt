package com.justai.khub.common.util

import org.apache.commons.io.FileUtils
import org.springframework.core.io.FileSystemResource
import java.io.Closeable
import java.io.FilterInputStream
import java.io.InputStream
import java.nio.ByteBuffer
import java.nio.channels.ReadableByteChannel
import java.nio.file.Path

class TemporaryFileSystemResource(path: Path) : FileSystemResource(path) {

    override fun readableChannel(): ReadableByteChannel {
        val readableChannel: ReadableByteChannel = super.readableChannel()
        return object : ReadableByteChannel {
            override fun close() {
                closeThenDeleteFile(readableChannel)
            }

            override fun isOpen(): Boolean {
                return readableChannel.isOpen
            }

            override fun read(dst: ByteBuffer?): Int {
                return readableChannel.read(dst)
            }
        }
    }

    override fun getInputStream(): InputStream {
        return object : FilterInputStream(super.getInputStream()) {
            override fun close() {
                closeThenDeleteFile(this.`in`)
            }
        }
    }

    private fun closeThenDeleteFile(closeable: Closeable) {
        try {
            closeable.close()
        } finally {
            deleteFile()
        }
    }

    private fun deleteFile() {
        FileUtils.deleteQuietly(file)
    }

    override fun isFile(): Boolean {
        // Prevent zero-copy so we can delete the file on close
        return false
    }
}
