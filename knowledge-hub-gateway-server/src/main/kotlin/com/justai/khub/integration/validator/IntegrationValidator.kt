package com.justai.khub.integration.validator

import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.integration.dto.CloudConfluenceIntegrationSettings
import com.justai.khub.integration.dto.ConfluenceIntegrationSettings
import com.justai.khub.integration.dto.MinervaIntegrationSettings
import com.justai.khub.integration.entity.IntegrationEntity
import org.springframework.stereotype.Service
import kotlin.reflect.KProperty

@Service
class IntegrationValidator {

    fun validate(integration: IntegrationEntity) {
        checkNotBlank(integration::name, integration.name)
        when (val settings = integration.settings) {
            is ConfluenceIntegrationSettings -> validateConfluenceSettings(settings)
            is CloudConfluenceIntegrationSettings -> validateCloudConfluenceSettings(settings)
            is MinervaIntegrationSettings -> validateMinervaSettings(settings)
        }
    }

    private fun validateConfluenceSettings(settings: ConfluenceIntegrationSettings) {
        checkNotBlank(settings::baseUrl, settings.baseUrl)
        checkNotBlank(settings::space, settings.space)
        checkNotBlank(settings::token, settings.token)
    }

    private fun validateCloudConfluenceSettings(settings: CloudConfluenceIntegrationSettings) {
        checkNotBlank(settings::space, settings.space)
        checkNotBlank(settings::siteUrl, settings.siteUrl)
    }

    private fun validateMinervaSettings(settings: MinervaIntegrationSettings) {
        checkNotBlank(settings::baseUrl, settings.baseUrl)
        checkNotBlank(settings::spaceId, settings.spaceId)
        checkNotBlank(settings::token, settings.token)
    }

    private fun checkNotBlank(property: KProperty<String>, propertyValue: String) {
        if (propertyValue.isBlank()) {
            throw KhubException(ApiErrorCode.MISSING_REQUIRED_PARAMETER, mapOf("parameterName" to property.name, "parameterType" to "string"))
        }
    }
}
