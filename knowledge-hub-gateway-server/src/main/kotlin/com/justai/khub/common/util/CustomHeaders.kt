package com.justai.khub.common.util

object CustomHeaders {
    const val REQUEST_ID = "Z-requestId"

    const val X_LICENSE_RATE_LIMIT_REMAINING = "X-License-Rate-Limit-Remaining"
    const val X_LICENSE_RATE_LIMIT_LIMIT = "X-License-Rate-Limit-Limit"
    const val X_LICENSE_RATE_LIMIT_RESET_MS = "X-License-Rate-Limit-Reset-Ms"
    const val X_LICENSE_RETRY_AFTER_MS = "X-License-Retry-After-Ms"

    const val X_LICENSE_TIMESTAMP = "X-LICENSE-TIMESTAMP"
    const val X_LICENSE_SIGNATURE = "X-LICENSE-SIGNATURE"
    const val X_LICENSE_KEY_ID = "X-LICENSE-KEY-ID"
    const val X_LICENSE_REQUEST_ID = "X-LICENSE-REQUEST-ID"
}
