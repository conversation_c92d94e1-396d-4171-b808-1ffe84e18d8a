package com.justai.khub.common.configuration.service

import com.justai.khub.common.configuration.dto.LoggingProperties
import com.justai.khub.common.enumeration.IntegrationName
import org.apache.commons.io.IOUtils
import org.slf4j.LoggerFactory
import org.springframework.http.HttpRequest
import org.springframework.http.client.ClientHttpRequestExecution
import org.springframework.http.client.ClientHttpRequestInterceptor
import org.springframework.http.client.ClientHttpResponse
import java.nio.charset.StandardCharsets
import kotlin.math.min

class LoggingInterceptor(
    private val integrationName: IntegrationName?,
    private val loggingProperties: LoggingProperties,
    private val ignoredRoutes: List<Regex> = listOf()
) : ClientHttpRequestInterceptor {
    private val log = LoggerFactory.getLogger(this::class.java)

    override fun intercept(
        httpRequest: HttpRequest,
        bytes: ByteArray,
        clientHttpRequestExecution: ClientHttpRequestExecution
    ): ClientHttpResponse {
        if (ignoredRoutes.any {it.matches(httpRequest.uri.path.toString())}) {
            return clientHttpRequestExecution.execute(httpRequest, bytes)
        }
        var requestLog = "${requestLogPrefix()}${httpRequest.method.name()} ${httpRequest.uri}"
        getLoggableRequestBody(httpRequest, bytes)?.let { requestLog += "\n\tRequest body: $it" }
        log.info(requestLog)
        val startedAt = System.currentTimeMillis()
        val response = clientHttpRequestExecution.execute(httpRequest, bytes)
        val processingTime = System.currentTimeMillis() - startedAt
        var responseLog = "${responseLogPrefix()}${httpRequest.method.name()} ${httpRequest.uri}" +
            "\n\tstatus = ${response.statusCode} ${response.statusText}" +
            "\n\tprocessing time = ${processingTime}ms"
        getLoggableResponseBody(response)?.let { responseLog += "\n\tResponse body: $it" }
        log.info(responseLog)
        return response
    }

    private fun getLoggableRequestBody(httpRequest: HttpRequest, requestBody: ByteArray): String? {
        if (requestBody.isEmpty()) return null
        if (httpRequest.headers.contentType?.subtype != "json") return null

        return String(requestBody).let {
            it.substring(0, min(it.length, loggingProperties.loggableBodySize))
        }
    }

    private fun getLoggableResponseBody(response: ClientHttpResponse): String? {
        if (response.headers.contentType?.subtype != "json") return null

        return IOUtils.toString(response.body, StandardCharsets.UTF_8)?.let {
            it.substring(0, min(it.length, loggingProperties.loggableBodySize))
        }
    }

    private fun requestLogPrefix(): String{
        return if (integrationName == null) "" else "Request to ${integrationName}: "
    }

    private fun responseLogPrefix(): String{
        return if (integrationName == null) "" else "Response from ${integrationName}: "
    }
}
