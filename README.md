# knowledge-hub-gateway

## Общее описание

Knowledge Hub Gateway — это входной сервис в платформе knowledge hub. Основная функциональность:
- хранение данных по проектам пользовательских баз знаний
- управление обучением баз знаний
- интеграция с customer-services, такими как confluence
- интеграции с другими компонентами платформу knowledge-hub

Предоставляет api двух типов:

- внутренний api - для взаимодействия frontend-backend и системными службами (e2e)
- внешний (публичный) api - для получения информации по проекту БЗ, отправки запросов к БЗ и управлению чатами

## Место в системе

### Используемые сервисы
- knowledge-hub-ingester
- knowledge-hub-parser
- knowledge-hub-rag
- knowledge-hub-evaluator

### Сервисы, которые используют knowledge-hub-gateway
- knowledge-hub-fe
- accounts-admin

## Техническая спецификация

Knowledge Hub Gateway представляет собой веб-сервис, написанный на Kotlin с использованием библиотеки Spring Boot.
Основное хранилище — PostgreSQL. Взаимодействие с другими сервисами (такими как RAG сервис) осуществляется через REST.
Хранилище для пользовательских сессий - MongoDB.
VCS для пользовательских репозиториев баз знаний - LakeFS.

### API
- Протокол: HTTP.
- Порт по умолчанию: 9333.
- Авторизация:
  - Служебные запросы: Basic Auth.
  - Пользовательские запросы: токен проекта базы знаний в заголовке Authorization (Bearer).
  - Запросы frontend: сессионный ключ в заголовке Authorization (Bearer).

### Ссылки (для внутреннего использования)

* Репозиторий: https://gitlab.just-ai.com/khub/knowledge-hub-gateway
* Jenkins джобы: https://jenkins.just-ai.com/job/KHUB/job/Knowledge%20Hub%20Gateway/
* Конфигурация: Управление конфигурацией осуществляется через `cm-configs`
* IT тесты - https://jenkins.just-ai.com/job/KHUB/job/Knowledge%20Hub%20Gateway/job/Knowledge%20Hub%20Gateway-it/
* BBT тесты - https://jenkins.just-ai.com/job/KHUB/job/Knowledge%20Hub%20Gateway/job/Knowledge%20Hub%20Gateway-bbt/

## Термины и сущности

- `Project` - версионированный набор источников знаний
- `ProjectVersion` - набор источников знаний
- `Chat` - Сеанс взаимодействия пользователя с версией проекта
- `IngestionJob` - Процесс загрузки нового источника знаний в базу

## Место в общей архитектуре

![hld.png](docs/hld.png)

## Конфигурация
```yaml
server:
  address: 0.0.0.0
  port: ${port:9333} # порт сервера.
  hostname: khub.just-ai.com # hostname на котором запускается платформа knowledge-hub.
  shutdown: graceful
  username: # системное имя пользователя
  password: # системный пароль пользователя

spring:
  # настройки spring
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  jackson:
    deserialization:
      fail-on-unknown-properties: false
  main:
    banner-mode: off
  application:
    name: knowledge-hub-gateway
  lifecycle:
    timeout-per-shutdown-phase: 100s
  pid:
    file: /opt/justai/knowledge-hub-gateway-be/bin/${id:application}.pid
    fail-on-write-error: true
  mvc:
    throw-exception-if-no-handler-found: true
  session:
    store-type: none
  messages:
    basename: messages/messages
    encoding: UTF-8
    default-locale: ru
    cache-duration: -1
  cloud:
    httpclientfactories:
      ok:
        enabled: false
    loadbalancer:
      justai:
        enabled: false
      enabled: false
      retry:
        enabled: true
      ai-vendor:
        balancing-strategy: round-robin
        retry:
          enabled: true
          safe-calls:
            methods: [ 'GET', 'DELETE' ]
            always-retry: false
            retryable-status-codes: [ 502, 503, 504 ]
            retry-on-exception: false
          not-safe-calls:
            always-retry: false
            retryable-status-codes: [ 502, 503, 504 ]
            retry-on-exception: false
      # настройки балансировки нагрузки по клиентам
      clients:
        knowledge-hub-rag:
          configurations: ai-vendor
          ai-vendor:
            balancing-strategy: round-robin
        knowledge-hub-parser:
          configurations: ai-vendor
          ai-vendor:
            balancing-strategy: round-robin
        knowledge-hub-ingester:
          configurations: ai-vendor
          ai-vendor:
            balancing-strategy: round-robin
        accountsadmin:
          configurations: ai-vendor
          ai-vendor:
            balancing-strategy: round-robin

  devtools:
    livereload:
      enabled: false
  # настройки соединения с базой данных postgresql
  datasource:
    url: *******************************************
    username:
    password:
    driver-class-name: org.postgresql.Driver
    validationQuery: SELECT 1
    test-on-borrow: true
    remove-abandoned: true
    remove-abandoned-timeout: 60
    test-while-idle: true
    hikari:
      maximumPoolSize: 50
      connectionTestQuery: SELECT 1
      maxLifeTime: 120000
      leakDetectionThreshold: 60000
  jpa:
    open-in-view: false
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: none
    generate-ddl: false
    properties:
      hibernate:
        types:
          jackson:
            object:
              mapper: com.justai.khub.common.util.HibernateObjectMapperSupplier
        enable_lazy_load_no_trans: true
      javax:
        persistence:
          validation:
            mode: none

resilience4j:
  # конфигурация политики повторных запросов к используемым сервисам
  retry:
    configs:
      knowledge-hub-rag:
        max-attempts: 2 # максимальное кол-во попыток
        enable-exponential-backoff: true # флаг, указывающий на необходимость прогрессивного ожидания между повторными запросами
        exponential-backoff-multiplier: 2
        enable-randomized-wait: true # флаг, указывающий на рандомизацию периода ожидания между повторными запросами
        wait-duration: 20ms # 20ms +- 10ms, 40ms +- 20ms
      knowledge-hub-ingester:
        base-config: knowledge-hub-rag # ссылка на описание конфигурации политики повторных запросов
      knowledge-hub-parser:
        base-config: knowledge-hub-rag
      knowledge-hub-evaluator:
        base-config: knowledge-hub-rag
      accountsadmin:
        base-config: knowledge-hub-rag

session-db:
  host: localhost
  database: justaisessions
  username:
  password:

# настройки хранилища mongodb пользовательских сессий
just-session:
  mongodb:
    uri: mongodb://localhost:27017/justaisessions
  account-user-compatibility: sessionInfo
  required-features:

eureka:
  client:
    enabled: false
    service-url:
      defaultZone: # Eureaka SD-endpoint url, for example http://localhost:9290/eureka
  instance:
    instance-id: khg01:local-${spring.application.name}:${server.port}
    statusPageUrlPath: /version
    healthCheckUrlPath: /version

management:
  # настройки метрик
  metrics:
    distribution:
      slo:
        http.server.requests:
          - 200ms
          - 500ms
          - 1s
          - 10s
  endpoints:
    web:
      exposure:
        include: "*"

# настройки логирования
logging:
  loggable-body-size: 2048 # Number of request/response body characters that will be printed.
  file:
    name: ${log.dir:/tmp}/knowledge-hub-gateway-be.main.log
    max-history: 90
    max-size: 100MB

# настройки для включенных фичей на платформе в рамках инстанса
local-features:
  # разрешить фичу ретривинга чанков в диалоговом окне БЗ
  chunksRetrieving: false
  # разрешить фичу интеграции с внешними каналами, такими как jaicp
  channels: false

# настройки интеграции с внешними сервисами
integrations:
  checkIntegrationsJobPoolSize: 5 # размер пула проверок обновления внешних интеграций (применимо для confluence)
  # настройки интеграции с lakefs
  lakefs:
    base-url: http://localhost:8009/api/v1
    access-key-id: TODO
    secret-access-key: TODO
    storage-namespace: TODO
    connectTimeout: 60000
    readTimeout: 120000
  # настройки интеграции с rag-сервисом
  rag:
    eureka-enabled: ${eureka.client.enabled}
    base-url: http://localhost:3010
    username:
    password:
  # настройки интеграции с accounts-admin
  aa:
    eureka-enabled: ${eureka.client.enabled}
    base-url: http://localhost:9140
    username:
    password:
  # настройки интеграции с сервисом чанкинга и создания эмбеддингов
  ingester:
    eureka-enabled: ${eureka.client.enabled}
    base-url: http://localhost:3040
    username:
    password:
  # настройки интеграции с сервисом распознавания документов
  parser:
    eureka-enabled: ${eureka.client.enabled}
    base-url: http://localhost:3030
    username:
    password:
  # настройки интеграции с customer confluence
  confluence:
    maxPages: 1000
    checkIntervalMinutes: 1
  # настройки интеграции с jaicp-платформой
  jaicp:
    baseUrl: https://app.jaicp.com


# настройки внутреннего http-клиента (для внутренних интеграций)
internal-http-client:
  connectTimeout: 10_000
  readTimeout: 1200_000
  maxConnections: 800
  maxConnectionsPerRoute: 100
  connTimeToLive: 0
  validateAfterInactivity: 500
  disableSslVerifier: false
  trustStore:
  trustStorePassword:
  trustStoreType:

# настройки внещнего http-клиента (для внешних интеграций)
external-http-client:
  connectTimeout: 10_000
  readTimeout: 120_000
  maxConnections: 800
  maxConnectionsPerRoute: 100
  connTimeToLive: 0
  validateAfterInactivity: 500
  disableSslVerifier: false
  trustStore:
  trustStorePassword:
  trustStoreType:

# настройки для публичных апи-ключей
api-keys:
  key-length: 60 # длина ключа
  visible-key-length: 10 # отображаемая длина ключа на UI
  # параметры примеров запросов для использования публичного апи
  request-samples:
    - content: |
        curl -H "Authorization: Bearer ${api_key}"
        https://${server.hostname}/api/knowledge-hub/info
      hint: 'apiKeys.request.sample.projectInfo.hint.ru'
    - content: |
        curl -d '{"query": "Что ты умеешь?"}'
        -H "Authorization: Bearer ${api_key}"
        -H "Content-Type: application/json"
        https://${server.hostname}/api/knowledge-hub/query
      hint: 'apiKeys.request.sample.generateQuery.hint.ru'

# настройки для индексации проектов баз знаний
ingest:
  processImages: true # включить обработку графики в документах
  enrichChunks: false # включить обогащение чанков. Чанки, сохраняемые в векторное хранилище, будут содержать контрольные вопросы, сгенерированные через llm, и саммаризацию.
  ingestJobThreadPoolSize: 5 # размер тред-пула для джобов индексации
  fileProcessingThreadPoolSize: 4 # размер тред-пула для парсинга файлов
  parser:
    parsingTimeoutSeconds: 1200 # таймаут ожидания результатов парсинга
  chunker:
    llmChunkerTotalCharsLimit: 500_000 # лимит символов при использовании чанкинга через llm-чанкер
  # настройки векторизатора
  vectorizer:
    options:
      - title: "text_embedding_3_large"
        value: "openai/text-embedding-3-large"
        img: "/static/images/openai.svg"
      - title: "multilingual_e5_large"
        value: "intfloat/multilingual-e5-large"
        img: "/static/images/internal-ner-model.svg"

# настройки умного поиска
rag:
  llm:
    # доступные llm-модели
    models:
      - title: "GPT-4o"
        value: "openai/gpt-4o"
      - title: "GPT-4o-mini"
        value: "openai/gpt-4o-mini"
      - title: "qwen2-72b-awq"
        value: "vllm-qwen2-72b-awq"
        pipelineTypes:
          - "semantic"
      - title: "llama3.1-8b"
        value: "vllm-llama3.1-8b"
        pipelineTypes:
          - "semantic"
  # настройки реранкера
  reranker:
    model:
      enabled: true
  imageSearchStrategy: add_parent # стратегия поиска чанков изображений

billing:
  enabled: true # включение khub-биллинга

attachments:
  enabled: true # включение выгрузки вложений при парсинге документов
  public-link-duration: "2h" # supported suffixes - ns, us, ms, s, m, h, d

jobs:
  timedOutEntitiesCheckIntervalSeconds: 7200
  entitiesProcessingTimeout: "2h"

```
## Описание работы

### Управление проектами

Пользователи могут создавать, получать, обновлять и удалять проекты. Каждый проект может иметь несколько версий и связанных файлов.

### Обработка файлов

Сервис позволяет загружать, скачивать и управлять файлами, связанными с проектами. Файлы хранятся с использованием LakeFS и привязываются к конкретной версии
проекта.

### Запросы к БЗ

Пользователи могут отправлять запросы в бз и получать ответы. Чат привязан к определенной версии проекта.

### Управление пользователями

Используется стандартная аутентификация \ авторизация с помощью conversation cloud

### Добавление файлов в бз

При загрузке файлы привязываются к версии проекта и сохраняются в хранилище.


### Интеграции customer-services в бз

Пользователи БЗ могут добавить внешнюю интеграцию данных в проект. Поддержаны следующие внешние интеграции данных:
- confluence

### Оценка качества проета базы знаний

Пользователи могут загружать проверочные тест-сеты в проект бз и на их основе проводить оценку качетсва ответов на пользовательские запросы.

## Сохранение данных

![db schema](docs/db_schema.svg)

## Метрики

| Код метрики                                        | Тип        | Описание                                                      | Теги                  |
|:---------------------------------------------------|:-----------|:--------------------------------------------------------------|:----------------------|
| postgres.requests                                  | timer      | Время запроса к базе данных postgresql                        | class, method         |
| integration_${integration_name}                    | timer      | Продолжительность работы api-соединения внешней интеграции    | method                |
| integration_${integration_name}_errors             | counter    | Кол-во ошибок в соединении внешних интеграций                 | method                |
| process_chat_query                                 | timer      | Время обработки чат-запроса к БЗ                              |                       |
| chat_query_processor_errors                        | counter    | Кол-во ошибок в обработке чат-запросов                        |                       |
| apache_http_pool_available_connections             | gauge      | Число актуальных доступных http-соединений                    | http_pool             |
| apache_http_pool_leased_connections                | gauge      | Число перманентно используемых http-соединений                | http_pool             |
| apache_http_pool_pending_connections               | gauge      | Число http-соединений, в состоянии ожидания                   | http_pool             |
| apache_http_pool_max_connections                   | gauge      | Максимальное число http-соединений, доступных в данный момент | http_pool             |
| ingest_job_waiting_time                            | timer      | Время ожидания задания на индексацию                          |              |
| ingest_version                                     | timer      | Время индексации                                              |              |
| ingest_version_errors                              | counter    | Кол-во ошибок индексации                                      |              |
| ingest_file_waiting_time                           | timer      | Время ожидания индексации файла                               |              |
| ingest_file                                        | timer      | Время индексации файла                                        |              |
| ingest_file_errors                                 | counter    | Кол-во ошибок индексации файла                                |              |
| integration_check                                  | timer      | Время проверки внешней интеграции                             |              |
| integration_check_errors                           | counter    | Кол-во ошибок проверки внешней интеграции                                      |


## Логи

В качестве движка логов используется Logback. Тег сервиса для фильтрации - `knowledge-hub-gateway`

Настройка логирования происходит в нескольких местах:

### При сборке проекта

**logback.xml** - указывает настройки, специфичные для сервиса

**default.xml** - включен в logback.xml, лежит в infrastructure-starter, содержит параметры, общие для всех сервисов (профили, file-appender)

**gelf.xml** - включен в default.xml, лежит в infrastructure-starter, содержит настройки для отправки логов в Graylog

### Во время выполнения

**logback.xml** - может полностью переопределить всю конфигурацию

**logback-includes.xml** - включен в logback.xml, может добавить конфигурацию для конкретного экземпляра - предпочтительный метод

**File-appender** и **gelf-appender** используются в качестве production аппендеров (когда профили dev, test, migration отключены)

File-appender - настраивается в application.yml под logging.file, указывает имя лога и настройки ротации.

Gelf-appender - основные настройки находятся в gelf.xml, GRAYLOG_APP переопределяется в logback.xml для определения имени приложения в graylog. GRAYLOG_SERVER и
GRAYLOG_ENV указываются в logback-includes.xml, так как они специфичны для каждого экземпляра.

Обновление конфигурации логов - логи могут быть обновлены на лету путем внесения изменений в logback.xml или logback-includes.xml. С autoscan, они будут
применены в течение 10 секунд и новая конфигурация начнет работать.
