package com.justai.khub.common

import com.justai.khub.BaseProjectIT
import com.justai.khub.util.AuthUtils
import com.justai.security.session.auth.JustSessionAuthentication
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

class EntitiesAccessIT : BaseProjectIT() {

    private lateinit var notOwner: JustSessionAuthentication

    @BeforeEach
    fun init() {
        notOwner = AuthUtils.mockCCUser(ccAccountId = -1)
    }

    @Test
    fun testNoAccessToNotOwnedProjects() {
        projectHelper.getProject(project.id, ccAuth = notOwner).andExpect(status().isForbidden)
        projectHelper.getDefaultVersionSettings(project.id, ccAuth = notOwner).andExpect(status().isForbidden)
        projectHelper.deleteProject(project.id, ccAuth = notOwner).andExpect(status().isForbidden)
    }

    @Test
    fun testNoAccessToNotOwnedIntegrations() {
        val integration = integrationHelper.createIntegration(project.id)
        integrationHelper.getIntegration(project.id, integration.id, ccAuth = notOwner).andExpect(status().isForbidden)
        integrationHelper.updateIntegration(project.id, integration.id, ccAuth = notOwner).andExpect(status().isForbidden)
        integrationHelper.deleteIntegration(project.id, integration.id, ccAuth = notOwner).andExpect(status().isForbidden)
    }

    @Test
    fun testNoAccessToNotOwnedChats() {
        val chat = chatHelper.createChat(project.id)
        chatHelper.getChatHistory(project.id, chat.id, ccAuth = notOwner).andExpect(status().isForbidden)
    }

    @Test
    fun testNoAccessToNotOwnedIngestJobs() {
        ingestHelper.startIngest(project.id)
        ingestHelper.cancelIngest(project.id, ccAuth = notOwner).andExpect(status().isForbidden)
    }

    @Test
    fun testNoAccessToNotOwnedApiKeys() {
        val apiKey = apiKeyHelper.createApiKey(project.id)
        apiKeyHelper.revokeApiKey(apiKey.id, project.id, ccAuth = notOwner).andExpect(status().isForbidden)
    }

    @Test
    fun testNoAccessToNotOwnedTestSets() {
        val testSet = qaHelper.uploadTestSetAndExpectOk(project.id)
        qaHelper.getTestSet(project.id, testSet.id, ccAuth = notOwner).andExpect(status().isForbidden)
        qaHelper.runTestSet(project.id, testSet.id, ccAuth = notOwner).andExpect(status().isForbidden)
    }
}
