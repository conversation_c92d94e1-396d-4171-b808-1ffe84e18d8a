package com.justai.khub.common.configuration.service

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.ObjectNode
import com.justai.khub.common.dto.ApiError
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.enumeration.ApiErrorCode.Companion.SOURCE_ERROR_CODE_ARG
import com.justai.khub.common.enumeration.LoggingLevel
import com.justai.khub.common.exception.AttachmentNotFoundException
import com.justai.khub.common.exception.KhubException
import com.justai.khub.common.util.JSON
import com.justai.khub.common.util.LoggingUtils
import jakarta.servlet.http.HttpServletResponse
import jakarta.validation.ConstraintViolationException
import org.apache.commons.lang3.text.StrSubstitutor
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.*
import org.springframework.http.converter.HttpMessageNotReadableException
import org.springframework.security.access.AccessDeniedException
import org.springframework.security.authentication.InsufficientAuthenticationException
import org.springframework.security.core.AuthenticationException
import org.springframework.security.web.firewall.RequestRejectedException
import org.springframework.web.HttpMediaTypeNotSupportedException
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.MissingServletRequestParameterException
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.client.RestClientException
import org.springframework.web.context.request.WebRequest
import org.springframework.web.servlet.NoHandlerFoundException
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler
import java.net.URI
import java.util.concurrent.CompletionException


@ControllerAdvice
class ApiErrorHandler( @Value("\${spring.messages.default-locale}") private val defaultLocale: String,
                       @Value("\${server.hostname}") private val hostname: String,) : ResponseEntityExceptionHandler() {

    @ExceptionHandler(AccessDeniedException::class)
    fun handleAccessDeniedException(ex: AccessDeniedException) = createResponseEntity(ex)

    @ExceptionHandler(KhubException::class)
    fun handleWebException(ex: KhubException) = createResponseEntity(ex)

    @ExceptionHandler(RuntimeException::class)
    fun handleRuntimeException(ex: RuntimeException) = createResponseEntity(ex)

    @ExceptionHandler(ConstraintViolationException::class)
    fun handleConstraintViolationException(ex: ConstraintViolationException) = createResponseEntity(ex)

    @ExceptionHandler(AttachmentNotFoundException::class)
    fun handleAttachmentNotFoundException(ex: AttachmentNotFoundException) = createPublicResourceNotFound(ex, defaultLocale, hostname)

    @ExceptionHandler(Throwable::class)
    fun handleThrowable(ex: Throwable) = createResponseEntity(ex)

    override fun handleExceptionInternal(
        ex: Exception,
        body: Any?,
        headers: HttpHeaders,
        statusCode: HttpStatusCode,
        request: WebRequest
    ): ResponseEntity<Any>? {
        when (ex) {
            is NoHandlerFoundException -> {
                @Suppress("UNCHECKED_CAST")
                return createResponseEntity(ex) as ResponseEntity<Any>
            }

            is HttpMediaTypeNotSupportedException -> {
                @Suppress("UNCHECKED_CAST")
                return createResponseEntity(ex) as ResponseEntity<Any>
            }

            is MissingServletRequestParameterException -> {
                @Suppress("UNCHECKED_CAST")
                return createResponseEntity(ex) as ResponseEntity<Any>
            }

            is MethodArgumentNotValidException -> {
                @Suppress("UNCHECKED_CAST")
                return createResponseEntity(ex) as ResponseEntity<Any>
            }

            is HttpMessageNotReadableException -> {
                @Suppress("UNCHECKED_CAST")
                return createResponseEntity(ex) as ResponseEntity<Any>
            }

            else -> {
                log.error("handleExceptionInternal", ex)

                val json = JSON.objectNode()
                    .put("error", "khub.common.internal_error")
                    .put("message", ex.message)

                val responseHeaders = HttpHeaders.writableHttpHeaders(HttpHeaders.EMPTY)
                    .apply { contentType = MediaType.APPLICATION_JSON }

                @Suppress("UNCHECKED_CAST")
                return ResponseEntity(json.toString(), responseHeaders, statusCode) as ResponseEntity<Any>
            }
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger("ErrorHandler")

        fun writeErrorToResponse(
            throwable: Throwable,
            response: HttpServletResponse,
            method: String,
            requestUrl: String,
        ) {
            val errors = getApiErrorsFromThrowable(throwable)
            val requestId = LoggingUtils.getRequestId()
            val (json, status) = renderErrors(errors, requestId)
            val mainError = selectMainError(errors).error

            val logMessage = "${throwable.javaClass.simpleName} was thrown.\n\tENDPOINT: {} {}\n\tREQUEST ID: {}"
            when (mainError.loggingLevel) {
                LoggingLevel.INFO -> log.info(logMessage, method, requestUrl, requestId, throwable)
                LoggingLevel.DEBUG -> log.debug(logMessage, method, requestUrl, requestId, throwable)
                LoggingLevel.WARN -> log.warn(logMessage, method, requestUrl, requestId, throwable)
                LoggingLevel.ERROR -> log.error(logMessage, method, requestUrl, requestId, throwable)
            }

            response.contentType = MediaType.APPLICATION_JSON_VALUE
            response.writer.println(json.toString())
            response.status = status.value()
        }

        fun writeErrorToResponse(
            errors: List<ApiError>,
            response: HttpServletResponse,
            method: String,
            requestUrl: String,
        ) {
            val requestId = LoggingUtils.getRequestId()
            val (json, status) = renderErrors(errors, requestId)
            val mainError = selectMainError(errors).error

            val logMessage = "Set errors {} for request.\n\tENDPOINT: {} {}\n\tREQUEST ID: {}"
            when (mainError.loggingLevel) {
                LoggingLevel.INFO -> log.info(logMessage, json, method, requestUrl, requestId)
                LoggingLevel.DEBUG -> log.debug(logMessage, json, method, requestUrl, requestId)
                LoggingLevel.WARN -> log.warn(logMessage, json, method, requestUrl, requestId)
                LoggingLevel.ERROR -> log.error(logMessage, json, method, requestUrl, requestId)
            }

            response.contentType = MediaType.APPLICATION_JSON_VALUE
            response.writer.println(json.toString())
            response.status = status.value()
        }

        fun createResponseEntity(throwable: Throwable): ResponseEntity<String> {
            val errors = getApiErrorsFromThrowable(throwable)
            val uuid = LoggingUtils.getRequestId()
            val (json, status) = renderErrors(errors, uuid)
            val mainError = selectMainError(errors).error

            val logMessage = "${throwable.javaClass.simpleName} was thrown. \n\tERROR UUID: $uuid"

            when (mainError.loggingLevel) {
                LoggingLevel.INFO -> log.info(logMessage, throwable)
                LoggingLevel.DEBUG -> log.debug(logMessage, throwable)
                LoggingLevel.WARN -> log.warn(logMessage, throwable)
                LoggingLevel.ERROR -> log.error(logMessage, throwable)
            }

            val headers = HttpHeaders()
            headers.contentType = MediaType.APPLICATION_JSON
            return ResponseEntity(json.toString(), headers, status)
        }

        fun createPublicResourceNotFound(
            attachmentNotFoundException: AttachmentNotFoundException,
            locale: String,
            hostname: String
        ): ResponseEntity<*> {
            if (attachmentNotFoundException.isImage) {
                val notFoundImageContent =
                    (ApiErrorHandler::class as Any).javaClass.getResourceAsStream("/images/image404_${locale}.svg")
                        ?.use { it.readAllBytes() } ?: return ResponseEntity.notFound().build<String>()
                val header = HttpHeaders()
                header.add("Content-Type", "image/svg+xml")
                return ResponseEntity.ok()
                    .headers(header)
                    .contentLength(notFoundImageContent.size.toLong())
                    .body(ByteArrayResource(notFoundImageContent))
            } else {
                val header = HttpHeaders()
                header.location = URI.create("https://${hostname}/static/pages/page404_${locale}.html")
                return ResponseEntity.status(HttpStatus.MOVED_PERMANENTLY)
                    .headers(header)
                    .build<String>()
            }
        }

        private fun renderErrors(errors: List<ApiError>, uuid: String): Pair<ObjectNode, HttpStatus> {
            val mainError = selectMainError(errors)
            val json = buildJsonError(mainError)
            if (errors.size > 1) {
                json.set<JsonNode>("errors", JSON.mapper.createArrayNode().addAll(errors.map { buildJsonError(it) }))
            }
            if (mainError.error.status.value() >= 500) {
                json.put("uuid", uuid)
            }
            return Pair(json, mainError.error.status)
        }

        fun selectMainError(errors: List<ApiError>): ApiError {
            if (errors.isEmpty()) {
                return ApiError(ApiErrorCode.INTERNAL_SERVER_ERROR)
            }
            return errors.maxByOrNull { e -> e.error.status.value() }!!
        }

        private fun buildJsonError(error: ApiError): ObjectNode {
            val message = if (error.args.isEmpty()) {
                error.error.message
            } else {
                StrSubstitutor.replace(error.error.message, error.args)
            }

            val errorCode = if (error.args.containsKey(SOURCE_ERROR_CODE_ARG)) {
                error.args[SOURCE_ERROR_CODE_ARG]?.toString() ?: error.error.code
            } else {
                error.error.code
            }
            val json = JSON.objectNode()
                .put("error", errorCode)
                .put("message", message)
            if (error.args.isNotEmpty()) {
                json.set<JsonNode>("args", JSON.toNode(error.args))
            }
            return json
        }

        private fun getApiErrorsFromThrowable(throwable: Throwable): List<ApiError> {
            if (throwable is KhubException) return throwable.errors
            if (throwable is AccessDeniedException) return listOf(ApiError(ApiErrorCode.ACCESS_DENIED, throwable))
            if (throwable is InsufficientAuthenticationException) return listOf(ApiError(ApiErrorCode.AUTHENTICATION_REQUIRED))
            if (throwable is AuthenticationException) return listOf(ApiError(ApiErrorCode.AUTHENTICATION_REQUIRED, throwable))
            if (throwable is CompletionException && throwable.cause != null) return getApiErrorsFromThrowable(throwable.cause!!)
            if (throwable is RestClientException && throwable.cause is HttpMessageNotReadableException) return getApiErrorsFromThrowable(throwable.cause!!)
            if (throwable is RequestRejectedException) return listOf(ApiError(ApiErrorCode.REQUEST_REJECTED_ERROR, "message" to throwable.message.toString()))
            if (throwable is MethodArgumentNotValidException) return listOf(ApiError(ApiErrorCode.METHOD_ARGUMENT_NOT_VALID, throwable))
            if (throwable is HttpMessageNotReadableException) return listOf(ApiError(ApiErrorCode.MESSAGE_NOT_READABLE, throwable))
            if (throwable is ConstraintViolationException) return throwable.constraintViolations.mapNotNull {ApiErrorCode.fromCode(it.messageTemplate)}.map { ApiError(it) }
            if (throwable is IllegalArgumentException) return listOf(ApiError(ApiErrorCode.METHOD_ARGUMENT_NOT_VALID, throwable))

            if (throwable is NoHandlerFoundException) {
                return listOf(
                    ApiError(
                        ApiErrorCode.HANDLER_NOT_FOUND,
                        mapOf("method" to throwable.httpMethod, "uri" to throwable.requestURL)
                    )
                )
            }

            if (throwable is HttpMediaTypeNotSupportedException) {
                val supported = throwable.supportedMediaTypes.joinToString { it.toString() }
                return listOf(
                    ApiError(
                        ApiErrorCode.UNSUPPORTED_MEDIA_TYPE,
                        mapOf("type" to (throwable.contentType?.toString() ?: ""), "supported" to supported)
                    )
                )
            }

            if (throwable is MissingServletRequestParameterException) {
                return listOf(
                    ApiError(
                        ApiErrorCode.MISSING_REQUIRED_PARAMETER,
                        mapOf("parameterName" to throwable.parameterName, "parameterType" to throwable.parameterType)
                    )
                )
            }

            return listOf(ApiError(ApiErrorCode.INTERNAL_SERVER_ERROR, throwable))
        }
    }
}
