package com.justai.khub.project.endpoint

import com.justai.khub.api.fe.ProjectFilesApiService
import com.justai.khub.api.fe.model.*
import com.justai.khub.common.util.JSON
import com.justai.khub.common.util.WebUtils.getCurrentAccountId
import com.justai.khub.common.util.WebUtils.saveAsTempFile
import com.justai.khub.integration.dto.CloudConfluenceIntegrationSettings
import com.justai.khub.integration.dto.ConfluenceIntegrationSettings
import com.justai.khub.integration.dto.IntegrationFilter
import com.justai.khub.integration.dto.MinervaIntegrationSettings
import com.justai.khub.integration.enumeration.IntegrationStatus
import com.justai.khub.integration.mapper.IntegrationMapper.DEFAULT_PAGINATION
import com.justai.khub.integration.service.IntegrationService
import com.justai.khub.project.dto.ProjectFileMetadata
import com.justai.khub.project.dto.ProjectFilesFilter
import com.justai.khub.project.enumeration.ProjectSourceStatus
import com.justai.khub.project.mapper.ProjectFileMapper.initEntity
import com.justai.khub.project.mapper.ProjectFileMapper.mapFileSort
import com.justai.khub.project.mapper.ProjectFileMapper.toDTO
import com.justai.khub.project.mapper.ProjectVersionMapper.Companion.DEFAULT_VERSION
import com.justai.khub.project.service.ProjectFileService
import com.justai.khub.project.service.ProjectService
import com.justai.khub.project.service.ProjectVersionService
import org.springframework.core.io.ByteArrayResource
import org.springframework.core.io.Resource
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.orm.ObjectOptimisticLockingFailureException
import org.springframework.retry.annotation.Backoff
import org.springframework.retry.annotation.Retryable
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.nio.file.Files

@Service
class ProjectFileEndpoint(
    private val projectFileService: ProjectFileService,
    private val integrationService: IntegrationService,
    private val projectService: ProjectService,
    private val projectVersionService: ProjectVersionService
) : ProjectFilesApiService {

    override fun getProjectFilesSummary(projectId: Long, version: String?): ProjectFilesSummary {
        val actualVersion = version ?: DEFAULT_VERSION
        val currentAccountId = getCurrentAccountId()
        return projectFileService.getFilesSummary(projectId, actualVersion, currentAccountId)
    }

    override fun addFile(projectId: Long, version: String?, metadata: String?, file: MultipartFile): SourceDTO {
        val currentAccountId = getCurrentAccountId()
        val fileName = file.originalFilename ?: file.name
        val tempFile = file.saveAsTempFile()
        val parsedMetadata: ProjectFileMetadata = metadata?.let { JSON.parse<ProjectFileMetadata>(it) } ?: ProjectFileMetadata()
        val createdFile = try {
            val project = projectService.getProject(projectId, currentAccountId)
            val versionEntity = projectVersionService.getOrCreateVersion(project, version ?: DEFAULT_VERSION)
            val newFileEntity = initEntity(versionEntity, parsedMetadata, fileName, Files.size(tempFile).toInt(), false)
            projectFileService.addFile(newFileEntity, tempFile)
        } finally {
            Files.deleteIfExists(tempFile)
        }
        return createdFile.toDTO()
    }

    // for concurrent transactional deletion parent page and children (attachments)
    @Retryable(
        value = [ObjectOptimisticLockingFailureException::class],
        maxAttempts = 3,
        backoff = Backoff(delay = 20)
    )
    override fun deleteFile(projectId: Long, fileId: Long) {
        val currentAccountId = getCurrentAccountId()
        projectFileService.deleteFile(projectId, fileId, currentAccountId)
    }

    override fun getFiles(
        projectId: Long,
        pageSize: Int,
        version: String?,
        pageNum: Int,
        name: String?,
        sort: String?,
        direction: String?,
        status: String?
    ): ProjectSourcesPage {
        val mappedSort = Sort.by(
            direction?.let { Sort.Direction.fromString(it) } ?: Sort.Direction.ASC,
            mapFileSort(sort)
        )
        val mappedStatuses = if (status.isNullOrBlank()) {
            listOf()
        } else {
            status.let { statusesRaw -> statusesRaw.split(",").map { ProjectSourceStatus.valueOf(it.trim()) } }
        }
        val filter = ProjectFilesFilter(
            projectId = projectId,
            versionName = version ?: DEFAULT_VERSION,
            accountId = getCurrentAccountId(),
            pageable = PageRequest.of(pageNum, pageSize, mappedSort),
            statuses = mappedStatuses,
            namePart = name?.trim()

        )
        val page = projectFileService.getFiles(filter)
        val filesWithIntegration = page.content.filter { it.integrationId != null }.associateBy { it.id }
        val integrationById = if (filesWithIntegration.isNotEmpty()) {
            val integrationFilter = IntegrationFilter(
                versionName = version ?: DEFAULT_VERSION,
                accountId = getCurrentAccountId(),
                pageable = DEFAULT_PAGINATION,
                statuses = IntegrationStatus.entries,
                namePart = null,
                projectId = projectId

            )
            val integrations = integrationService.getIntegrations(integrationFilter)
            integrations.content.associateBy { it.id }
        } else emptyMap()

        return ProjectSourcesPage(
            content = page.content.map {
                it.toDTO(filesWithIntegration[it.id]?.let { integrationById[it.integrationId] }?.let {
                    when (it.settings) {
                        is ConfluenceIntegrationSettings -> SourceType.CONFLUENCE
                        is CloudConfluenceIntegrationSettings -> SourceType.CLOUD_CONFLUENCE
                        is MinervaIntegrationSettings -> SourceType.MINERVA
                    }
                } ?: SourceType.FILE)
            },
            paging = PagingResponse(
                totalCount = page.totalElements,
                pageNum = page.number,
                pageSize = page.size
            )
        )
    }

    override fun downloadFileResponseEntity(projectId: Long, fileId: Long): ResponseEntity<Resource> {
        val currentAccountId = getCurrentAccountId()
        val (filePath, fileContent) = projectFileService.getFileContent(projectId, fileId, currentAccountId)
        val fileName = filePath.substringAfterLast("/")
        return sendFile(fileName, fileContent)
    }

    override fun downloadParsedFileResponseEntity(projectId: Long, fileId: Long): ResponseEntity<Resource> {
        val currentAccountId = getCurrentAccountId()
        val (filePath, fileContent) = projectFileService.getParsedContent(projectId, fileId, currentAccountId)
        val fileName = filePath.substringAfterLast("/")
        return sendFile(fileName, fileContent)
    }

    override fun downloadChunksResponseEntity(projectId: Long, fileId: Long): ResponseEntity<Resource> {
        val currentAccountId = getCurrentAccountId()
        val (archivePath, fileContent) = projectFileService.getChunksArchive(projectId, fileId, currentAccountId)
        val archiveName = archivePath.substringAfterLast("/")
        return sendFile(archiveName, fileContent)
    }

    companion object {
        fun sendFile(fileName: String, fileContent: ByteArray?): ResponseEntity<Resource> {
            if (fileContent == null) {
                return ResponseEntity.noContent().build()
            }
            val encodedFilename = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString())
            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"$encodedFilename\"")
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .contentLength(fileContent.size.toLong())
                .body(ByteArrayResource(fileContent))
        }
    }
}
