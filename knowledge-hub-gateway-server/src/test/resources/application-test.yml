sql: "./src/main/migrations"

server:
  shutdown: graceful


spring:
  main:
    allow-bean-definition-overriding: true
  jpa: # for logging psql queries
    show-sql: false
    properties:
      hibernate:
        type: info
  datasource:
    hikari:
      maximum-pool-size: 2

billing:
  enabled: true

rag:
  chatQueryProcessingJobThreadPoolSize: 1

scheduling:
  enabled: true
  rag:
    # to prevent conflicting between background jobs if multiple spring contexts started
    enabled: false
  ingest:
    enabled: true
  integration:
    enabled: false
