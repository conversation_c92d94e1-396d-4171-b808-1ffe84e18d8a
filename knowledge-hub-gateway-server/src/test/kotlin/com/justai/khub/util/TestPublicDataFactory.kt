package com.justai.khub.util

import com.justai.khub.api.public.model.ConfluenceSettings
import com.justai.khub.api.public.model.IntegrationCreateRequest
import org.apache.commons.lang3.RandomStringUtils

object TestPublicDataFactory {

    fun integrationCreateRequest():IntegrationCreateRequest {
        return IntegrationCreateRequest(
            name = "confluence-integration-${RandomStringUtils.randomAlphabetic(5)}",
            settings = confluenceIntegrationSettings()
        )
    }

    fun confluenceIntegrationSettings(): ConfluenceSettings {
        return ConfluenceSettings(
            baseUrl = "http://localhost",
            space = "test-space",
            token = "test-token"
        )
    }
}
