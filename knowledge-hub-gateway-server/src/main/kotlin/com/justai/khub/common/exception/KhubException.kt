package com.justai.khub.common.exception

import com.justai.khub.common.configuration.service.ApiErrorHandler
import com.justai.khub.common.dto.ApiError
import com.justai.khub.common.enumeration.ApiErrorCode
import org.apache.commons.lang3.text.StrSubstitutor

open class KhubException(var errors: List<ApiError>, cause: Exception? = null) :
    RuntimeException(ApiErrorHandler.selectMainError(errors).run { StrSubstitutor.replace(error.message, args) }, cause) {

    constructor(error: ApiErrorCode, args: Map<String, String?> = emptyMap()) : this(listOf(ApiError(error, args)))
    constructor(error: ApiErrorCode, args: Map<String, String?> = emptyMap(), cause: Exception) : this(listOf(ApiError(error, args)), cause)
    constructor(error: ApiErrorCode, cause: Exception) : this(listOf(ApiError(error, emptyMap())), cause)
}
