package com.justai.khub.integration.dto.atlassian

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class TokenCodeExchangeRequest(
    val clientId: String,
    val clientSecret: String,
    val code: String,
    val redirectUri: String,
    val grantType: String = "authorization_code"
)
