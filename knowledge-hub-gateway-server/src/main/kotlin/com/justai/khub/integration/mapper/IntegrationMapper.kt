package com.justai.khub.integration.mapper

import com.justai.khub.api.fe.model.IntegrationDTO
import com.justai.khub.api.fe.model.IntegrationFull
import com.justai.khub.api.fe.model.IntegrationShort
import com.justai.khub.api.fe.model.IntervalUnits
import com.justai.khub.common.configuration.dto.IntegrationProperties
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.common.mapper.CommonMapper.toAuditDTO
import com.justai.khub.common.util.JSON
import com.justai.khub.integration.dto.CloudConfluenceIntegrationSettings
import com.justai.khub.integration.dto.ConfluenceIntegrationSettings
import com.justai.khub.integration.dto.IntegrationSettings
import com.justai.khub.integration.dto.MinervaIntegrationSettings
import com.justai.khub.integration.entity.IntegrationEntity
import com.justai.khub.integration.enumeration.IntegrationStatus
import com.justai.khub.project.entity.ProjectVersionEntity
import org.slf4j.LoggerFactory
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort

object IntegrationMapper {
    const val DEFAULT_SORT = "name"

    private val log = LoggerFactory.getLogger(this::class.java)

    val DEFAULT_PAGINATION = PageRequest.of(1, 1000, Sort.by(Sort.Direction.DESC, "createdAt"))

    fun IntegrationDTO.toEntity(version: ProjectVersionEntity?, integrationProperties: IntegrationProperties): IntegrationEntity {
        val defaultCheckIntervalMinutes = integrationProperties.externalSources.checkIntervalMinutes
        return IntegrationEntity().also {
            it.autoSync = this.autoSync ?: false
            it.autoIngest = this.autoIngest ?: false
            it.name = this.name
            it.status = IntegrationStatus.ACTIVE
            it.settings = this.toSettings()
            it.checkIntervalMinutes = this.checkIntervalMinutes?.coerceAtLeast(defaultCheckIntervalMinutes) ?: defaultCheckIntervalMinutes
            if (version != null) it.version = version
        }
    }

    fun IntegrationEntity.toShortDTO(): IntegrationShort {
        return IntegrationShort(
            id = this.id,
            name = this.name,
            status = this.status.name,
            type = this.toType(),
            lastError = this.lastError,
            errorsCount = this.errorsCount,
            audit = this.toAuditDTO(),
        )
    }

    fun IntegrationEntity.toFullDTO(): IntegrationFull {
        return IntegrationFull(
            id = this.id,
            name = this.name,
            status = this.status.name,
            type = this.toType(),
            audit = this.toAuditDTO(),
            autoSync = this.autoSync,
            autoIngest = this.autoIngest,
            checkIntervalMinutes = this.checkIntervalMinutes,
            checkIntervalUnits = IntervalUnits.valueOf(this.checkIntervalUnits.name),
            lastError = this.lastError,
            errorsCount = this.errorsCount,
            settings = JSON.toMap(this.settings),
        )
    }

    private fun IntegrationEntity.toType(): String {
        return when (this.settings) {
            is ConfluenceIntegrationSettings -> IntegrationDTO.Type.CONFLUENCE.toString()
            is CloudConfluenceIntegrationSettings -> IntegrationDTO.Type.CLOUD_CONFLUENCE.toString()
            is MinervaIntegrationSettings -> IntegrationDTO.Type.MINERVA.toString()
        }
    }

    private fun IntegrationDTO.toSettings(): IntegrationSettings = when (this.settings) {
        is IntegrationSettings -> this.settings
        else -> {
            try {
                val rawSettings = JSON.stringify((this.settings as MutableMap<String, Any?>).also { settings ->
                    settings.getValue<String?>("baseUrl")?.let { baseUrl ->
                        if (baseUrl.isBlank())
                            settings.remove("baseUrl")
                        else
                            settings["baseUrl"] = baseUrl.trim().removeSuffix("/")
                    }

                    settings["type"] = this.type.name.lowercase()

                    settings.getValue<String?>("siteUrl")?.let { siteUrl ->
                        if (this.type == IntegrationDTO.Type.CLOUD_CONFLUENCE)
                            settings["siteUrl"] = extractDomain(siteUrl) ?: error("siteUrl is not a valid url")
                    }
                })

                JSON.parse<IntegrationSettings>(rawSettings)
            } catch (_: Exception) {
                throw KhubException(ApiErrorCode.CLIENT_ERROR, mapOf("message" to "Unexpected settings type ${this.settings}"))
            }
        }
    }

    fun mapIntegrationSort(clientSort: String?): String {
        return when (clientSort) {
            "name" -> clientSort
            "createdAt" -> clientSort
            "status" -> clientSort
            null -> DEFAULT_SORT
            else -> {
                log.warn("Unexpected integration sort: {}", clientSort)
                DEFAULT_SORT
            }
        }
    }

    /**
     * Get the value and make unsafe cast to type [T].
     * Use the nullable [T] if [key] presence is optional in [MutableMap].
     */
    private inline fun <reified T> MutableMap<String, Any?>.getValue(key: String): T = get(key) as T

    /**
     * Extract only the domain part with schema (http(s)://domain.com)
     */
    private fun extractDomain(url: String): String? {
        val domainRegex = "(https?://[^/]+).*".toRegex()
        val matchResult = domainRegex.find(url.trim())
        return matchResult?.groupValues?.get(1)
    }
}
