package com.justai.khub.qa.repository

import com.justai.khub.qa.entity.TestRunEntity
import org.springframework.data.jpa.repository.EntityGraph
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface TestRunRepository : JpaRepository<TestRunEntity, Long> {

    @Query("SELECT tr.test.id FROM TestRunEntity tr WHERE tr.testSetRun.id = :testSetRunId")
    fun findTestIdsByTestSetRunId(testSetRunId: Long): List<Long>

    @Query("SELECT tr FROM TestRunEntity tr WHERE tr.testSetRun.id = :testSetRunId")
    @EntityGraph(attributePaths = ["testSetRun", "test"])
    fun findByTestSetRunId(testSetRunId: Long): List<TestRunEntity>
}
