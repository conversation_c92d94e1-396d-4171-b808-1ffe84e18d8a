package com.justai.khub.ingest.service

import com.justai.khub.project.entity.ProjectFileEntity
import com.justai.khub.project.entity.ProjectVersionEntity
import com.justai.khub.project.enumeration.ProjectSourceStatus
import com.justai.khub.project.repository.ProjectFileRepository
import com.justai.khub.project.service.ProjectVersionService
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

@Service
class IngestService(
    private val projectVersionService: ProjectVersionService,
    private val projectFileRepository: ProjectFileRepository,
) {

    @Transactional(rollbackFor = [Exception::class])
    fun startIngest(projectId: Long, versionName: String, accountId: Long): ProjectVersionEntity {
        val version = projectVersionService.getVersion(projectId, versionName, accountId)
        projectFileRepository.updateByStatusIn(
            version.id,
            listOf(
                ProjectSourceStatus.NOT_INGESTED,
                ProjectSourceStatus.FAILED_TO_INGEST,
                ProjectSourceStatus.INGEST_CANCELLED
            ),
            ProjectSourceStatus.READY_TO_INGEST
        )
        return version
    }

    @Transactional(rollbackFor = [Exception::class])
    fun cancelIngest(projectId: Long, versionName: String, reason: String?, accountId: Long) {
        val version = projectVersionService.getVersion(projectId, versionName, accountId)
        projectFileRepository.updateByStatusIn(
            version.id,
            listOf(ProjectSourceStatus.READY_TO_INGEST, ProjectSourceStatus.PROCESSING),
            ProjectSourceStatus.INGEST_CANCELLED,
            reason
        )
    }

    @Transactional(readOnly = true)
    fun getReadyToIngest(limit: Int): List<ProjectFileEntity> {
        val pagination = PageRequest.of(0, limit, Sort.by(Sort.Direction.ASC, "updatedAt"))
        return projectFileRepository.findAllByStatus(ProjectSourceStatus.READY_TO_INGEST, pagination)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun releaseTimedOutFileIngests(lastUpdateBefore: LocalDateTime): Int {
        return projectFileRepository.releaseTimedOutJobs(
            ProjectSourceStatus.PROCESSING,
            ProjectSourceStatus.READY_TO_INGEST,
            lastUpdateBefore
        )
    }
}
