package com.justai.khub.project

import com.justai.khub.BaseProjectIT
import com.justai.khub.api.public.model.AddLinkRequest
import com.justai.khub.api.public.model.AddTextRequest
import com.justai.khub.project.enumeration.ProjectSourceStatus
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.LocalDate

class ProjectSourcePublicIT : BaseProjectIT() {
    private val TEST_PUBLIC_TXT = "https://sample-files.com/downloads/documents/txt/simple.txt"
    private val TEST_PUBLIC_MP3 = "https://sample-files.com/downloads/audio/mp3/sample-files.com_tone_test_audio.mp3"

    private lateinit var projectApiKey: String

    @BeforeEach
    fun init() {
        projectApiKey = apiKeyHelper.createApiKey(project.id).key!!
    }

    @Test
    fun testProjectSources() = runBlocking {
        val projectFiles = listOf(
            projectFileHelper.uploadFile(projectId = project.id),
            projectFileHelper.uploadFile(projectId = project.id)
        )
        val projectSources = projectSourcePublicHelper.getProjectSources(apiKey = projectApiKey)
        val expected = projectSources.sources.map { it.id }
        val actual = projectFiles.map { it.id }
        assertTrue(expected.containsAll(actual), "Actual ($actual) a not matched to expected sources: $expected")

        val deletedProjectSourceId = expected.first()
        projectSourcePublicHelper.deleteProjectSource(deletedProjectSourceId, apiKey = projectApiKey)
        val updatedProjectSources = projectSourcePublicHelper.getProjectSources(apiKey = projectApiKey)
        assertFalse(updatedProjectSources.sources.any { it.id == deletedProjectSourceId })
    }

    @Test
    fun testProjectSourcesWithFilters() = runBlocking {
        repeat(2) {
            projectFileHelper.uploadFile(projectId = project.id)
        }
        var projectSources = projectSourcePublicHelper.getProjectSources(
            apiKey = projectApiKey,
            params = mapOf(
                "createDateFrom" to LocalDate.now().minusDays(1).toString(),
                "createDateTo" to LocalDate.now().toString(),
                "sourceStatus" to ProjectSourceStatus.NOT_INGESTED.toString()
            )
        )
        assertEquals(2, projectSources.sources.size)

        projectSources = projectSourcePublicHelper.getProjectSources(
            apiKey = projectApiKey,
            params = mapOf(
                "sourceStatus" to ProjectSourceStatus.INGESTED.toString()
            )
        )
        assertEquals(0, projectSources.sources.size)
    }

    @Test
    fun testAddProjectSources() {
        val initialSources = projectSourcePublicHelper.getProjectSources(apiKey = projectApiKey)
        assertTrue(initialSources.sources.isEmpty())
        projectSourcePublicHelper.addTextSourceAndExpectSuccess(AddTextRequest(name = "text-source.txt", text = "test"), apiKey = projectApiKey)
        projectSourcePublicHelper.addLinkSourceAndExpectSuccess(AddLinkRequest(name = "link-source.txt", link = TEST_PUBLIC_TXT), apiKey = projectApiKey)
        projectSourcePublicHelper.addFileSourceAndExpectSuccess(fileName = "file-source.txt", apiKey = projectApiKey)
        val sourcesAfterUpload = projectSourcePublicHelper.getProjectSources(apiKey = projectApiKey)
        assertEquals(3, sourcesAfterUpload.sources.size)
        assertTrue(sourcesAfterUpload.sources.any { it.name == "text-source.txt" })
        assertTrue(sourcesAfterUpload.sources.any { it.name == "link-source.txt" })
        assertTrue(sourcesAfterUpload.sources.any { it.name == "file-source.txt" })
    }

    @Test
    fun testUpdateProjectSource() {
        val initialSource = projectSourcePublicHelper.addTextSourceAndExpectSuccess(AddTextRequest(name = "text-source.txt", text = "test"), apiKey = projectApiKey)
        ingestHelper.cancelIngestAndExpectOk(project.id)
        projectSourcePublicHelper.updateLinkSourceAndExpectSuccess(AddLinkRequest(name = initialSource.name, link = TEST_PUBLIC_TXT), apiKey = projectApiKey)
        ingestHelper.cancelIngestAndExpectOk(project.id)
        projectSourcePublicHelper.updateFileSourceAndExpectSuccess(fileName = initialSource.name, apiKey = projectApiKey)
        ingestHelper.cancelIngestAndExpectOk(project.id)
        projectSourcePublicHelper.updateTextSourceAndExpectSuccess(AddTextRequest(name = initialSource.name, text = "updated-text"), apiKey = projectApiKey)
        projectSourcePublicHelper.updateTextSource(AddTextRequest(name = initialSource.name, text = "updated-text"), apiKey = projectApiKey)
            .andExpect(status().isBadRequest)// can't update ingesting files

        val sourcesAfterUpdate = projectSourcePublicHelper.getProjectSources(apiKey = projectApiKey)
        assertEquals(1, sourcesAfterUpdate.sources.size)
        val updatedSource = sourcesAfterUpdate.sources.first()
        assertEquals(initialSource.id, updatedSource.id)
        assertEquals(initialSource.name, updatedSource.name)
        assertEquals("updated-text".toByteArray().size, updatedSource.sizeBytes)
    }

    @Test
    fun testUpdateMissingProjectSource() {
        projectSourcePublicHelper.updateTextSource(AddTextRequest(name = "text-source.txt", text = "test"), apiKey = projectApiKey)
            .andExpect(status().isNotFound)
        projectSourcePublicHelper.updateLinkSource(AddLinkRequest(name = "link-source.txt", link = TEST_PUBLIC_TXT), apiKey = projectApiKey)
            .andExpect(status().isNotFound)
        projectSourcePublicHelper.updateFileSource(fileName = "file-source.txt", apiKey = projectApiKey)
            .andExpect(status().isNotFound)
    }


    @Test
    fun testAddInvalidProjectSource() {
        val sourceWithEmptyId = projectSourcePublicHelper.addTextSourceAndExpectSuccess(AddTextRequest(name = "     ", text = "test"), apiKey = projectApiKey)
        assertFalse(sourceWithEmptyId.name.isBlank())

        // test unsupported extension
        projectSourcePublicHelper.addTextSource(AddTextRequest(name = "test.mp3", text = "test"), apiKey = projectApiKey)
            .andExpect(status().isBadRequest)
        projectSourcePublicHelper.addLinkSource(AddLinkRequest(name = null, link = TEST_PUBLIC_MP3), apiKey = projectApiKey)
            .andExpect(status().isBadRequest)
    }

    @Test
    fun testDownloadProjectSource() {
        val projectFiles = listOf(
            projectFileHelper.uploadFile(projectId = project.id),
            projectFileHelper.uploadFile(projectId = project.id)
        )
        projectFiles.forEach {
            val content = projectSourcePublicHelper.downloadProjectSource(it.id, projectApiKey)
            assertNotNull(content)
        }
    }
}
