package com.justai.khub.common.connector

import com.fasterxml.jackson.databind.node.ObjectNode
import com.justai.khub.api.fe.model.E2ECheckResult
import com.justai.khub.api.fe.model.E2EComponent
import com.justai.khub.attachment.service.AttachmentService
import com.justai.khub.common.configuration.dto.IntegrationProperties
import com.justai.khub.common.dto.rag.QueryResponse
import com.justai.khub.common.dto.rag.RetrieveChunksResponse
import com.justai.khub.common.dto.ServiceVersionResponse
import com.justai.khub.common.dto.WithIntegrationTimer
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.enumeration.ApiErrorCode.Companion.SOURCE_ERROR_CODE_ARG
import com.justai.khub.common.enumeration.IntegrationName
import com.justai.khub.common.exception.KhubException
import com.justai.khub.common.util.JSON
import com.justai.khub.project.dto.*
import com.justai.khub.project.mapper.RagServiceMapper
import com.justai.khub.common.dto.rag.TestGenerateResponse
import com.justai.khub.query.dto.UserHistory
import com.justai.loadbalancer.annotations.HttpInternal
import org.slf4j.LoggerFactory
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod.GET
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.web.client.RestClientResponseException
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.postForObject

@Service
@WithIntegrationTimer(integrationName = IntegrationName.ragService)
class RagServiceConnector(
    private val ragServiceMapper: RagServiceMapper,
    @HttpInternal private val ragServiceRestTemplate: RestTemplate,
    private val attachmentService: AttachmentService,
    integrationProperties: IntegrationProperties
) {
    private val log = LoggerFactory.getLogger(this::class.java)

    val baseUrl = integrationProperties.rag.baseUrl

    fun getVersion(): ServiceVersionResponse {
        return ragServiceRestTemplate.getForObject("${baseUrl}/version", ServiceVersionResponse::class.java)!!
    }

    fun clearHistory(conversationId: String) {
        ragServiceRestTemplate.postForObject<Any>("${baseUrl}/conversations/${conversationId}/history/clear")
    }

    fun query(
        message: String,
        segment: String?,
        conversationId: String,
        searchPipelineConfig: SearchPipelineConfig,
        ingestSettings: IngestPipelineConfig,
        history: UserHistory? = null
    ): QueryResponse {
        val request = mapOf(
            "query" to mapOf(
                "message" to message, "segment" to segment, "conversation_id" to conversationId
            ),
            "config" to ragServiceMapper.toRagPipelineConfig(searchPipelineConfig, ingestSettings),
            "user_history" to history?.historyRecords?.let {
                mapOf(
                    "messages" to it.map { record ->
                        mapOf(
                            "content" to record.message, "role" to record.role.name
                        )
                    }.toList()
                )
            }
        )
        try {
            val response = ragServiceRestTemplate.postForEntity("${baseUrl}/predict", request, QueryResponse::class.java)
            checkStatus(response)
            return response.body!!.let { it.copy(answer = attachmentService.adjustAttachmentLinks(it.answer)) }
        } catch (ex: Exception) {
            handleException(ex)
            throw ex
        }
    }

    fun getChunksArchive(indexName: String, filePath: String): ByteArray? {
        val request = ragServiceMapper.toDownloadChunksRequest(indexName, filePath)
        try {
            val response = ragServiceRestTemplate.postForEntity("${baseUrl}/download-chunks", request, ByteArray::class.java)
            checkStatus(response)
            return response.body
        } catch (ex: Exception) {
            handleException(ex)
            throw ex
        }
    }

    private fun handleException(ex: Exception) {
        val errorCode =
            if (ex is RestClientResponseException && ex.responseHeaders?.contentType?.isCompatibleWith(MediaType.APPLICATION_JSON) == true) {
                (JSON.parse(ex.responseBodyAsString) as ObjectNode)["code"]?.asText()
                    ?: ApiErrorCode.RAG_INTEGRATION_ERROR.code
            } else {
                null
            }
        throw KhubException(ApiErrorCode.RAG_INTEGRATION_ERROR, mapOf(SOURCE_ERROR_CODE_ARG to errorCode), ex)
    }

    private fun checkStatus(response: ResponseEntity<*>) {
        if (!response.statusCode.is2xxSuccessful) {
            log.error("Can't connect to to rag service: status code = {}", response.statusCode)
            throw KhubException(ApiErrorCode.RAG_INTEGRATION_ERROR)
        }
    }

    fun retrieveChunks(
        query: String,
        searchConfig: SearchPipelineConfig,
        ingestConfig: IngestPipelineConfig,
        topK: Int? = null,
        conversationId: String? = null,
        segment: String? = null,
        history: UserHistory? = null
    ): RetrieveChunksResponse {
        val request = ragServiceMapper.toRetrieveChunksRequest(query, searchConfig, ingestConfig, topK)
        try {
            val response = ragServiceRestTemplate.postForEntity(
                "${baseUrl}/retrieve-chunks", request, RetrieveChunksResponse::class.java
            )
            checkStatus(response)
            return response.body!!.let {
                it.copy(chunks = it.chunks.map { chunk ->
                    chunk.copy(content = attachmentService.adjustAttachmentLinks(chunk.content ?: ""))
                })
            }
        } catch (ex: Exception) {
            handleException(ex)
            throw ex
        }
    }

    fun performE2E(requestId: String, loggingEnabledE2ECheck: Boolean): List<E2EComponent> {
        val request = HttpEntity(
            null,
            HttpHeaders().apply {
                set("Z-RequestId", requestId)
                set("loggingEnabledE2ECheck", "$loggingEnabledE2ECheck")
            }
        )
        val result = ragServiceRestTemplate
            .exchange("${baseUrl}/e2e-check", GET, request, E2ECheckResult::class.java)
        return result.body?.e2EComponents ?: listOf()
    }

    fun generateTests(
        fileLink: FileLink,
        parsedFileName: String,
        llm: LLMConfig,
        prompt: String,
        testsCount: Int
    ): TestGenerateResponse {
        val request = ragServiceMapper.toGenerateQuestionsRequest(fileLink, parsedFileName, llm, prompt, testsCount)
        try {
            return ragServiceRestTemplate.postForObject("${baseUrl}/api/testset/generate", request, TestGenerateResponse::class)
        } catch (ex: Exception) {
            handleException(ex)
            throw ex
        }
    }
}
