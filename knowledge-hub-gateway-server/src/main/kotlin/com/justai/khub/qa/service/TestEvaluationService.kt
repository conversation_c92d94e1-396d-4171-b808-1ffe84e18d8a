package com.justai.khub.qa.service

import com.justai.khub.qa.entity.TestEvaluationEntity
import com.justai.khub.qa.repository.TestEvaluationRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class TestEvaluationService(
    private val testEvaluationRepository: TestEvaluationRepository
) {

    @Transactional(rollbackFor = [Exception::class])
    fun createEvaluation(evaluation: TestEvaluationEntity): TestEvaluationEntity {
        return testEvaluationRepository.saveAndFlush(evaluation)
    }

    @Transactional(readOnly = true)
    fun findEvaluations(testSetRunId: Long): List<TestEvaluationEntity> {
        return testEvaluationRepository.findByTestSetRunId(testSetRunId)
    }
}
