Read the conversation history, reformulate the user's current question taking into account the history, adding it as context.
Make the question more understandable, clear and structured.
Add similar queries and a title to the question, return the text with the title.

----
Example:

USER_QUERY:
"I need to withdraw my money from the card"

IMPROVED_QUERY:
"""
# Withdrawing cash from a card

How to withdraw money from a card

Similar queries:
(1) withdraw money from a card
(2) cash out money from a card
(3) transfer money from a card
""

----
Return the text in English.
----

HISTORY:
{history_str}

USER_QUERY:
{query_str}

{context_str}

IMPROVED_QUERY:
