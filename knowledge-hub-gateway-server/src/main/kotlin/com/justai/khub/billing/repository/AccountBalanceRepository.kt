package com.justai.khub.billing.repository

import com.justai.khub.billing.entity.AccountBalanceEntity
import jakarta.persistence.LockModeType
import jakarta.persistence.QueryHint
import org.hibernate.jpa.HibernateHints.HINT_NATIVE_LOCK_MODE
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Lock
import org.springframework.data.jpa.repository.Query
import org.springframework.data.jpa.repository.QueryHints
import org.springframework.stereotype.Repository

@Repository
interface AccountBalanceRepository : JpaRepository<AccountBalanceEntity, Long> {
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    fun findForUpdateByAccountId(accountId: Long): AccountBalanceEntity?


    // DO NOT use @Lock(LockModeType.PESSIMISTIC_WRITE) here, as it would override any lock mode specified by query hints
    @QueryHints(value = [QueryHint(name = HINT_NATIVE_LOCK_MODE, value = "upgrade-skiplocked")])
    @Query("SELECT abe FROM AccountBalanceEntity abe WHERE abe.accountId = :accountId")
    fun findForUpdateByAccountIdSkipLocked(accountId: Long): AccountBalanceEntity?

    @Query("SELECT b FROM AccountBalanceEntity b WHERE b.accountId in :accountIds")
    fun findByAccountIds(accountIds: Collection<Long>): List<AccountBalanceEntity>
}
