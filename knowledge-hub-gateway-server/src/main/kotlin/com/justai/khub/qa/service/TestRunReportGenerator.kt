package com.justai.khub.qa.service

import com.justai.khub.common.configuration.dto.QAProperties
import com.justai.khub.common.dto.DocumentChunks
import com.justai.khub.qa.entity.TestEvaluationEntity
import com.justai.khub.qa.entity.TestRunEntity
import com.justai.khub.qa.entity.TestSetRunEntity
import org.apache.poi.xssf.usermodel.XSSFCell
import org.apache.poi.xssf.usermodel.XSSFSheet
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.springframework.stereotype.Service
import java.io.ByteArrayOutputStream

@Service
class TestRunReportGenerator(
    private val testRunService: TestRunService,
    private val testEvaluationService: TestEvaluationService,
    private val qaProperties: QAProperties
) : ReportGenerator() {
    val DETAILS_HEADERS = mutableListOf(
        "File",
        "Question",
        "Ground truth answer",
        "Actual answer",
        "Prompt\ntokens",
        "Completion\ntokens",
        "Processing\ntime (ms)",
        "Error",
        "Score",
        "Comment"
    )
    val CHUNKS_CONTENT_HEADER = "chunks"
    val SUMMARY_COLUMN_WIDTHS_IN_CHARS = listOf(20, 15, 15, 15, 20, 20, 30)
    val DETAILS_COLUMN_WIDTHS_IN_CHARS = listOf(10, 40, 80, 80, 10, 15, 15, 10, 10, 40)

    fun generateReport(testSetRun: TestSetRunEntity): ByteArray {
        val workbook = XSSFWorkbook()
        val summarySheet = createSummarySheet(workbook, testSetRun)
        SUMMARY_COLUMN_WIDTHS_IN_CHARS.forEachIndexed { index, width -> summarySheet.setColumnWidth(index, width * 256) }
        addBordersToAllCells(workbook, summarySheet)

        val testRuns = testRunService.getTestRuns(testSetRun.id)
        val evaluations = testEvaluationService.findEvaluations(testSetRun.id)
        if (testRuns.isNotEmpty()) {
            val detailsSheet = createDetailsSheet(workbook, testRuns, evaluations)
            DETAILS_COLUMN_WIDTHS_IN_CHARS.forEachIndexed { index, width -> detailsSheet.setColumnWidth(index, width * 256) }
            addBordersToAllCells(workbook, detailsSheet)
        }

        return ByteArrayOutputStream().use {
            workbook.write(it)
            it.toByteArray()
        }
    }

    private fun createSummarySheet(workbook: XSSFWorkbook, testSetRun: TestSetRunEntity): XSSFSheet {
        val sheet = workbook.createSheet("Summary")
        val headersRow = sheet.createRow(0)
        val dataRow = sheet.createRow(1)
        val headersWithDataSetter: List<Pair<String, (XSSFCell) -> Unit>> = listOf(
            "Status" to { it.setCellValue(testSetRun.status.toString()) },
            "Mean\nscore" to { it.setCellValue(testSetRun.meanScore?.toString() ?: "-") },
            "Finished\ntests" to { it.setCellValue(testSetRun.finishedTests.toDouble()) },
            "Total\ntests" to { it.setCellValue(testSetRun.testSet.totalTests.toDouble()) },
            "Prompt\ntokens" to { it.setCellValue(testSetRun.usedPromptTokens.toDouble()) },
            "Completion\ntokens" to { it.setCellValue(testSetRun.usedCompletionTokens.toDouble()) },
            "Error" to { it.setCellValue(testSetRun.lastError) },
        )
        headersWithDataSetter.forEachIndexed { index, (header, dataSetter) ->
            headersRow.createCell(index).apply {
                setCellValue(header)
                cellStyle = createHeaderStyle(workbook)
            }
            dataRow.createCell(index).apply(dataSetter).also { it.cellStyle = createDataStyle(workbook) }
        }
        if (testSetRun.lastError != null) {
            setErrorBg(workbook, dataRow)
        }
        return sheet
    }

    private fun createDetailsSheet(
        workbook: XSSFWorkbook,
        testRuns: List<TestRunEntity>,
        evaluations: List<TestEvaluationEntity>
    ): XSSFSheet {
        val sheet = workbook.createSheet("Details")
        val evaluationByTestRunId = evaluations.associateBy { it.testRun.id }
        createDetailSheetHeadersRow(workbook, sheet)
        testRuns.forEachIndexed { index, testRun ->
            val evaluation = evaluationByTestRunId[testRun.id]
            createDetailsSheetDataRow(workbook, sheet, index + 1, testRun, evaluation)
        }
        return sheet
    }

    private fun createDetailSheetHeadersRow(workbook: XSSFWorkbook, sheet: XSSFSheet) {
        sheet.autoSizeColumn(0)
        val headersRow = sheet.createRow(0)
        DETAILS_HEADERS.forEachIndexed { index, header ->
            headersRow.createCell(index).apply {
                setCellValue(header)
                cellStyle = createHeaderStyle(workbook)
            }
        }
        if (qaProperties.useChunksContent) {
            headersRow.createCell(DETAILS_HEADERS.size).apply {
                setCellValue(CHUNKS_CONTENT_HEADER)
                cellStyle = createHeaderStyle(workbook)
            }
        }
    }

    private fun createDetailsSheetDataRow(
        workbook: XSSFWorkbook,
        sheet: XSSFSheet,
        rowIndex: Int,
        testRun: TestRunEntity,
        evaluation: TestEvaluationEntity?,
    ) {
        val dataRow = sheet.createRow(rowIndex)
        val score = evaluation?.score
        val evaluationError = evaluation?.lastError
        val dataSetters: MutableList<(XSSFCell) -> Unit> = mutableListOf(
            { it.setCellValue(testRun.test.groundTruthFile?.relativePath) },
            { it.setCellValue(testRun.test.question) },
            { it.setCellValue(testRun.test.groundTruthAnswer) },
            { it.setCellValue(testRun.actualAnswer) },
            { it.setCellValue(testRun.usedPromptTokens.toDouble()) },
            { it.setCellValue(testRun.usedCompletionTokens.toDouble()) },
            { it.setCellValue(testRun.processingTimeMs.toDouble()) },
            { it.setCellValue(testRun.lastError ?: evaluationError) },
            { if (score != null) it.setCellValue(score) else it.setCellValue("") },
            { it.setCellValue(evaluation?.comment) },
        )
        if (qaProperties.useChunksContent) {
            dataSetters.add { if (testRun.usedChunks != null) it.setCellValue(mapUsedChunks(testRun.usedChunks!!.chunks)) else it.setCellValue("") }
        }
        dataSetters.forEachIndexed { index, dataSetter -> dataRow.createCell(index).apply(dataSetter).also { it.cellStyle = createDataStyle(workbook) } }
        if (score != null && score < 5.0) {
            setErrorBg(workbook, dataRow)
        } else if (score != null && score < 8.0) {
            setWarnBg(workbook, dataRow)
        }
    }

    private fun mapUsedChunks(documentChunks: DocumentChunks): String {
        return documentChunks.chunks.joinToString("\n") {
            "id:\n${it.id}\ndocId:\n${it.docId}\nscore:\n${it.score}\ncontent:\n${it.content}"
        }
    }
}
