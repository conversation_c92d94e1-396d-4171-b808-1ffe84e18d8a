package com.justai.khub.ingest.dto

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class ParsingTaskDTO(
    val id: String,
    val state: String,
    val startedAt: Int,
    val parsedContentLength: Int?,
    val parsedFile: String?,
    val attachmentIds: List<String>?,
    val finishedAt: Int? = null,
    val error: String? = null
) {
    fun isFinished(): Boolean = finishedAt != null
}
