package com.justai.khub.util

import com.justai.khub.api.fe.model.UserBalanceDTO
import com.justai.khub.api.fe.model.UserInfoDTO
import com.justai.khub.util.WebUtils.parseTo
import com.justai.security.session.auth.JustSessionAuthentication
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors
import org.springframework.stereotype.Component
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@Component
class UserHelper {

    @Autowired
    protected lateinit var mvc: MockMvc

    @Autowired
    protected lateinit var defaultAuth: JustSessionAuthentication

    fun getMe(ccAuth: JustSessionAuthentication? = defaultAuth, apiKey: String? = ""): UserInfoDTO {
        return mvc.perform(
            get("/api/khub/web/users/me")
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
            .andExpect(status().isOk)
            .andReturn().parseTo<UserInfoDTO>()
    }

    fun getCurrentUserBalance(ccAuth: JustSessionAuthentication? = defaultAuth, apiKey: String? = ""):UserBalanceDTO {
        return mvc.perform(
            get("/api/khub/web/users/me/balance")
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
            .andExpect(status().isOk)
            .andReturn().parseTo<UserBalanceDTO>()
    }

}
