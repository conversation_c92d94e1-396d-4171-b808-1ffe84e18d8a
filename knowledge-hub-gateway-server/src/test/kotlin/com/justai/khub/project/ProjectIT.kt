package com.justai.khub.project

import com.justai.khub.BaseIT
import com.justai.khub.api.fe.model.ProjectDTO
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.project.validator.ProjectValidator.Companion.MAX_PROJECT_NAME_LENGTH
import com.justai.khub.util.AuthUtils
import com.justai.khub.util.TestDataFactory
import org.hamcrest.CoreMatchers.containsString
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

class ProjectIT : BaseIT() {

    var newFirstUserProject: ProjectDTO? = null
    var newSecondUserProject: ProjectDTO? = null

    @Test
    fun testCRUD() {
        val firstUser = AuthUtils.mockCCUser()
        newFirstUserProject = projectHelper.createProject(ccAuth = firstUser)

        val secondUser = AuthUtils.mockCCUser()
        newSecondUserProject = projectHelper.createProject(ccAuth = secondUser)
        val foundProject = projectHelper.getProjectAndExpectOk(newSecondUserProject!!.id, secondUser)
        assertEquals(newSecondUserProject!!.id, foundProject.id)

        val firstUserProjects = projectHelper.getProjectsPage(firstUser)
        assertTrue(firstUserProjects.content.any { it.id == newFirstUserProject!!.id })
        assertTrue(firstUserProjects.content.none { it.id == newSecondUserProject!!.id })

        projectHelper.deleteProjectAndExpectOk(newFirstUserProject!!.id, firstUser)
        val updatedFirstUserProjects = projectHelper.getProjectsPage(firstUser)
        assertTrue(updatedFirstUserProjects.content.none { it.id == newFirstUserProject!!.id })
    }

    @Test
    fun testValidationOnCreate() {
        val validRequest = TestDataFactory.projectCreateRequest()
        projectHelper.tryCreateProject(validRequest.copy(name = ""))
            .andExpect(status().isBadRequest)
            .andExpect(jsonPath("$.error", containsString(ApiErrorCode.EMPTY_PROJECT_NAME.code)))
        projectHelper.tryCreateProject(validRequest.copy(name = " "))
            .andExpect(status().isBadRequest)
            .andExpect(jsonPath("$.error", containsString(ApiErrorCode.EMPTY_PROJECT_NAME.code)))
        projectHelper.tryCreateProject(validRequest.copy(name = "a".repeat(MAX_PROJECT_NAME_LENGTH + 1)))
            .andExpect(status().isBadRequest)
            .andExpect(jsonPath("$.error", containsString(ApiErrorCode.PROJECT_NAME_TOO_LONG.code)))
        for (char in "!@#$%^&*()_<>?,./'") {
            projectHelper.tryCreateProject(validRequest.copy(name = "$char"))
                .andExpect(status().isBadRequest)
                .andExpect(jsonPath("$.error", containsString(ApiErrorCode.PROJECT_NAME_INVALID_FORMAT.code)))
        }
    }

    @AfterEach
    fun cleanup() {
        newFirstUserProject?.let { projectHelper.deleteProjectRepository(it) }
        newSecondUserProject?.let { projectHelper.deleteProjectRepository(it) }
    }
}
