package com.justai.khub.ratelimit.filter

import com.justai.khub.common.configuration.SecurityConfiguration.Companion.INTERNAL_URL_PREFIX
import com.justai.khub.common.configuration.SecurityConfiguration.Companion.PUBLIC_URL_PREFIX
import com.justai.khub.common.configuration.service.ApiErrorHandler
import com.justai.khub.common.dto.ApiError
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.util.CustomHeaders.X_LICENSE_RATE_LIMIT_LIMIT
import com.justai.khub.common.util.CustomHeaders.X_LICENSE_RATE_LIMIT_REMAINING
import com.justai.khub.common.util.CustomHeaders.X_LICENSE_RATE_LIMIT_RESET_MS
import com.justai.khub.common.util.CustomHeaders.X_LICENSE_RETRY_AFTER_MS
import com.justai.khub.ratelimit.service.LicensingService
import com.justai.onprem.license.KHubModuleV1
import com.justai.onprem.license.keyIdTag
import com.justai.onprem.license.moduleMeterNamePrefix
import io.github.bucket4j.Bucket
import io.github.bucket4j.ConsumptionProbe
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Tags
import jakarta.annotation.PostConstruct
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.http.HttpMethod
import org.springframework.security.web.util.matcher.AntPathRequestMatcher
import org.springframework.stereotype.Component
import org.springframework.web.filter.OncePerRequestFilter
import java.util.concurrent.TimeUnit.NANOSECONDS
import java.util.concurrent.atomic.AtomicLong

@Component
class GlobalRateLimitingFilter(
    private val licensingService: LicensingService,
    private val meterRegistry: MeterRegistry,
    private val globalRateLimitingBucket: Bucket
) : OncePerRequestFilter() {
    val protectedEndpointsMatchers = listOf(
        // retrieve
        AntPathRequestMatcher("${INTERNAL_URL_PREFIX}/projects/*/retrieve", HttpMethod.POST.name()),
        AntPathRequestMatcher("${INTERNAL_URL_PREFIX}/projects/*/chat/default/retrieve", HttpMethod.POST.name()),
        AntPathRequestMatcher("${PUBLIC_URL_PREFIX}/retrieve", HttpMethod.POST.name()),
        AntPathRequestMatcher("${PUBLIC_URL_PREFIX}/chat/*/retrieve", HttpMethod.POST.name()),
        // generate internal
        AntPathRequestMatcher("${INTERNAL_URL_PREFIX}/projects/*/chat/*", HttpMethod.POST.name()),
        // generate public
        AntPathRequestMatcher("${PUBLIC_URL_PREFIX}/chat/*/query", HttpMethod.POST.name()),
        AntPathRequestMatcher("${PUBLIC_URL_PREFIX}/async/chat/*/query", HttpMethod.POST.name()),
        AntPathRequestMatcher("${PUBLIC_URL_PREFIX}/query", HttpMethod.POST.name()),
        AntPathRequestMatcher("${PUBLIC_URL_PREFIX}/async/query", HttpMethod.POST.name()),
    )

    private val remainingTokensGauge = AtomicLong(licensingService.apiRequestsRpm)

    @PostConstruct
    fun init() {
        if (licensingService.hasActiveKey()) {
            val gaugeName = "$moduleMeterNamePrefix.${KHubModuleV1.MODULE_NAME}.api_requests_rpm_remaining_requests"
            val tags = Tags.of(keyIdTag, licensingService.keyId)
            meterRegistry.gauge(gaugeName, tags, remainingTokensGauge)
        }
    }

    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain
    ) {
        if (protectedEndpointsMatchers.none { it.matches(request) }) {
            filterChain.doFilter(request, response)
            return
        }
        if (!licensingService.hasActiveKey()) {
            return writeError(request, response, ApiErrorCode.INVALID_LICENSE_KEY)
        }
        val probe = globalRateLimitingBucket.tryConsumeAndReturnRemaining(1)
        remainingTokensGauge.set(probe.remainingTokens)
        addRateLimitHeaders(response, probe)
        if (probe.isConsumed) {
            filterChain.doFilter(request, response)
        } else {
            return writeError(request, response, ApiErrorCode.TOO_MANY_REQUESTS)
        }
    }

    private fun writeError(request: HttpServletRequest, response: HttpServletResponse, code: ApiErrorCode) {
        ApiErrorHandler.writeErrorToResponse(
            errors = listOf(ApiError(code)),
            response = response,
            method = request.method,
            requestUrl = request.requestURI
        )
    }

    private fun addRateLimitHeaders(response: HttpServletResponse, probe: ConsumptionProbe) {
        response.addHeader(X_LICENSE_RATE_LIMIT_LIMIT, licensingService.apiRequestsRpm.toString())
        response.addHeader(X_LICENSE_RATE_LIMIT_REMAINING, probe.remainingTokens.toString())
        response.addHeader(X_LICENSE_RATE_LIMIT_RESET_MS, NANOSECONDS.toMillis(probe.nanosToWaitForReset).toString())
        if (!probe.isConsumed) {
            response.addHeader(X_LICENSE_RETRY_AFTER_MS, NANOSECONDS.toMillis(probe.nanosToWaitForRefill).toString())
        }
    }
}
