package com.justai.khub.common.mapper

import com.justai.khub.api.public.model.*
import com.justai.khub.api.public.model.RerankerType
import com.justai.khub.common.configuration.dto.RagProperties
import com.justai.khub.common.service.MessageService
import com.justai.khub.project.dto.*
import org.springframework.stereotype.Component

@Component
class SearchConfigMapper(
    private val ragProperties: RagProperties
) {

    fun RetrievingSettings.toSearchSettings(defaultSearchPipelineConfig: SearchPipelineConfig, withHistory: Boolean? = null): SearchPipelineConfig {
        return when (this.pipeline) {
            PipelineType.AGENT -> AgentSearchPipelineConfig(
                dao = defaultSearchPipelineConfig.dao,
                llm = defaultSearchPipelineConfig.llm.copy(
                    llmProvider = this.llm?.model?.let { llmProviderByModel(it) } ?: defaultSearchPipelineConfig.llm.llmProvider,
                    model = this.llm?.model?.let { validateAndGetLlmModel(it) } ?: defaultSearchPipelineConfig.llm.model,
                    modelId = defaultSearchPipelineConfig.llm.modelId,
                    accountId = defaultSearchPipelineConfig.llm.accountId,
                    frequencyPenalty = this.llm?.frequencyPenalty
                        ?: defaultSearchPipelineConfig.llm.frequencyPenalty,
                    maxTokens = this.llm?.maxTokens ?: defaultSearchPipelineConfig.llm.maxTokens,
                    presencePenalty = this.llm?.presencePenalty
                        ?: defaultSearchPipelineConfig.llm.presencePenalty,
                    temperature = this.llm?.temperature ?: defaultSearchPipelineConfig.llm.temperature,
                    topP = this.llm?.topP ?: defaultSearchPipelineConfig.llm.topP,
                    contextWindow = this.llm?.contextWindow
                        ?: defaultSearchPipelineConfig.llm.contextWindow
                ),
                search = RetrievingConfig(
                    similarityTopK = this.search?.similarityTopK
                        ?: defaultSearchPipelineConfig.search?.similarityTopK ?: 5,
                    numCandidates = this.search?.similarityTopK?.let { it * 10 }
                        ?: defaultSearchPipelineConfig.search?.numCandidates ?: 50,
                    candidateRadius = this.search?.candidateRadius
                        ?: defaultSearchPipelineConfig.search?.candidateRadius ?: 0,
                    useHistory = withHistory ?: defaultSearchPipelineConfig.search?.useHistory,
                    historyCondensePrompt = defaultSearchPipelineConfig.search?.historyCondensePrompt,
                    historyMaxTokens = defaultSearchPipelineConfig.search?.historyMaxTokens,
                    historyMinUserMessages = defaultSearchPipelineConfig.search?.historyMinUserMessages,
                    rephraseQuery = this.search?.rephraseUserQuery != null || true == defaultSearchPipelineConfig.search?.rephraseQuery,
                    queryRephrasePrompt = this.search?.rephraseUserQuery?.prompt,
                    useAllEmbeddings = true,
                    transformGenerativeMessage = true,
                    fullTextSearch = this.search?.fullTextSearch?.toFullTextSearchConfig(
                        defaultSearchPipelineConfig.search?.fullTextSearch
                    ) ?: defaultSearchPipelineConfig.search?.fullTextSearch
                ),
                reranker = this.search?.reranker?.toRerankerConfig() ?: defaultSearchPipelineConfig.reranker,
                abbreviations = defaultSearchPipelineConfig.abbreviations,
                systemPrompt = defaultSearchPipelineConfig.let {
                    if (it is AgentSearchPipelineConfig) {
                        it.systemPrompt
                    } else null
                }
            )

            PipelineType.SEMANTIC -> SemanticSearchPipelineConfig(dao = defaultSearchPipelineConfig.dao,
                llm = defaultSearchPipelineConfig.llm.copy(
                    llmProvider = this.llm?.model?.let { llmProviderByModel(it) } ?: defaultSearchPipelineConfig.llm.llmProvider,
                    model = this.llm?.model?.let { validateAndGetLlmModel(it) } ?: defaultSearchPipelineConfig.llm.model,
                    modelId = defaultSearchPipelineConfig.llm.modelId,
                    accountId = defaultSearchPipelineConfig.llm.accountId,
                    frequencyPenalty = this.llm?.frequencyPenalty
                        ?: defaultSearchPipelineConfig.llm.frequencyPenalty,
                    maxTokens = this.llm?.maxTokens ?: defaultSearchPipelineConfig.llm.maxTokens,
                    presencePenalty = this.llm?.presencePenalty
                        ?: defaultSearchPipelineConfig.llm.presencePenalty,
                    temperature = this.llm?.temperature ?: defaultSearchPipelineConfig.llm.temperature,
                    topP = this.llm?.topP ?: defaultSearchPipelineConfig.llm.topP,
                    contextWindow = this.llm?.contextWindow
                        ?: defaultSearchPipelineConfig.llm.contextWindow
                ),
                search = RetrievingConfig(
                    similarityTopK = this.search?.similarityTopK
                        ?: defaultSearchPipelineConfig.search?.similarityTopK ?: 5,
                    numCandidates = this.search?.similarityTopK?.let { it * 10 }
                        ?: defaultSearchPipelineConfig.search?.numCandidates ?: 50,
                    candidateRadius = this.search?.candidateRadius
                        ?: defaultSearchPipelineConfig.search?.candidateRadius ?: 0,
                    useHistory = withHistory ?: defaultSearchPipelineConfig.search?.useHistory,
                    historyCondensePrompt = defaultSearchPipelineConfig.search?.historyCondensePrompt,
                    historyMaxTokens = defaultSearchPipelineConfig.search?.historyMaxTokens,
                    historyMinUserMessages = defaultSearchPipelineConfig.search?.historyMinUserMessages,
                    rephraseQuery = this.search?.rephraseUserQuery != null || true == defaultSearchPipelineConfig.search?.rephraseQuery,
                    queryRephrasePrompt = this.search?.rephraseUserQuery?.prompt,
                    useAllEmbeddings = true,
                    transformGenerativeMessage = true,
                    fullTextSearch = this.search?.fullTextSearch?.toFullTextSearchConfig(
                        defaultSearchPipelineConfig.search?.fullTextSearch
                    ) ?: defaultSearchPipelineConfig.search?.fullTextSearch
                ),
                reranker = this.search?.reranker?.toRerankerConfig() ?: defaultSearchPipelineConfig.reranker,
                abbreviations = defaultSearchPipelineConfig.abbreviations,
                responseGenerator = defaultSearchPipelineConfig.let {
                    if (it is SemanticSearchPipelineConfig) {
                        it.responseGenerator.copy(
                            prompt = it.responseGenerator.prompt,
                        )
                    } else ResponseGeneratorConfig()
                })
        }
    }

    fun RagSettings.toSearchSettings(defaultSearchPipelineConfig: SearchPipelineConfig, withHistory: Boolean? = null): SearchPipelineConfig {
        return when (this.pipeline) {
            PipelineType.AGENT -> AgentSearchPipelineConfig(
                dao = defaultSearchPipelineConfig.dao,
                llm = defaultSearchPipelineConfig.llm.copy(
                    llmProvider = this.llm?.model?.let { llmProviderByModel(it) } ?: defaultSearchPipelineConfig.llm.llmProvider,
                    model = this.llm?.model?.let { validateAndGetLlmModel(it) } ?: defaultSearchPipelineConfig.llm.model,
                    modelId = defaultSearchPipelineConfig.llm.modelId,
                    accountId = defaultSearchPipelineConfig.llm.accountId,
                    frequencyPenalty = this.llm?.frequencyPenalty
                        ?: defaultSearchPipelineConfig.llm.frequencyPenalty,
                    maxTokens = this.llm?.maxTokens ?: defaultSearchPipelineConfig.llm.maxTokens,
                    presencePenalty = this.llm?.presencePenalty
                        ?: defaultSearchPipelineConfig.llm.presencePenalty,
                    temperature = this.llm?.temperature ?: defaultSearchPipelineConfig.llm.temperature,
                    topP = this.llm?.topP ?: defaultSearchPipelineConfig.llm.topP,
                    contextWindow = this.llm?.contextWindow
                        ?: defaultSearchPipelineConfig.llm.contextWindow
                ),
                search = RetrievingConfig(
                    similarityTopK = this.search?.similarityTopK
                        ?: defaultSearchPipelineConfig.search?.similarityTopK ?: 5,
                    numCandidates = this.search?.similarityTopK?.let { it * 10 }
                        ?: defaultSearchPipelineConfig.search?.numCandidates ?: 50,
                    candidateRadius = this.search?.candidateRadius
                        ?: defaultSearchPipelineConfig.search?.candidateRadius ?: 0,
                    useHistory = withHistory ?: defaultSearchPipelineConfig.search?.useHistory,
                    historyCondensePrompt = defaultSearchPipelineConfig.search?.historyCondensePrompt,
                    historyMaxTokens = defaultSearchPipelineConfig.search?.historyMaxTokens,
                    historyMinUserMessages = defaultSearchPipelineConfig.search?.historyMinUserMessages,
                    rephraseQuery = this.search?.rephraseUserQuery != null || true == defaultSearchPipelineConfig.search?.rephraseQuery,
                    queryRephrasePrompt = this.search?.rephraseUserQuery?.prompt,
                    useAllEmbeddings = true,
                    transformGenerativeMessage = true,
                    fullTextSearch = this.search?.fullTextSearch?.toFullTextSearchConfig(
                        defaultSearchPipelineConfig.search?.fullTextSearch
                    ) ?: defaultSearchPipelineConfig.search?.fullTextSearch
                ),
                reranker = this.search?.reranker?.toRerankerConfig() ?: defaultSearchPipelineConfig.reranker,
                abbreviations = defaultSearchPipelineConfig.abbreviations,
                showSources = this.responseGeneration?.showRelevantSources ?: defaultSearchPipelineConfig.showSources,
                systemPrompt = this.responseGeneration?.prompt)

            PipelineType.SEMANTIC -> SemanticSearchPipelineConfig(dao = defaultSearchPipelineConfig.dao,
                llm = defaultSearchPipelineConfig.llm.copy(
                    llmProvider = this.llm?.model?.let { llmProviderByModel(it) } ?: defaultSearchPipelineConfig.llm.llmProvider,
                    model = this.llm?.model?.let { validateAndGetLlmModel(it) } ?: defaultSearchPipelineConfig.llm.model,
                    modelId = defaultSearchPipelineConfig.llm.modelId,
                    accountId = defaultSearchPipelineConfig.llm.accountId,
                    frequencyPenalty = this.llm?.frequencyPenalty
                        ?: defaultSearchPipelineConfig.llm.frequencyPenalty,
                    maxTokens = this.llm?.maxTokens ?: defaultSearchPipelineConfig.llm.maxTokens,
                    presencePenalty = this.llm?.presencePenalty
                        ?: defaultSearchPipelineConfig.llm.presencePenalty,
                    temperature = this.llm?.temperature ?: defaultSearchPipelineConfig.llm.temperature,
                    topP = this.llm?.topP ?: defaultSearchPipelineConfig.llm.topP,
                    contextWindow = this.llm?.contextWindow
                        ?: defaultSearchPipelineConfig.llm.contextWindow
                ),
                search = RetrievingConfig(
                    similarityTopK = this.search?.similarityTopK
                        ?: defaultSearchPipelineConfig.search?.similarityTopK ?: 5,
                    numCandidates = this.search?.similarityTopK?.let { it * 10 }
                        ?: defaultSearchPipelineConfig.search?.numCandidates ?: 50,
                    candidateRadius = this.search?.candidateRadius
                        ?: defaultSearchPipelineConfig.search?.candidateRadius ?: 0,
                    useHistory = withHistory ?: defaultSearchPipelineConfig.search?.useHistory,
                    historyCondensePrompt = defaultSearchPipelineConfig.search?.historyCondensePrompt,
                    historyMaxTokens = defaultSearchPipelineConfig.search?.historyMaxTokens,
                    historyMinUserMessages = defaultSearchPipelineConfig.search?.historyMinUserMessages,
                    rephraseQuery = this.search?.rephraseUserQuery != null || true == defaultSearchPipelineConfig.search?.rephraseQuery,
                    queryRephrasePrompt = this.search?.rephraseUserQuery?.prompt,
                    useAllEmbeddings = true,
                    transformGenerativeMessage = true,
                    fullTextSearch = this.search?.fullTextSearch?.toFullTextSearchConfig(
                        defaultSearchPipelineConfig.search?.fullTextSearch
                    ) ?: defaultSearchPipelineConfig.search?.fullTextSearch
                ),
                reranker = this.search?.reranker?.toRerankerConfig() ?: defaultSearchPipelineConfig.reranker,
                abbreviations = defaultSearchPipelineConfig.abbreviations,
                showSources = this.responseGeneration?.showRelevantSources ?: defaultSearchPipelineConfig.showSources,
                responseGenerator = defaultSearchPipelineConfig.let {
                    if (it is SemanticSearchPipelineConfig) {
                        it.responseGenerator.copy(
                            prompt = this.responseGeneration?.prompt,
                            mode = if (this.responseGeneration?.showRelevantSources == true) {
                                ResponseMode.compact_with_relevant_sources
                            }  else it.responseGenerator.mode
                        )
                    } else ResponseGeneratorConfig(
                        prompt = this.responseGeneration?.prompt,
                        mode = if (this.responseGeneration?.showRelevantSources == true) {
                            ResponseMode.compact_with_relevant_sources
                        } else ResponseMode.compact
                    )
                })
        }
    }

    fun SearchPipelineConfig.toRagSettings(): RagSettings {
        return when (this) {
            is AgentSearchPipelineConfig -> RagSettings(
                pipeline = PipelineType.AGENT,
                search = SearchSettings(
                    similarityTopK = this.search!!.similarityTopK,
                    candidateRadius = this.search?.candidateRadius,
                    rephraseUserQuery = if (true == this.search?.rephraseQuery) {
                        Rephrase(
                            prompt = this.search?.queryRephrasePrompt ?: ""
                        )
                    } else null,
                    reranker = this.reranker?.toReranker(),
                    fullTextSearch = this.search?.fullTextSearch?.toFullTextSearch()
                ),
                llm = LlmSettings(
                    model = ragProperties.llm.models.find { it.value == this.llm.model }?.title,
                    contextWindow = this.llm.contextWindow,
                    maxTokens = this.llm.maxTokens,
                    temperature = this.llm.temperature,
                    topP = this.llm.topP,
                    frequencyPenalty = this.llm.frequencyPenalty,
                    presencePenalty = this.llm.presencePenalty
                ),
                responseGeneration = ResponseGeneration(
                    prompt = this.systemPrompt,
                    showRelevantSources = this.showSources
                )
            )

            is SemanticSearchPipelineConfig -> RagSettings(
                pipeline = PipelineType.SEMANTIC,
                search = SearchSettings(
                    similarityTopK = this.search!!.similarityTopK,
                    candidateRadius = this.search?.candidateRadius,
                    rephraseUserQuery = if (true == this.search?.rephraseQuery) {
                        Rephrase(
                            prompt = this.search?.queryRephrasePrompt ?: ""
                        )
                    } else null,
                    reranker = this.reranker?.toReranker(),
                    fullTextSearch = this.search?.fullTextSearch?.toFullTextSearch()
                ),
                llm = LlmSettings(
                    model = ragProperties.llm.models.find { it.value == this.llm.model }?.title,
                    contextWindow = this.llm.contextWindow,
                    maxTokens = this.llm.maxTokens,
                    temperature = this.llm.temperature,
                    topP = this.llm.topP,
                    frequencyPenalty = this.llm.frequencyPenalty,
                    presencePenalty = this.llm.presencePenalty
                ),
                responseGeneration = ResponseGeneration(
                    prompt = this.responseGenerator.prompt,
                    showRelevantSources = this.showSources
                )
            )
        }
    }

    private fun llmProviderByModel(model: String): LLMProvider {
        return ragProperties.llm.models.find { it.title == model }?.value?.let {
            when {
                it.contains(LLMProvider.openai.name) -> LLMProvider.openai
                else -> LLMProvider.mlp
            }
        } ?: throw IllegalArgumentException("Unsupported model type $model")
    }


    private fun validateAndGetLlmModel(model: String): String {
        return ragProperties.llm.models.find { it.title == model }?.value
            ?: throw IllegalArgumentException("Unsupported model type $model")
    }

    private fun Reranker.toRerankerConfig(): RerankerConfig {
        return when (this.type) {
            RerankerType.MANUAL -> ManualRerankerConfig(
                minScore = this.minScore?.toInt() ?: -5,
                maxChunks = this.maxChunks ?: 5,
                scoreReductionLimit = this.scoreReductionLimit ?: 20,
                maxChunksPerDoc = this.maxChunksPerDocument ?: 3
            )
            RerankerType.MODEL -> ModelRerankerConfig(
                minScore = this.minScore?.toInt() ?: -5,
                maxChunks = this.maxChunks ?: 5,
                model = "BAAI/bge-reranker-v2-m3"
            )
        }
    }

    private fun RerankerConfig.toReranker(): Reranker? {
        if (this.type == com.justai.khub.project.dto.RerankerType.noop) {
            return null
        }
        return Reranker(
            type = RerankerType.entries.first { it.value.uppercase() == this.type.name.uppercase() },
            minScore = this.minScore.toDouble(),
            maxChunks = when (this) {
                is ModelRerankerConfig -> this.maxChunks
                is ManualRerankerConfig -> this.maxChunks
                else -> null
            },
            maxChunksPerDocument = when (this) {
                is ManualRerankerConfig -> this.maxChunksPerDoc
                else -> null
            },
            scoreReductionLimit = when (this) {
                is ManualRerankerConfig -> this.scoreReductionLimit
                else -> null
            }
        )
    }

    private fun FullTextSearch.toFullTextSearchConfig(default: FullTextSearchConfig?): FullTextSearchConfig {
        return FullTextSearchConfig(
            ftSearch = true,
            strategy = SearchStrategy.valueOf(this.strategy.value),
            semanticPortion = this.semanticPortion ?: default?.semanticPortion,
            ftsPortion = this.ftsPortion ?: default?.ftsPortion,
            threshold = this.threshold ?: default?.threshold,
            useOnlyInSegment = this.useOnlyInSegment
        )
    }

    private fun FullTextSearchConfig.toFullTextSearch(): FullTextSearch? {
        return if (this.ftSearch) {
            return FullTextSearch(
                strategy = FullTextSearchStrategy.entries.first { it.value.uppercase() == this.strategy.name.uppercase() },
                semanticPortion = this.semanticPortion,
                ftsPortion = this.ftsPortion,
                threshold = this.threshold,
                useOnlyInSegment = this.useOnlyInSegment
            )
        } else null
    }
}
