package com.justai.khub.test

import com.justai.khub.api.model.*
import com.justai.khub.base.BaseBBT
import com.justai.khub.util.BBTContextExtensions.createProjectWithFile
import com.justai.khub.util.BBTContextExtensions.generateTestSetSync
import com.justai.khub.util.BBTContextExtensions.ingestSync
import com.justai.khub.util.BBTContextExtensions.runTestSetSync
import com.justai.khub.util.TestDataGenerator
import org.apache.poi.ss.usermodel.WorkbookFactory
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.HttpStatus
import org.springframework.web.client.HttpClientErrorException
import java.io.ByteArrayInputStream

class QABBT : BaseBBT() {

    @Test
    fun testGetDefaultTestSetSettings() = withDefaultAuth {
        val project = createProjectWithFile()
        val defaultSettings = qaApi.getDefaultTestSetSettings(project.id)
        assertNotNull(defaultSettings.maxTests)
        assertNotNull(defaultSettings.testsPerDocument)
        assertNotNull(defaultSettings.evaluationModel)
        assertNotNull(defaultSettings.generationModel)
        // prompts can be null due to missing local features
    }

    @Test
    fun testUpdate() = withDefaultAuth {
        val project = createProjectWithFile()
        val testSetFile = TestDataGenerator.testFile("testSet.xlsx")
        val testSet = qaApi.uploadTestSet(project.id, testSetFile, "manual-test-set")
        assertEquals("manual-test-set", testSet.name)
        val updatedTestSet = qaApi.updateTestSet(project.id, testSet.id, TestSetUpdateRequest().name("updated-name"))
        assertEquals("updated-name", updatedTestSet.name)
    }

    @Test
    fun testUploadTestCase() = withDefaultAuth {
        val project = createProjectWithFile()
        val testSetFile = TestDataGenerator.testFile("testSet.xlsx")
        val testSet = qaApi.uploadTestSet(project.id, testSetFile, "manual-test-set")
        assertEquals(TestSetDTO.StatusEnum.ACTIVE, testSet.status)
        assertEquals(TestSetDTO.TypeEnum.MANUAL, testSet.type)

        val testSetsPage = qaApi.getTestSetsPage(project.id)
        assertTrue(testSetsPage.content.any { it.id == testSet.id })
    }

    @Test
    fun testUploadWrongFormat() = withDefaultAuth {
        val project = createProjectWithFile()
        val testSetFile = TestDataGenerator.testFile("test.md")
        val ex = assertThrows<HttpClientErrorException> { qaApi.uploadTestSet(project.id, testSetFile, "manual-test-set") }
        assertEquals(HttpStatus.BAD_REQUEST, ex.statusCode)
    }

    @Test
    fun testCustomOrder() = withDefaultAuth {
        val project = createProjectWithFile()
        val updatedProject = ingestSync(project.id)
        assertEquals("ACTIVE", updatedProject.status)
        val testSetFile = TestDataGenerator.testFile("testSet.xlsx")
        val firstTestSet = qaApi.uploadTestSet(project.id, testSetFile, "first")
        val secondTestSet = qaApi.uploadTestSet(project.id, testSetFile, "second")

        // sorted by createdAt DESC by default
        var page = qaApi.getTestSetsPage(project.id).content
        assertEquals(secondTestSet.id, page.first().id)
        assertEquals(firstTestSet.id, page.last().id)

        // applied custom order
        qaApi.setTestSetsOrder(project.id, TestSetOrderDTO().ids(listOf(firstTestSet.id, secondTestSet.id)))
        page = qaApi.getTestSetsPage(project.id).content
        assertEquals(firstTestSet.id, page.first().id)
        assertEquals(secondTestSet.id, page.last().id)

        // new testSets are added to the head of the list
        val thirdTestSet = qaApi.uploadTestSet(project.id, testSetFile, "third")
        page = qaApi.getTestSetsPage(project.id).content
        assertEquals(thirdTestSet.id, page.first().id)
    }

    @Test
    fun testArchive() = withDefaultAuth {
        val project = createProjectWithFile()
        val updatedProject = ingestSync(project.id)
        assertEquals("ACTIVE", updatedProject.status)
        val testSetGenerationRequest = GenerateTestSetRequest().name("generated test set").testsPerDoc(10)

        val testSet = qaApi.generateTestSet(project.id, testSetGenerationRequest)
        var notArchivedTestSets = qaApi.getTestSetsPage(project.id)
        assertTrue(notArchivedTestSets.content.any { it.id == testSet.id })
        var archivedTestSets = qaApi.getArchivedTestSetsPage(project.id, 10, 0)
        assertFalse(archivedTestSets.content.any { it.id == testSet.id })

        qaApi.archiveTestSet(project.id, testSet.id)
        notArchivedTestSets = qaApi.getTestSetsPage(project.id)
        assertFalse(notArchivedTestSets.content.any { it.id == testSet.id })
        archivedTestSets = qaApi.getArchivedTestSetsPage(project.id, 10, 0)
        assertTrue(archivedTestSets.content.any { it.id == testSet.id })

        qaApi.unArchiveTestSet(project.id, testSet.id)
        notArchivedTestSets = qaApi.getTestSetsPage(project.id)
        assertTrue(notArchivedTestSets.content.any { it.id == testSet.id })
        archivedTestSets = qaApi.getArchivedTestSetsPage(project.id, 10, 0)
        assertFalse(archivedTestSets.content.any { it.id == testSet.id })
    }

    @Test
    fun testDownloadTestSetSample() = withDefaultAuth {
        val result = qaApi.downloadTestSetSample(123) as ByteArrayResource
        assertFalse(result.byteArray.isEmpty())
    }

    @Test
    fun testGenerateTestSetAndRun() = withDefaultAuth {
        val project = createProjectWithFile()
        val updatedProject = ingestSync(project.id)
        assertEquals("ACTIVE", updatedProject.status)

        val model = qaApi.getTestSetGenerationModels(project.id).firstOrNull()
        assertNotNull(model, "Missing model for test set generation")
        val testSetGenerationRequest = GenerateTestSetRequest().generationModel(model!!.value).name("generated test set").testsPerDoc(10)
        val testSet = generateTestSetSync(project.id, request = testSetGenerationRequest)
        assertEquals(TestSetDTO.StatusEnum.ACTIVE, testSet.status)
        assertEquals(testSetGenerationRequest.testsPerDoc, testSet.totalTests)

        val testSetReport = qaApi.downloadTestSet(project.id, testSet.id).contentAsByteArray
        validateTestSetReport(testSetReport, testSet.totalTests)

        val testSetRun = runTestSetSync(project.id, testSet.id, autoEvaluate = true)
        assertEquals(TestSetRunDTO.StatusEnum.FINISHED, testSetRun.status)
        assertEquals(testSetGenerationRequest.testsPerDoc, testSetRun.finishedTests)
        assertEquals(testSetGenerationRequest.testsPerDoc, testSetRun.totalTests)

        val testRunReport = qaApi.getTestSetRunReport(project.id, testSetRun.id).contentAsByteArray
        validateTestRunReport(testRunReport)
    }

    private fun validateTestSetReport(report: ByteArray, expectedTestsCount: Int) {
        val workbook = WorkbookFactory.create(ByteArrayInputStream(report))
        assertEquals(1, workbook.numberOfSheets)
        val rowsCount = workbook.getSheetAt(0).physicalNumberOfRows
        assertEquals(expectedTestsCount + 1, rowsCount)
    }

    private fun validateTestRunReport(report: ByteArray) {
        val workbook = WorkbookFactory.create(ByteArrayInputStream(report))
        assertEquals(2, workbook.numberOfSheets)
        assertEquals("Summary", workbook.getSheetName(0))
        assertEquals("Details", workbook.getSheetName(1))
    }
}
