package com.justai.khub.chat

import com.justai.khub.BaseProjectIT
import com.justai.khub.api.fe.model.SourceDTO
import com.justai.khub.chat.enumeration.ChatHistoryRecordStatus
import com.justai.khub.project.dto.*
import com.justai.khub.project.mapper.ProjectSettingsMapper
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class ChatIT : BaseProjectIT() {
    private lateinit var projectSources: List<SourceDTO>

    @Autowired
    private lateinit var projectSettingsMapper: ProjectSettingsMapper

    private val rawAbbreviations: String = """
        {
            "ИНН": {
                "name": "Идентификационный номер налогоплательщика",
                "variations": [],
                "description": "Налоговый номер"
            },
            "PIN-код": {
                "name": "PIN-код",
                "variations": ["ПИН код", "пин код", "ПИ<PERSON> кот", "пин кот"],
                "description": "Код для карты"
            }
        }
    """.trimIndent()

    @BeforeEach
    fun init() {
        projectSources = listOf(
            projectFileHelper.uploadFile(projectId = project.id, fileName = "doc1.docx", fileContent = "тест1"),
            projectFileHelper.uploadFile(projectId = project.id, fileName = "doc2.docx", fileContent = "тест2")
        )
    }

    @Test
    fun testCRUD() {
        var chatsPage = chatHelper.getChatsPage(project.id)
        assertFalse(chatsPage.content.isEmpty()) // default chat must be created with project

        val newChat = chatHelper.createChat(project.id)
        assertNotNull(newChat.name)

        chatsPage = chatHelper.getChatsPage(project.id)
        assertFalse(chatsPage.content.isEmpty())

        var chatHistory = chatHelper.getChatHistoryAndExpectOk(project.id, newChat.id)
        assertFalse(chatHistory.content.isEmpty())

        val newRecord = chatHelper.makeRequestAndExpectOk(project.id, newChat.id, "Test message")
        assertEquals(ChatHistoryRecordStatus.READY_TO_PROCESS.toString(), newRecord.status)

        chatHistory = chatHelper.getChatHistoryAndExpectOk(project.id, newChat.id)
        assertFalse(chatHistory.content.isEmpty())

        val canceledRecord = chatHelper.cancelRecordProcessing(project.id, newChat.id, newRecord.id)
        assertTrue(listOf(ChatHistoryRecordStatus.CANCELED.toString(), ChatHistoryRecordStatus.FINISHED.toString()).contains(canceledRecord.status))

        val foundRecord = chatHelper.getChatRecord(project.id, newChat.id, newRecord.id)
        assertEquals(ChatHistoryRecordStatus.CANCELED.toString(), foundRecord.status)

        chatHistory = chatHelper.clearChatHistory(project.id, newChat.id)
        assertFalse(chatHistory.content.isEmpty()) // expect initial message to be present
    }

    @Test
    fun testDefaultChat() {
        var chatsPage = chatHelper.getChatsPage(project.id)
        assertFalse(chatsPage.content.isEmpty())

        val record = chatHelper.makeRequestToDefaultChatAndExpectOk(project.id, "Test message")
        assertNotNull(record.chatId)

        chatsPage = chatHelper.getChatsPage(project.id)
        assertFalse(chatsPage.content.isEmpty())

        chatHelper.cancelRecordProcessing(project.id, record.chatId, record.id)
        val secondRecord = chatHelper.makeRequestToDefaultChatAndExpectOk(project.id, "Test message")
        assertNotEquals(record.id, secondRecord.id)

        var chatHistory = chatHelper.getDefaultChatHistory(project.id)
        assertFalse(chatHistory.content.isEmpty())

        chatHistory = chatHelper.clearDefaultChatHistory(project.id)
        assertFalse(chatHistory.content.isEmpty()) // expect initial message to be present
    }

    @Test
    fun testDefaultChatSettings() {
        val initialSettings = chatHelper.getDefaultChatSettings(project.id)
        assertFalse(initialSettings.partitions.isEmpty())
        val defaultSettings = projectSettingsMapper.defaultSettings("unused")
        val settingsUpdateRequest = defaultSettings.copy(
            indexation = null,
            search = defaultSettings.search.copy(
                retrieving = defaultSettings.search.retrieving.copy(
                    fullText = FullTextSearchConfig(
                        strategy = SearchStrategy.weighted
                    )
                ),
                abbreviations = UIFileData("dummy.json", rawAbbreviations)
            ),
            generation = defaultSettings.generation.copy(llm = LLMConfig(contextWindow = 1234))
        )
        chatHelper.setDefaultChatSettings(project.id, settingsUpdateRequest)

        val updatedSettings = chatHelper.getDefaultChatSettings(project.id)
        val contextWindowSetting = updatedSettings.partitions.firstOrNull { it.name == "generation" }
            ?.sections?.getOrDefault("llm", null)
            ?.settings?.getOrDefault("context_window", defaultValue = null) as? SliderSettingDescription
        assertEquals(settingsUpdateRequest.generation.llm.contextWindow, contextWindowSetting?.value?.toInt())
        val ftsSetting = updatedSettings.partitions.firstOrNull { it.name == "search" }
            ?.sections?.getOrDefault("retrieving", null)
            ?.settings?.getOrDefault("full_text", defaultValue = null) as? BlockSettingDescription
        val ftsStrategy = ftsSetting?.nestedSettings?.getOrDefault("strategy", defaultValue = null) as? CardsSettingDescription
        assertEquals(settingsUpdateRequest.search.retrieving.fullText?.strategy.toString(), ftsStrategy?.value)
        val abbreviationsSetting = updatedSettings.partitions.firstOrNull { it.name == "search" }
            ?.sections?.getOrDefault("abbreviations", null)
            ?.settings?.getOrDefault("data", defaultValue = null) as? FileSettingDescription
        assertEquals(settingsUpdateRequest.search.abbreviations?.fileName, abbreviationsSetting?.fileName)
        assertNotNull(abbreviationsSetting?.rawData)
    }
}
