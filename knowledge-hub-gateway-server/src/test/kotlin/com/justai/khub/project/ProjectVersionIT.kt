package com.justai.khub.project

import com.justai.khub.BaseProjectIT
import com.justai.khub.common.configuration.dto.IngestProperties
import com.justai.khub.common.util.JSON
import com.justai.khub.project.dto.*
import com.justai.khub.project.mapper.ProjectSettingsMapper
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class ProjectVersionIT : BaseProjectIT() {

    @Autowired
    private lateinit var projectSettingsMapper: ProjectSettingsMapper

    @Autowired
    private lateinit var ingestProperties: IngestProperties


    @Test
    fun testDefaultVersionSettings() {
        val initialSettings = projectHelper.getDefaultVersionSettingsAndExpectOk(project.id)
        assertEquals(3, initialSettings.partitions.size)
        val defaultSettings = projectSettingsMapper.defaultSettings("unused")
        val settingsUpdateRequest = defaultSettings.copy(
            indexation = defaultSettings.indexation?.copy(
                chunker = ChunkerConfig(chunkSize = 1234)
            ),
            search = defaultSettings.search.copy(
                retrieving = defaultSettings.search.retrieving.copy(similarityTopK = 12)
            )
        )

        projectHelper.setDefaultVersionSettings(project.id, settingsUpdateRequest)

        val updatedSettings = projectHelper.getDefaultVersionSettingsAndExpectOk(project.id)
        val chunkSizeSetting = updatedSettings.partitions.firstOrNull { it.name == "indexation" }
            ?.sections?.getOrDefault("chunker", null)?.settings?.getOrDefault("chunk_size", null) as? SliderSettingDescription
        val languageSetting = updatedSettings.partitions.firstOrNull { it.name == "indexation" }
            ?.sections?.getOrDefault("chunker", null)?.settings?.getOrDefault("language", null) as? CardsSettingDescription
        val vectorizerSetting = updatedSettings.partitions.firstOrNull { it.name == "indexation" }
            ?.sections?.getOrDefault("main", null)?.settings?.getOrDefault("type", null) as? CardsSettingDescription
        val similarityTopkSettings = updatedSettings.partitions.firstOrNull { it.name == "search" }
            ?.sections?.getOrDefault("retrieving", null)?.settings?.getOrDefault("similarity_top_k", null) as? SliderSettingDescription
        assertEquals(settingsUpdateRequest.indexation!!.chunker.chunkSize, chunkSizeSetting?.value?.toInt())
        assertEquals(settingsUpdateRequest.indexation!!.type, vectorizerSetting?.value)
        assertEquals(ingestProperties.vectorizer.options.size, vectorizerSetting?.options?.mapNotNull { it.img }?.size)
        assertEquals(settingsUpdateRequest.search.retrieving.similarityTopK, similarityTopkSettings?.value?.toInt())

        val foundEnglishLanguage = languageSetting?.options?.firstOrNull { it.value == Language.english.toString() }
        assertNotNull(foundEnglishLanguage, "Missing english language for chunker. Settings: ${JSON.prettyStringify(languageSetting)}")
        // russian language may be missing if default locale is set to 'en'
    }

    @Test
    fun testValidateGenerationLlmWithPipelineType() {
        val initialSettings = projectHelper.getDefaultVersionSettingsAndExpectOk(project.id)
        assertEquals(3, initialSettings.partitions.size)
        val defaultSettings = projectSettingsMapper.defaultSettings("unused")
        val settingsUpdateRequest = defaultSettings.copy(
            search = defaultSettings.search.copy(
                type = SearchPipelineType.agent
            ),
            generation = defaultSettings.generation.copy(
                llm = defaultSettings.generation.llm.copy(model = "vllm-llama3.1-8b")
            )
        )

        val updatedSettings = projectHelper.setDefaultVersionSettings(project.id, settingsUpdateRequest)
        val llmModelSettings = updatedSettings.partitions.firstOrNull { it.name == "generation" }
            ?.sections?.getOrDefault("llm", null)?.settings?.getOrDefault("model", null) as? SelectSettingDescription

        assertEquals("openai/gpt-4o", llmModelSettings?.value)

        val settingsUpdateRequest2 = defaultSettings.copy(
            generation = defaultSettings.generation.copy(
                llm = defaultSettings.generation.llm.copy(model = "openai/gpt-4o-mini")
            )
        )
        val updatedSettings2 = projectHelper.setDefaultVersionSettings(project.id, settingsUpdateRequest2)
        val llmModelSettings2 = updatedSettings2.partitions.firstOrNull { it.name == "generation" }
            ?.sections?.getOrDefault("llm", null)?.settings?.getOrDefault("model", null) as? SelectSettingDescription

        assertEquals("openai/gpt-4o-mini", llmModelSettings2?.value)
    }
}
