package com.justai.khub.project.mapper

import com.justai.khub.common.configuration.dto.IngestProperties
import com.justai.khub.common.configuration.dto.RagProperties
import com.justai.khub.common.service.PromptsService
import com.justai.khub.project.dto.*
import com.justai.khub.project.entity.ProjectFileEntity
import com.justai.khub.project.mapper.ProjectSettingsMapper.Companion.DEFAULT_HF_EMBEDDINGS_DIMENSION
import com.justai.khub.project.mapper.ProjectSettingsMapper.Companion.DEFAULT_HF_MAX_SEQUENCE_LENGTH
import com.justai.khub.project.mapper.ProjectSettingsMapper.Companion.DEFAULT_NUM_CANDIDATES
import com.justai.khub.project.mapper.ProjectSettingsMapper.Companion.DEFAULT_OPENAI_EMBEDDINGS_DIMENSION
import com.justai.khub.project.mapper.ProjectSettingsMapper.Companion.DEFAULT_OPENAI_MAX_SEQUENCE_LENGTH
import com.justai.khub.project.mapper.ProjectSettingsMapper.Companion.DEFAULT_SIMILARITY_TOP_K
import com.justai.khub.query.dto.UserHistory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class RagServiceMapper(
    private val ragProperties: RagProperties,
    private val ingestProperties: IngestProperties,
    @Value("\${ingest.enrichChunks}")
    private val enrichChunks: Boolean,
    @Value("\${rag.csvProcessingEnabled}")
    private val csvProcessingEnabled: Boolean,
    private val promptsService: PromptsService
) {

    fun toRagPipelineConfig(source: SearchPipelineConfig, ingestSettings: IngestPipelineConfig): Map<String, Any?> {
        return when (source) {
            is AgentSearchPipelineConfig -> toRagPipelineConfig(source, ingestSettings)
            is SemanticSearchPipelineConfig -> toRagPipelineConfig(source, ingestSettings)
        }
    }

    private fun toRagPipelineConfig(searchSettings: SemanticSearchPipelineConfig, ingestSettings: IngestPipelineConfig): Map<String, Any?> {
        return mapOf(
            "type" to "semantic",
            "llm" to mapLLMConfig(searchSettings.llm),
            "dao" to mapDAOConfig(searchSettings.dao, ingestSettings.vectorizer),
            "search" to searchSettings.search?.let { mapSearchConfig(it) },
            "reranker" to searchSettings.reranker?.let { mapRerankerConfig(it) },
            "query_transformers_queue" to mapQueryTransformers(searchSettings.search, searchSettings.abbreviations),
            "response_generator" to mapResponseGeneratorConfig(searchSettings.responseGenerator),
            "return_content" to true,
            "csv_processor_config" to mapCsvProcessorConfig(searchSettings.csvProcessorConfig)
        )
    }

    private fun toRagPipelineConfig(source: AgentSearchPipelineConfig, ingestSettings: IngestPipelineConfig): Map<String, Any?> {
        return mapOf(
            "type" to "agent",
            "dao" to mapDAOConfig(source.dao, ingestSettings.vectorizer),
            "llm" to mapLLMConfig(source.llm),
            "search" to source.search?.let { mapSearchConfig(it) },
            "reranker" to source.reranker?.let { mapRerankerConfig(it) },
            "system_prompt" to source.systemPrompt,
            "control_prompt" to promptsService.agentResponseControlPrompt(),
            "return_content" to true,
            "csv_processor_config" to mapCsvProcessorConfig(source.csvProcessorConfig)
        )
    }

    private fun mapLLMConfig(source: LLMConfig): Map<String, Any?> {
        return mapOf(
            "llm_provider" to if (source.model?.startsWith("openai/") == true || source.model == "gpt-4o") "openai" else "mlp",
            "model" to source.model?.removePrefix("openai/"),
            "frequency_penalty" to source.frequencyPenalty,
            "presence_penalty" to source.presencePenalty,
            "max_tokens" to source.maxTokens,
            "context_window" to source.contextWindow,
            "temperature" to source.temperature,
            "top_p" to source.topP,
        )
    }

    fun mapDAOConfig(dao: DaoConfig, vectorizer: VectorizerConfig): Map<String, Any?> {
        return mapOf(
            "type" to "elastic_search",
            "index_name" to dao.indexName,
            "vectorizer" to mapVectorizerConfig(vectorizer)
        )
    }

    private fun mapSearchConfig(searchConfig: RetrievingConfig): Map<String, Any?> {
        return mapOf(
            "similarity_top_k" to if (searchConfig.similarityTopK <= 0) DEFAULT_SIMILARITY_TOP_K else searchConfig.similarityTopK,
            "num_candidates" to if (searchConfig.numCandidates <= 0) DEFAULT_NUM_CANDIDATES else searchConfig.numCandidates,
            "candidate_radius" to searchConfig.candidateRadius,
            "history_max_tokens" to searchConfig.historyMaxTokens,
            "history_min_user_messages" to searchConfig.historyMinUserMessages,
            "image_processing_strategy" to ragProperties.imageSearchStrategy.toString(),
            "use_all_embeddings" to searchConfig.useAllEmbeddings,
            "enable_fts" to searchConfig.fullTextSearch?.ftSearch,
            "search_strategy" to searchConfig.fullTextSearch?.strategy?.toString()?.uppercase(),
            "semantic_weight" to searchConfig.fullTextSearch?.semanticPortion,
            "fts_weight" to searchConfig.fullTextSearch?.ftSearch,
            "threshold" to searchConfig.fullTextSearch?.threshold,
            "fts_only_in_segment" to searchConfig.fullTextSearch?.useOnlyInSegment,
            "use_history" to searchConfig.useHistory,
        )
    }

    private fun mapQueryTransformers(searchConfig: RetrievingConfig?, abbreviationsConfig: AbbreviationsConfig?): List<Any> {
        val queryTransformers = mutableListOf<Any>()
        if (searchConfig?.useHistory == true && searchConfig.historyCondensePrompt != null) {
            queryTransformers.add(
                mapOf(
                    "type" to "rephrase_with_history",
                    "prompt" to searchConfig.historyCondensePrompt,
                    "transform_generative_message" to (searchConfig.transformGenerativeMessage ?: false)
                )
            )
        }
        if (searchConfig?.useHistory != true && searchConfig?.rephraseQuery == true && searchConfig.queryRephrasePrompt != null) {
            queryTransformers.add(
                mapOf(
                    "type" to "rephrase",
                    "prompt" to searchConfig.queryRephrasePrompt,
                    "transform_generative_message" to (searchConfig.transformGenerativeMessage ?: false)
                )
            )
        }
        if (abbreviationsConfig?.values != null) {
            queryTransformers.add(
                mapOf(
                    "type" to "abbreviations_resolve",
                    "abbreviations" to abbreviationsConfig.values,
                    "transform_generative_message" to (searchConfig?.transformGenerativeMessage ?: false)
                )
            )
        }
        return queryTransformers
    }

    private fun mapResponseGeneratorConfig(source: ResponseGeneratorConfig): Map<String, Any?> {
        return mapOf(
            "prompt" to source.prompt,
            "control_prompt" to promptsService.semanticResponseControlPrompt(),
            "mode" to source.mode.name
        )
    }

    fun mapVectorizerConfig(source: VectorizerConfig): Map<String, Any?> {
        var model = source.model ?: ingestProperties.vectorizer.options.first().value
        // fix misprint in old settings
        if (model.contains("text-embeddings-3-large")) {
            model = ingestProperties.vectorizer.options.first().value
        }
        val type = if (model.startsWith("openai/") || model == "text-embedding-3-large") "openai" else "mlp"
        return mapOf(
            "type" to type,
            "model" to model.removePrefix("openai/"),
            "distance_strategy" to DistanceStrategy.cosine.name.uppercase(),
            "embeddings_dimension" to if (type == "mlp") DEFAULT_HF_EMBEDDINGS_DIMENSION else DEFAULT_OPENAI_EMBEDDINGS_DIMENSION,
            "max_sequence_length" to if (type == "mlp") DEFAULT_HF_MAX_SEQUENCE_LENGTH else DEFAULT_OPENAI_MAX_SEQUENCE_LENGTH,
        )
    }

    private fun mapRerankerConfig(reranker: RerankerConfig): Map<String, Any?>? {
        return when (reranker) {
            is ManualRerankerConfig -> mapOf(
                "type" to "manual",
                "min_score" to reranker.minScore,
                "score_reduction_limit" to reranker.scoreReductionLimit,
                "max_chunks" to reranker.maxChunks,
                "max_chunks_per_doc" to reranker.maxChunksPerDoc,
            )

            is ModelRerankerConfig -> mapOf(
                "type" to "mlp",
                "max_chunks" to reranker.maxChunks,
                "min_score" to reranker.minScore,
                "model" to reranker.model

            )

            else -> null
        }
    }

    private fun mapCsvProcessorConfig(csvProcessorConfig: CSVProcessorConfig?): Map<String, Any?> {
        return mapOf(
            "enabled" to (csvProcessingEnabled && (csvProcessorConfig?.enabled ?: true)),
            "prompt_adjustment" to csvProcessorConfig?.promptAdjustment,
        )
    }

    fun toRetrieveChunksRequest(
        query: String,
        searchConfig: SearchPipelineConfig,
        ingestConfig: IngestPipelineConfig,
        topK: Int?,
        conversationId: String? = null,
        segment: String? = null,
        history: UserHistory? = null
    ): Map<String, Any?> {
        val retrievingSearch = if (topK != null) {
            when (searchConfig) {
                is AgentSearchPipelineConfig -> searchConfig.copy(search = searchConfig.search?.copy(similarityTopK = topK))
                is SemanticSearchPipelineConfig -> searchConfig.copy(search = searchConfig.search?.copy(similarityTopK = topK))
            }
        } else searchConfig
        return mapOf(
            "query" to mapOf(
                "message" to query,
                "segment" to segment,
                "conversation_id" to conversationId
            ),
            "config" to toRagPipelineConfig(retrievingSearch, ingestConfig),
            "user_history" to history?.historyRecords?.let {
                mapOf(
                    "messages" to it.map { record ->
                        mapOf(
                            "content" to record.message, "role" to record.role.name
                        )
                    }.toList()
                )
            }
        )
    }

    fun mapChunkerConfig(source: ChunkerConfig): Map<String, Any?> {
        return mapOf(
            "type" to source.type.name,
            "chunk_size" to source.chunkSize,
            "language" to source.language?.name,
            "tokens_soft_limit" to source.tokensSoftLimit,
            "tokens_hard_limit" to source.tokensSoftLimit?.let { it * 2 },
            "large_table_lines " to source.useForLargeTables?.let { useForLargeTables ->
                if (useForLargeTables) {
                    50
                } else {
                    10000
                }
            },
            "max_llm_retries" to 2,
            "max_llm_chunker_tokens" to 70000,
            "llm" to mapOf(
                "llm_provider" to if (source.model?.startsWith("openai/") == true || source.model == "gpt-4o") "openai" else "mlp",
                "model" to source.model?.removePrefix("openai/"),
                "frequency_penalty" to source.frequencyPenalty,
                "presence_penalty" to source.presencePenalty,
                "max_tokens" to source.maxTokens,
                "context_window" to source.contextWindow,
                "temperature" to source.temperature,
                "top_p" to source.topP,
            )
        )
    }

    fun mapFileLink(source: FileLink): Map<String, Any?> {
        return mapOf(
            "repository_id" to source.repositoryId,
            "branch_name" to source.branchName,
            "relative_path" to source.path,
        )
    }

    fun toIngestRequest(
        file: ProjectFileEntity,
        fileLink: FileLink,
        parsedFileName: String,
        ingestSettings: IngestPipelineConfig
    ): Map<String, Any?> {
        return mapOf(
            "file" to mapFileLink(fileLink),
            "parsed_file" to parsedFileName,
            "segment" to file.metadata.segment,
            "metadata" to mapOf("path" to file.pathInStorage),
            "config" to mapOf(
                "chunker" to mapChunkerConfig(ingestSettings.chunker),
                "index_name" to ingestSettings.dao!!.indexName,
                "vectorizer" to mapVectorizerConfig(ingestSettings.vectorizer),
                "llm" to mapOf(
                    "llm_provider" to if (ingestSettings.chunker.model?.startsWith("openai/") == true || ingestSettings.chunker.model == "gpt-4o") "openai" else "mlp",
                    "model" to ingestSettings.chunker.model?.removePrefix("openai/"),
                    "frequency_penalty" to ingestSettings.chunker.frequencyPenalty,
                    "presence_penalty" to ingestSettings.chunker.presencePenalty,
                    "max_tokens" to ingestSettings.chunker.maxTokens,
                    "context_window" to ingestSettings.chunker.contextWindow,
                    "temperature" to ingestSettings.chunker.temperature,
                    "top_p" to ingestSettings.chunker.topP,
                ),
                "image_model" to mapOf(
                    "llm_provider" to if (ingestProperties.imageProcessor.model.startsWith("openai/") == true || ingestProperties.imageProcessor.model == "gpt-4o") "openai" else "mlp",
                    "model" to ingestProperties.imageProcessor.model.removePrefix("openai/")
                ),
                "process_images" to ingestProperties.imageProcessor.enabled,
                "enrich_chunks" to enrichChunks,
            )
        )
    }

    fun toGenerateQuestionsRequest(
        fileLink: FileLink,
        parsedFileName: String,
        llm: LLMConfig,
        prompt: String,
        testsCount: Int,
    ): Map<String, Any?> {
        return mapOf(
            "file" to mapFileLink(fileLink),
            "parsed_file" to parsedFileName,
            "prompt" to prompt,
            "tests_count" to testsCount,
            "llm" to mapOf(
                "llm_provider" to llm.llmProvider,
                "model" to llm.model,
            )
        )
    }

    fun toDownloadChunksRequest(indexName: String, filePath: String): Map<String, Any?> {
        return mapOf(
            "doc_id" to filePath,
            "index_name" to indexName
        )
    }
}
