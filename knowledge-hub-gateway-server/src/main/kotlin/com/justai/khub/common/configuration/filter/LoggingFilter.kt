package com.justai.khub.common.configuration.filter

import com.justai.khub.common.util.CustomHeaders
import com.justai.khub.common.util.LoggingUtils
import com.justai.khub.common.util.WebUtils
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.security.SecurityProperties
import org.springframework.core.annotation.Order
import org.springframework.stereotype.Component
import org.springframework.web.filter.OncePerRequestFilter
import java.util.*

@Component
@Order(SecurityProperties.DEFAULT_FILTER_ORDER + 1) // we need security filter to add current user to security context
class LoggingFilter : OncePerRequestFilter() {
    private val log = LoggerFactory.getLogger(this::class.java)

    private val IGNORED_ROUTES = setOf(
        "/healthCheck",
        "/version",
        "/actuator/prometheus",
        "/favicon.ico"
    )

    override fun doFilterInternal(request: HttpServletRequest, response: HttpServletResponse, filterChain: FilterChain) {
        val startedAt = System.currentTimeMillis()
        try {
            LoggingUtils.addToMDC(
                accountId = WebUtils.getCurrentAccountIdOrNull(),
                requestId = request.getHeader(CustomHeaders.REQUEST_ID) ?: UUID.randomUUID().toString()
            )
            logRequest(request)
            filterChain.doFilter(request, response)
        } finally {
            logResponse(request, response, startedAt)
            LoggingUtils.clearAll()
        }
    }

    private fun logResponse(request: HttpServletRequest, response: HttpServletResponse, startedAt: Long) {
        if (IGNORED_ROUTES.contains(request.requestURI)) return
        val processingTime = System.currentTimeMillis() - startedAt
        if (response.status >= 500) {
            log.warn("Response: ${request.method} ${request.requestURI}\n\tstatus = ${response.status}\n\tprocessing time = ${processingTime}ms")
        } else {
            log.info("Response: ${request.method} ${request.requestURI}\n\tstatus = ${response.status}\n\tprocessing time = ${processingTime}ms")
        }
    }

    private fun logRequest(request: HttpServletRequest) {
        if (IGNORED_ROUTES.contains(request.requestURI)) return
        log.info("Request: ${request.method} ${request.requestURI}")
    }


}
