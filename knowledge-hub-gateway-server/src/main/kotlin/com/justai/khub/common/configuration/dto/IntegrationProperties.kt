package com.justai.khub.common.configuration.dto

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "integrations", ignoreUnknownFields = true)
data class IntegrationProperties(
    val lakefs: LakefsIntegrationProperties,
    val rag: RagServiceIntegrationProperties,
    val aa: AccountsAdminIntegrationProperties,
    val ingester: IngesterIntegrationProperties,
    val evaluator: EvaluationServiceIntegrationProperties,
    val parser: ParsingServiceIntegrationProperties,
    val externalSources: ExternalSourcesIntegrationProperties,
    val confluence: ConfluenceIntegrationProperties,
    val minerva: MinervaIntegrationProperties,
    val jaicp: JaicpIntegrationProperties,
    val checkIntegrationsJobPoolSize: Int,
    val checkErrorsLimit: Int,
    val atlassian: AtlassianIntegrationProperties,
)
