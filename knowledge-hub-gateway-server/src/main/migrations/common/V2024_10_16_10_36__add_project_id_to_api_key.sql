ALTER TABLE public.api_key ADD COLUMN project_id BIGINT REFERENCES public.project (id);
ALTER TABLE public.api_key ADD COLUMN value VARCHAR;
DROP INDEX IF EXISTS idx_api_key_name;
CREATE UNIQUE INDEX idx_api_key_name_by_project ON public.api_key (created_by_account_id, name, project_id) WHERE project_id IS NOT NULL AND status = 'ACTIVE';
CREATE UNIQUE INDEX idx_api_key_name ON public.api_key (created_by_account_id, name) WHERE project_id IS NULL AND status = 'ACTIVE';
