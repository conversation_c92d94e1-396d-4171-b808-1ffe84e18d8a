@Library('justai-common') _

properties([
    parameters([
        string(name: 'SLACK_CHANNEL', defaultValue: '#khub-ci', description: ''),
        string(name: 'GATEWAY_BASE_URL', defaultValue: 'https://khub.dev18.lo.test-ai.net', description: ''),
        string(name: 'AA_BASE_URL', defaultValue: 'http://aa.dev18.lo.test-ai.net', description: '')
    ])
])
runTestPipeline([
    "mvn_params": "clean install -U -am -pl knowledge-hub-gateway-bbt -DskipBBTs=false",
    "SLACK_CHANNEL": "${params.SLACK_CHANNEL}",
    "JDK_VERSION": "17",
    "TEST_YML_FILE": "knowledge-hub-gateway-bbt/src/test/resources/application-test.yml",
    "test_yml_content": """
integration:
  aa:
    baseUrl: ${params.AA_BASE_URL}

  gateway:
    baseUrl: ${params.GATEWAY_BASE_URL}
""",
])
