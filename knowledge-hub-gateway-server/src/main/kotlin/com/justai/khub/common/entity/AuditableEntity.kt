package com.justai.khub.common.entity

import jakarta.persistence.AttributeOverride
import jakarta.persistence.AttributeOverrides
import jakarta.persistence.Column
import jakarta.persistence.Embedded
import jakarta.persistence.EntityListeners
import jakarta.persistence.MappedSuperclass
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedBy
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime


@MappedSuperclass
@EntityListeners(AuditingEntityListener::class)
abstract class AuditableEntity {
    @Column
    @CreatedDate
    lateinit var createdAt: LocalDateTime

    @CreatedBy
    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "accountId", column = Column(name = "created_by_account_id")),
        AttributeOverride(name = "userId", column = Column(name = "created_by_user_id"))
    )
    var createdBy: CCUserEntity = CCUserEntity()

    @Column
    @LastModifiedDate
    lateinit var updatedAt: LocalDateTime


    @LastModifiedBy
    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "accountId", column = Column(name = "updated_by_account_id")),
        AttributeOverride(name = "userId", column = Column(name = "updated_by_user_id"))
    )
    var updatedBy: CCUserEntity = CCUserEntity()
}
