package com.justai.khub.channel.entity

import com.justai.khub.channel.dto.ChannelSettings
import com.justai.khub.channel.enumeration.ChannelStatus
import com.justai.khub.common.entity.AuditableEntity
import com.justai.khub.integration.dto.IntegrationSettings
import com.justai.khub.integration.enumeration.IntegrationStatus
import com.justai.khub.project.entity.ProjectVersionEntity
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import org.hibernate.annotations.Type
import java.time.LocalDateTime

@Entity
@Table(name = "channel")
class ChannelEntity : AuditableEntity() {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = -1

    @Column(nullable = false)
    lateinit var name: String

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    lateinit var status: ChannelStatus

    @Type(JsonType::class)
    @Column(columnDefinition = "jsonb")
    lateinit var settings: ChannelSettings

    @ManyToOne
    lateinit var version: ProjectVersionEntity
}

