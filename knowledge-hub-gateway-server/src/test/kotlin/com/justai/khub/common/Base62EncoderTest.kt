package com.justai.khub.common

import com.justai.khub.common.util.Base62Encoder
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class Base62EncoderTest {

    @Test
    fun testEncoder() {
        for (i in 0L..20) {
            val encoded = Base62Encoder.encode(i)
            val decoded = Base62Encoder.decode(encoded)
            assertEquals(i, decoded)
        }

        val encoded = Base62Encoder.encode(13810629177)
        val decoded = Base62Encoder.decode(encoded)
        assertEquals(13810629177, decoded)
    }
}
