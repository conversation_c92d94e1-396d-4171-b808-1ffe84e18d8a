package com.justai.khub.integration.service

import com.justai.khub.common.configuration.dto.IntegrationProperties
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.integration.dto.CloudConfluenceIntegrationSettings
import com.justai.khub.integration.dto.IntegrationSettings
import com.justai.khub.integration.dto.atlassian.AvailableResourcesResponse
import com.justai.khub.integration.dto.atlassian.RefreshTokenRequest
import com.justai.khub.integration.dto.atlassian.TokenCodeExchangeRequest
import com.justai.khub.integration.dto.atlassian.TokenResponse
import com.justai.loadbalancer.annotations.HttpExternal
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.exchange
import org.springframework.web.client.postForEntity


@Service
class AtlassianService(
    @HttpExternal private val atlassianRestTemplate: RestTemplate,
    private val integrationProperties: IntegrationProperties,
) {
    fun getAccessToken(integrationSettings: IntegrationSettings, authCode: String): TokenResponse {
        require(integrationSettings is CloudConfluenceIntegrationSettings)

        return try {
            atlassianRestTemplate.postForEntity<TokenResponse>(
                "${integrationSettings.authBaseUrl}/oauth/token",
                TokenCodeExchangeRequest(
                    clientId = integrationProperties.atlassian.clientId,
                    clientSecret = integrationProperties.atlassian.clientSecret,
                    code = authCode,
                    redirectUri = integrationProperties.atlassian.redirectUrl,
                ),
            ).body ?: error("Empty response")
        } catch (hex: HttpClientErrorException) {
            throw KhubException(ApiErrorCode.INVALID_ACCESS_TOKEN, mapOf("error" to hex.responseBodyAsString), hex)
        }
    }

    fun getCloudIds(integrationSettings: IntegrationSettings, token: String): Map<String, String> {
        val headers = HttpHeaders().apply {
            setBearerAuth(token)
        }
        val response = atlassianRestTemplate.exchange<List<AvailableResourcesResponse>>(
            url = "${integrationSettings.baseUrl}/oauth/token/accessible-resources",
            method = HttpMethod.GET,
            requestEntity = HttpEntity<Void>(headers)
        ).body ?: error("Empty response")
        return response.associate { it.id to it.url }
    }

    fun refreshAccessToken(integrationSettings: IntegrationSettings, refreshToken: String): TokenResponse {
        require(integrationSettings is CloudConfluenceIntegrationSettings)

        try {
            val response = atlassianRestTemplate.postForEntity<TokenResponse>(
                "${integrationSettings.authBaseUrl}/oauth/token",
                RefreshTokenRequest(
                    clientId = integrationProperties.atlassian.clientId,
                    clientSecret = integrationProperties.atlassian.clientSecret,
                    refreshToken = refreshToken,
                ),
            )
            return response.body ?: error("Empty response")
        } catch (fex: HttpClientErrorException) {
            throw KhubException(ApiErrorCode.INVALID_ACCESS_TOKEN, mapOf("error" to fex.responseBodyAsString), fex)
        }
    }
}
