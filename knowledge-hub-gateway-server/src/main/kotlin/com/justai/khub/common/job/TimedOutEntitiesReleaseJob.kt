package com.justai.khub.common.job

import com.justai.khub.chat.service.ChatHistoryService
import com.justai.khub.ingest.service.IngestService
import com.justai.khub.integration.service.IntegrationService
import com.justai.khub.project.service.ProjectFileService
import com.justai.shutdown.CallbackAwaiter
import com.justai.shutdown.GracefulShutDownAware
import com.justai.shutdown.ShutDownLatch
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.LocalDateTime
import java.util.concurrent.TimeUnit

@ConditionalOnProperty(value = ["scheduling.enabled"], havingValue = "true", matchIfMissing = true)
@Component
class TimedOutEntitiesReleaseJob(
    private val ingestService: IngestService,
    private val projectFileService: ProjectFileService,
    private val chatHistoryService: ChatHistoryService,
    private val integrationService: IntegrationService,
    @Value("\${jobs.entitiesProcessingTimeout}")
    private val entitiesProcessingTimeout: Duration
) : GracefulShutDownAware {

    private val log = LoggerFactory.getLogger(this::class.java)

    private val shutDownLatch = ShutDownLatch()

    @Scheduled(fixedDelayString = "\${jobs.timedOutEntitiesCheckIntervalSeconds}", timeUnit = TimeUnit.SECONDS)
    fun cleanupExpiredLinks() {
        shutDownLatch.run {
            val lastUpdatedBefore = LocalDateTime.now().minus(entitiesProcessingTimeout)
            val releaseIngestJobs = ingestService.releaseTimedOutFileIngests(lastUpdatedBefore)
            if (releaseIngestJobs > 0) {
                log.error("Released {} timed out ingest jobs", releaseIngestJobs)
            }

            val releasedFiles = projectFileService.releaseAllTimedOutFiles(lastUpdatedBefore)
            if (releasedFiles > 0) {
                log.error("Released {} timed out files", releasedFiles)
            }

            val releasedRecords = chatHistoryService.releaseAllTimedOutRecords(lastUpdatedBefore)
            if (releasedRecords > 0) {
                log.error("Released {} timed out chat records", releasedRecords)
            }

            val releasedIntegrationChecks = integrationService.releaseAllTimedOutChecks(lastUpdatedBefore)
            if (releasedIntegrationChecks > 0) {
                log.error("Released {} timed out integration checks", releasedIntegrationChecks)
            }
        }
    }


    override fun shutDownGracefully(runnable: Runnable) {
        CallbackAwaiter.withCallback(runnable).awaitAsync {
            log.info("Stop TimedOutEntitiesReleaseJob...")
            shutDownLatch.awaitShutdown()
            log.info("Stop TimedOutEntitiesReleaseJob... DONE")
        }
    }
}
