package com.justai.khub.common.configuration.service

import com.justai.khub.common.configuration.dto.KHubUser
import com.justai.khub.common.entity.CCUserEntity
import org.springframework.data.domain.AuditorAware
import org.springframework.security.core.context.SecurityContextHolder
import java.util.*


class SpringSecurityAuditorAware : AuditorAware<CCUserEntity> {
    override fun getCurrentAuditor(): Optional<CCUserEntity> {
        val authentication = SecurityContextHolder.getContext()?.authentication ?: return Optional.empty()
        if (!authentication.isAuthenticated || authentication !is KHubUser) return Optional.empty()
        return Optional.of(CCUserEntity().also {
            it.userId = authentication.userId
            it.accountId = authentication.accountId
        })
    }
}
