package com.justai.khub.project.mapper

import com.justai.khub.api.fe.model.*
import com.justai.khub.api.public.model.ProjectStatus
import com.justai.khub.api.public.model.SourceDTO
import com.justai.khub.common.dto.DocumentChunkWithSource
import com.justai.khub.common.mapper.CommonMapper.toAuditDTO
import com.justai.khub.common.util.JSON
import com.justai.khub.project.dto.FilesSummaryByStatus
import com.justai.khub.project.dto.SettingsSchema
import com.justai.khub.project.entity.ProjectEntity
import com.justai.khub.project.entity.ProjectFileEntity
import com.justai.khub.project.enumeration.ProjectSourceStatus
import java.time.ZoneId

object ProjectMapper {

    fun ProjectCreateRequest.toEntity(): ProjectEntity {
        return ProjectEntity().also {
            it.name = this.name
        }
    }

    fun ProjectUpdateRequest.toEntity(projectId: Long): ProjectEntity {
        return ProjectEntity().also {
            it.id = projectId
            it.name = this.name
        }
    }

    fun ProjectEntity.toDTO(fileStatuses: List<FilesSummaryByStatus>): ProjectDTO {
        return ProjectDTO(
            id = this.id,
            name = this.name,
            status = mapProjectStatus(fileStatuses).name,
            sourcesCount = fileStatuses.sumOf { it.count },
            lastIngestedAt = fileStatuses.firstOrNull { it.status == ProjectSourceStatus.INGESTED }?.maxUpdatedAt?.atZone(ZoneId.systemDefault())?.toInstant(),
            audit = this.toAuditDTO()
        )
    }


    fun mapProjectStatus(fileStatuses: List<FilesSummaryByStatus>): ProjectStatus {
        val statuses = fileStatuses.mapNotNullTo(HashSet()) { if (it.count <= 0) null else it.status }
        if (statuses.contains(ProjectSourceStatus.READY_TO_INGEST) || statuses.contains(ProjectSourceStatus.PROCESSING)) {
            return ProjectStatus.INGESTING_DOCUMENTS
        }
        if (statuses.contains(ProjectSourceStatus.FAILED_TO_INGEST)) {
            return ProjectStatus.INGEST_FAILED
        }
        if (statuses.contains(ProjectSourceStatus.INGESTED)) {
            return ProjectStatus.ACTIVE
        }
        return ProjectStatus.CREATED
    }

    fun List<DocumentChunkWithSource>.toDTO(): RetrievedChunksDTO {
        return RetrievedChunksDTO(
            chunks = this.map { it.toDTO() }
        )
    }

    private fun DocumentChunkWithSource.toDTO(): ChunkWithScoreDTO {
        return ChunkWithScoreDTO(
            score = this.chunk.score,
            content = this.chunk.content,
            summary = this.chunk.summary,
            questions = this.chunk.questions,
            keywords = this.chunk.keywords,
            docId = this.source.path,
            source = RelevantSourceDTO(
                id = this.source.sourceId,
                path = this.source.path,
                externalLink = this.source.externalLink
            )
        )
    }

    fun SettingsSchema.toDTO(): Map<String, Any> {
        return JSON.toMap(this)
    }

    fun ProjectFileEntity.toPublicSourceDTO(): SourceDTO {
        return SourceDTO(
            id = this.id,
            name = this.relativePath,
            status = this.status.toString(),
            version = this.version.name,
            createdAt = this.createdAt.atZone(ZoneId.systemDefault()).toInstant(),
            segment = this.metadata.segment,
            lastError = this.lastError,
            sizeBytes = this.sizeBytes,
            sizeChars = this.sizeChars
        )
    }
}
