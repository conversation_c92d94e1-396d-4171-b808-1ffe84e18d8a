package com.justai.khub

import com.justai.khub.api.fe.model.ProjectDTO
import com.justai.khub.util.TestDataFactory.testBalanceUpdate
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach

class BaseProjectIT : BaseIT() {
    protected lateinit var project: ProjectDTO

    @BeforeEach
    fun initProject() {
        project = projectHelper.createProject()
        billingHelper.updateAccountBalance(testBalanceUpdate(project.audit.createdBy))
    }

    @AfterEach
    fun cleanupProject() {
        if (::project.isInitialized) {
            projectHelper.deleteProjectRepository(project)
        }
    }
}
