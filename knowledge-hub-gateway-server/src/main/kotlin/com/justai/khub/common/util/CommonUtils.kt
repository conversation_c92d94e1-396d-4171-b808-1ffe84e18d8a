package com.justai.khub.common.util

import com.justai.khub.chat.entity.ChatHistoryRecordEntity
import com.justai.khub.chat.service.ChatHistoryService
import com.justai.khub.common.enumeration.ApiErrorCode.Companion.SOURCE_ERROR_CODE_ARG
import com.justai.khub.common.exception.KhubException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.delay
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.io.ByteArrayOutputStream
import java.time.Instant
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream
import kotlin.time.Duration
import kotlin.time.Duration.Companion.minutes

object CommonUtils {
    private val log = LoggerFactory.getLogger(this::class.java)

    fun createZipArchive(files: Map<String, ByteArray>): ByteArray {
        val byteArrayOutputStream = ByteArrayOutputStream()
        ZipOutputStream(byteArrayOutputStream).use { zipOut ->
            files.forEach { (fileName, fileContent) ->
                val zipEntry = ZipEntry(fileName)
                zipOut.putNextEntry(zipEntry)
                zipOut.write(fileContent)
                zipOut.closeEntry()
            }
        }
        return byteArrayOutputStream.toByteArray()
    }

    suspend fun ChatHistoryService.waitRecordProcessingFinish(
        projectId: Long,
        chatId: Long,
        recordId: Long,
        accountId: Long,
        initialTime: Instant = Instant.now(),
        timeout: Duration = 2.minutes,
        logger: Logger = log
    ): ChatHistoryRecordEntity {
        val expirationTime = initialTime.plusSeconds(timeout.inWholeSeconds)
        return with(Dispatchers.IO) {
            var recordProcessing = getChatRecord(projectId, chatId, recordId, accountId)
            while (Instant.now().isBefore(expirationTime) && !recordProcessing.status.isFinished()) {
                delay(1000)
                recordProcessing = getChatRecord(projectId, chatId, recordId, accountId)
            }
            logger.info(
                "Got record processing result: id={}, status={}, response={}",
                recordProcessing.id,
                recordProcessing.status,
                recordProcessing.response
            )
            recordProcessing
        }
    }

    fun Exception.extractErrorCode(defaultCode: String): String {
        return if (this is KhubException) {
            this.errors.firstNotNullOfOrNull { it.args[SOURCE_ERROR_CODE_ARG]?.toString() ?: it.error.code }
                ?: defaultCode
        } else {
            defaultCode
        }
    }
}
