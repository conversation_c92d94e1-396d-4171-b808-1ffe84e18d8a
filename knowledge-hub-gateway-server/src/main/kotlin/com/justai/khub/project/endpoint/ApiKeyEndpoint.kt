package com.justai.khub.project.endpoint

import com.justai.khub.api.fe.ApiKeyApiService
import com.justai.khub.api.fe.model.*
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.common.service.MessageService
import com.justai.khub.common.util.WebUtils
import com.justai.khub.common.util.WebUtils.getCurrentUser
import com.justai.khub.project.service.ProjectService
import com.justai.khub.project.mapper.ApiKeyMapper
import com.justai.khub.project.service.ApiKeyService
import org.slf4j.LoggerFactory
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service

@Service
class ApiKeyEndpoint(
    private val projectService: ProjectService,
    private val apiKeyService: ApiKeyService,
    private val apiKeyMapper: ApiKeyMapper
) : ApiKeyApiService {
    private val log = LoggerFactory.getLogger(this::class.java)

    override fun createApiKey(projectId: Long, apiKeyCreateRequest: ApiKeyCreateRequest): ApiKeyDTO {
        val currentUser = getCurrentUser()
        val currentProject = projectService.getProject(projectId, currentUser.accountId)
        val newEntity = apiKeyMapper.toEntity(apiKeyCreateRequest, currentProject, currentUser)
        if (apiKeyService.checkActiveApiKeyByName(projectId, currentUser.accountId, newEntity.name)) {
            throw KhubException(ApiErrorCode.DUPLICATE_API_KEY_NAME)
        }
        val savedEntity = apiKeyService.createApiKey(newEntity)
        return apiKeyMapper.toDTO(savedEntity, newEntity.value)
    }

    override fun getApiKeys(projectId: Long, pageSize: Int, onlyActive: Boolean, pageNum: Int): ApiKeysPage {
        val pageable = PageRequest.of(pageNum, pageSize, Sort.by(Sort.Direction.ASC, "name"))
        val currentAccountId = WebUtils.getCurrentAccountId()
        val page = apiKeyService.getApiKeys(onlyActive, projectId, currentAccountId, pageable)
        return ApiKeysPage(
            content = page.content.map { apiKeyMapper.toDTO(it, it.value) },
            paging = PagingResponse(
                totalCount = page.totalElements,
                pageNum = page.number,
                pageSize = page.size
            )
        )
    }

    override fun revokeApiKey(projectId: Long, apiKeyId: Long): ApiKeyDTO {
        val currentAccountId = WebUtils.getCurrentAccountId()
        val revokedEntity = apiKeyService.revokeApiKey(apiKeyId, currentAccountId)
        return apiKeyMapper.toDTO(revokedEntity)
    }

    override fun apiRequestSamples(projectId: Long, apiKeyId: Long): ApiRequestSamples {
        val keyEntity = apiKeyService.getApiKey(apiKeyId)
        return apiKeyMapper.toApiRequestSamples(keyEntity)
    }
}
