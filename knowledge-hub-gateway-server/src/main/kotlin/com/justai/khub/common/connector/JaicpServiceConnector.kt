package com.justai.khub.common.connector

import com.justai.khub.channel.dto.BotProjectModificationDto
import com.justai.khub.channel.dto.BotProjectReadDto
import com.justai.khub.channel.dto.ProjectSettingsData
import com.justai.khub.common.configuration.dto.IntegrationProperties
import com.justai.khub.common.configuration.dto.KHubUser
import com.justai.khub.common.dto.WithIntegrationTimer
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.enumeration.IntegrationName
import com.justai.khub.common.exception.KhubException
import com.justai.loadbalancer.annotations.HttpInternal
import com.justai.security.session.constants.SELECTED_ACCOUNT_HEADER_NAME
import com.justai.security.session.constants.XSRF_TOKEN_HEADER_NAME
import org.apache.commons.codec.binary.Base64
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service
import org.springframework.web.client.*
import org.springframework.web.client.HttpClientErrorException.NotFound

@Service
@WithIntegrationTimer(integrationName = IntegrationName.jaicp)
class JaicpServiceConnector(
    @HttpInternal private val jaicpRestTemplate: RestTemplate,
    @Value("\${server.hostname}") private val hostname: String,
    @Value("\${spring.messages.default-locale}") private val defaultLocale: String,
    private val integrationProperties: IntegrationProperties
) {
    private val log = LoggerFactory.getLogger(this::class.java)

    fun createProjectFromKhubTemplate(
        jaicpProjectName: String,
        khubApiToken: String,
        user: KHubUser
    ): BotProjectReadDto {
        val headers = createHeaders(user)

        val botProjectModificationDto = BotProjectModificationDto(
            name = jaicpProjectName,
            templateParams = mapOf(
                "khub_token" to khubApiToken,
                "khub_baseurl" to "https://$hostname"
            ),
            template = integrationProperties.jaicp.templateName,
            projectSettingsData = ProjectSettingsData(language = defaultLocale)
        )
        return try {
            val request: HttpEntity<BotProjectModificationDto> = HttpEntity(botProjectModificationDto, headers)
            jaicpRestTemplate.postForObject<BotProjectReadDto>(
                "${integrationProperties.jaicp.baseUrl}/api/botadmin/accounts/${user.accountId}/botproject",
                request
            )
        } catch (ex: Exception) {
            throw KhubException(ApiErrorCode.INTERNAL_SERVER_ERROR, ex)
        }
    }

    fun getKhubProjects(
        user: KHubUser,
    ): List<BotProjectReadDto> {
        val headers = createHeaders(user)
        return try {
            val allBotProjects = jaicpRestTemplate.exchange<List<BotProjectReadDto>>(
                "${integrationProperties.jaicp.baseUrl}/api/botadmin/accounts/${user.accountId}/botproject",
                HttpMethod.GET,
                HttpEntity<Void>(headers)
            ).body ?: emptyList()
            allBotProjects.filter { it.templateName == integrationProperties.jaicp.templateName }
        } catch (cex: HttpClientErrorException) {
            throw KhubException(ApiErrorCode.REQUEST_REJECTED_ERROR, cex)
        } catch (ex: Exception) {
            throw KhubException(ApiErrorCode.INTERNAL_SERVER_ERROR, ex)
        }
    }

    fun deleteProject(
        user: KHubUser,
        botProjectId: Long
    ): BotProjectReadDto? {
        val headers = createHeaders(user)

        return try {
            jaicpRestTemplate.exchange<BotProjectReadDto>(
                "${integrationProperties.jaicp.baseUrl}/api/botadmin/accounts/${user.accountId}/botproject/$botProjectId",
                HttpMethod.DELETE,
                HttpEntity<Void>(headers)
            ).body
        } catch (nfe: NotFound) {
            throw KhubException(ApiErrorCode.CHANNEL_NOT_FOUND, nfe)
        } catch (cex: HttpClientErrorException) {
            throw KhubException(ApiErrorCode.REQUEST_REJECTED_ERROR, cex)
        } catch (ex: Exception) {
            throw KhubException(ApiErrorCode.INTERNAL_SERVER_ERROR, ex)
        }
    }

    fun blockProjectChannel(
        user: KHubUser,
        channelId: Long
    ) {
        val headers = createHeaders(user)

        try {
            jaicpRestTemplate.exchange<Void>(
                "${integrationProperties.jaicp.baseUrl}/restapi/botconfig/$channelId/block",
                HttpMethod.PUT,
                HttpEntity<Void>(headers)
            )
        } catch (nfe: NotFound) {
            throw KhubException(ApiErrorCode.EXTERNAL_CHANNEL_NOT_FOUND, nfe)
        } catch (cex: HttpClientErrorException) {
            throw KhubException(ApiErrorCode.REQUEST_REJECTED_ERROR, cex)
        } catch (ex: Exception) {
            throw KhubException(ApiErrorCode.INTERNAL_SERVER_ERROR, ex)
        }
    }

    fun unblockProjectChannel(
        user: KHubUser,
        channelId: Long
    ) {
        val headers = createHeaders(user)

        try {
            jaicpRestTemplate.exchange<Void>(
                "${integrationProperties.jaicp.baseUrl}/restapi/botconfig/$channelId/unblock",
                HttpMethod.PUT,
                HttpEntity<Void>(headers)
            )
        } catch (nfe: NotFound) {
            throw KhubException(ApiErrorCode.EXTERNAL_CHANNEL_NOT_FOUND, nfe)
        } catch (cex: HttpClientErrorException) {
            throw KhubException(ApiErrorCode.REQUEST_REJECTED_ERROR, cex)
        } catch (ex: Exception) {
            throw KhubException(ApiErrorCode.INTERNAL_SERVER_ERROR, ex)
        }
    }

    private fun createHeaders(user: KHubUser) = HttpHeaders().also {
        it.set("Authorization", "Bearer ${user.sessionId?.toByteArray()?.let { Base64.encodeBase64String(it) }}")
        it.set(XSRF_TOKEN_HEADER_NAME, user.xsrfToken)
        it.set(SELECTED_ACCOUNT_HEADER_NAME, user.accountId.toString())
    }
}



