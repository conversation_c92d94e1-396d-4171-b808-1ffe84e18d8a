package com.justai.khub.qa.job

import com.justai.khub.qa.service.TestSetScheduleService
import com.justai.shutdown.CallbackAwaiter
import com.justai.shutdown.GracefulShutDownAware
import com.justai.shutdown.ShutDownLatch
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@ConditionalOnProperty(value = ["scheduling.enabled"], havingValue = "true", matchIfMissing = true)
@Component
class TestSetScheduleProcessingJob(
    private val testSetScheduleService: TestSetScheduleService,
) : GracefulShutDownAware {
    private val shutDownLatch = ShutDownLatch()

    private val log = LoggerFactory.getLogger(this::class.java)

    @Scheduled(fixedDelay = 60 * 1_000)
    fun processScheduledTestSets() {
        shutDownLatch.run {
            try {
                testSetScheduleService.getReadyToSchedule()
                    .forEach { testSetScheduleService.trySchedule(it) }
            } catch (ex: Exception) {
                log.error("Failed to proces scheduled test sets", ex)
            }
        }
    }

    override fun shutDownGracefully(runnable: Runnable) {
        CallbackAwaiter.withCallback(runnable).awaitAsync {
            log.info("Stop TestSetScheduleProcessingJob...")
            shutDownLatch.awaitShutdown()
            log.info("Stop TestSetProcessingJob... DONE")
        }
    }
}
