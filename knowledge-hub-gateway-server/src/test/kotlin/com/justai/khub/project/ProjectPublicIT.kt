package com.justai.khub.project

import com.justai.khub.BaseProjectIT
import com.justai.khub.api.public.model.ProjectStatus
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class ProjectPublicIT : BaseProjectIT() {
    private lateinit var projectApiKey: String

    @BeforeEach
    fun init() {
        projectApiKey = apiKeyHelper.createApiKey(project.id).key!!
    }

    @Test
    fun testProjectInfo() = runBlocking {
        val projInfoBeforeIngest = projectPublicHelper.getProjectInfo(projectApiKey)
        assertEquals(ProjectStatus.CREATED, projInfoBeforeIngest.status)
        projectFileHelper.uploadFile(project.id)
        ingestHelper.startIngest(project.id)
        val projInfoAfterIngest = projectPublicHelper.getProjectInfo(projectApiKey)
        assertEquals(ProjectStatus.INGESTING_DOCUMENTS, projInfoAfterIngest.status)
    }
}
