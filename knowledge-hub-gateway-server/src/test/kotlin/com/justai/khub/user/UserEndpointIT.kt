package com.justai.khub.user

import com.justai.khub.BaseIT
import com.justai.khub.api.fe.model.UserBalanceDTO
import com.justai.khub.billing.dto.AccountBalanceResponse
import com.justai.khub.user.mapper.UserMapper
import com.justai.khub.util.AuthUtils.mockCCUser
import com.justai.khub.util.TestDataFactory.testBalanceUpdate
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.springframework.test.context.TestPropertySource

@TestPropertySource(properties = ["analytics.gtm=test-GTM"])
class UserEndpointIT : BaseIT() {
    val expectedEnabledLocalFeatures = setOf("qaModuleEnabled", "tariffInfoEnabled")

    @Test
    fun testLocalFeatures() {
        val ccAuth = mockCCUser()
        val currentUser = userHelper.getMe(ccAuth = ccAuth)
        assertNotNull(currentUser.localFeatures)
        val actualLocalFeatures = currentUser.localFeatures!!.toSet()
        expectedEnabledLocalFeatures.forEach {
            assertTrue(actualLocalFeatures.contains(it))
        }
    }

    @Test
    fun testAnalytics() {
        val ccAuth = mockCCUser()
        val currentUser = userHelper.getMe(ccAuth = ccAuth)
        assertNotNull(currentUser.analytics)
        assertEquals("test-GTM", currentUser.analytics!!.gtm)
    }


    @Test
    fun testGetMe() {
        val ccAuth = mockCCUser()
        val currentUser = userHelper.getMe(ccAuth = ccAuth)

        assertEquals(ccAuth.userData.email, currentUser.email)
        assertEquals(ccAuth.userData.login, currentUser.login)
        assertEquals(ccAuth.userData.fullName, currentUser.name)
        assertEquals(ccAuth.userData.features, currentUser.features)
    }

    @Test
    fun testGetCurrentUserBalance() {
        val ccAuth = mockCCUser()
        val initialBalance = userHelper.getCurrentUserBalance(ccAuth)
        assertEquals(UserMapper.EMPTY_BALANCE, initialBalance)

        val balanceUpdatedByAA = billingHelper.updateAccountBalance(testBalanceUpdate(ccAuth.userData.accountId!!))

        val updatedBalance = userHelper.getCurrentUserBalance(ccAuth)
        assertBalanceEqualse(balanceUpdatedByAA, updatedBalance)

        billingHelper.useTariffRequests(ccAuth.userData.accountId!!, 2)
        billingHelper.usePackageRequests(ccAuth.userData.accountId!!, 3)
        val balanceWithUsedRequests = userHelper.getCurrentUserBalance(ccAuth)
        assertEquals(2, balanceWithUsedRequests.usedRequestsByTariff)
        assertEquals(3, balanceWithUsedRequests.usedRequestsByPackages)
    }

    private fun assertBalanceEqualse(aaBalanceDto: AccountBalanceResponse, khubBalanceDto: UserBalanceDTO) {
        assertEquals(aaBalanceDto.khubRequestsTariffLimit, khubBalanceDto.availableRequestsByTariff)
        assertEquals(aaBalanceDto.khubRequestsPackageLimit, khubBalanceDto.availableRequestsByPackages)
        assertEquals(aaBalanceDto.khubRequestsUsedTariffLimit, khubBalanceDto.usedRequestsByTariff)
        assertEquals(aaBalanceDto.khubRequestsUsedPackageLimit, khubBalanceDto.usedRequestsByPackages)
    }
}
