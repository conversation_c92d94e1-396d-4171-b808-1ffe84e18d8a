package com.justai.khub.common.service

import com.justai.khub.common.configuration.dto.PromptProperties
import com.justai.khub.common.dto.PromptRule
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import jakarta.annotation.PostConstruct
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.io.support.PathMatchingResourcePatternResolver
import org.springframework.stereotype.Service
import java.nio.charset.StandardCharsets
import java.util.regex.Pattern


@Service
class PromptsService(
    @Value("\${spring.messages.default-locale}") private val defaultLocale: String,
    private val promptProperties: PromptProperties
) {
    private val log = LoggerFactory.getLogger(this::class.java)

    private val prompts: MutableMap<String, String> = mutableMapOf()

    private val semanticGenerationRuleRegexp = Pattern.compile(promptProperties.semanticGenerationResponseRulePattern)

    @PostConstruct
    fun loadPrompts() {
        val resolver = PathMatchingResourcePatternResolver()
        resolver.getResources("/prompts/${defaultLocale}/*.txt").forEach {
            prompts[it.filename?.removeSuffix(".txt")!!] = it.getContentAsString(StandardCharsets.UTF_8)
        }
        log.info("Loaded {} prompts", prompts.size)
    }

    fun rephraseWithHistoryPrompt(): String {
        return getPrompt("rephrase_with_history")
    }

    fun rephrasePrompt(): String {
        return getPrompt("rephrase")
    }

    fun responseGenerationPrompt(): String {
        return getPrompt("response_generation")
    }

    fun semanticResponseControlPrompt(): String {
        return getPrompt("semantic_response_control_prompt")
    }

    fun agentPipelinePrompt(): String {
        return getPrompt("agent_pipeline_prompt")
    }

    fun agentResponseControlPrompt(): String {
        return getPrompt("agent_response_control_prompt")
    }

    fun testSetGenerationPrompt(): String {
        return getPrompt("test_set_generation")
    }

    fun testEvaluationPrompt(): String {
        return getPrompt("test_evaluation")
    }

    fun relevantDocsInAnswerRule(): PromptRule {
        return PromptRule(getPrompt("relevant_docs_in_answer_rule").trim())
    }

    fun removeRulesAndGet(userGenerationPrompt: String, rules: List<PromptRule>): String {
        var generationMatcher = semanticGenerationRuleRegexp.matcher(userGenerationPrompt)
        var results = StringBuffer()
        while (generationMatcher.find()) {
            val ruleText = generationMatcher.group("ruleText")
            val matchedRule = rules.firstOrNull { it.rule == ruleText }
            if (matchedRule != null) {
                generationMatcher = generationMatcher.appendReplacement(results, "")
                results = StringBuffer(results.trim())
            }
        }
        generationMatcher.appendTail(results)
        return results.toString()
    }

    fun addRulesAndGet(userGenerationPrompt: String, rules: List<PromptRule>): String {
        val generationMatcher = semanticGenerationRuleRegexp.matcher(userGenerationPrompt)
        var lastRuleIndex = 0
        var lastRule = ""
        val skippedRules = ArrayList<PromptRule>()
        while (generationMatcher.find()) {
            lastRuleIndex = try {
                generationMatcher.group("ruleIndex").toInt()
            } catch (ex: Exception) {
                lastRuleIndex + 1
            }
            lastRule = generationMatcher.group("ruleText")
            val matchedRule = rules.firstOrNull { it.rule == lastRule }
            if (matchedRule != null) {
                skippedRules.add(matchedRule)
            }
        }
        var reformatPrompt = userGenerationPrompt
        (rules - skippedRules).forEach { additionalRule ->
            lastRuleIndex++
            reformatPrompt = reformatPrompt.replaceAfterLast(
                lastRule,
                "\n- (${lastRuleIndex}) ${additionalRule.rule}${reformatPrompt.substringAfterLast(lastRule)}"
            )
            lastRule = additionalRule.rule
        }
        return reformatPrompt
    }

    private fun getPrompt(name: String): String {
        return prompts[name] ?: throw KhubException(ApiErrorCode.PROMPT_NOT_FOUND, mapOf("prompt" to name))
    }
}
