package com.justai.khub.common.configuration.filter

import com.justai.khub.common.configuration.dto.KHubUser
import com.justai.khub.common.configuration.service.ApiErrorHandler
import com.justai.khub.common.dto.ApiError
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.security.session.auth.JustSessionAuthentication
import com.justai.security.session.constants.XSRF_TOKEN_HEADER_NAME
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.security.core.context.SecurityContext
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.filter.OncePerRequestFilter

class CurrentAuthRewriteFilter(requiredFeaturesRaw: String?, requiredPermissionsRaw: String?) : OncePerRequestFilter() {

    private val requiredFeatures: List<String> =
        requiredFeaturesRaw?.split(",")?.map { it.trim() }?.filterNot { it.isEmpty() } ?: listOf()

    private val requiredPermissions: List<String> =
        requiredPermissionsRaw?.split(",")?.map { it.trim() }?.filterNot { it.isEmpty() } ?: listOf()

    // public for tests
    public override fun doFilterInternal(request: HttpServletRequest, response: HttpServletResponse, filterChain: FilterChain) {
        val securityContext = checkNotNull(SecurityContextHolder.getContext())
        val handled = when (val currentAuth = securityContext.authentication) {
            is JustSessionAuthentication -> {
                mapAndCheckRequiredFeatures(securityContext, request, response, currentAuth)
            }

            else -> false
        }
        if (!handled) {
            filterChain.doFilter(request, response)
        }
    }

    private fun mapAndCheckRequiredFeatures(
        securityContext: SecurityContext,
        request: HttpServletRequest,
        response: HttpServletResponse,
        currentAuth: JustSessionAuthentication
    ): Boolean {
        securityContext.authentication = KHubUser(
            accountId = currentAuth.userData.accountId ?: throw KhubException(ApiErrorCode.MISSING_ACCOUNT_ID),
            userId = currentAuth.userData.userId,
            userData = currentAuth.userData,
            sessionId = currentAuth.sessionId,
            xsrfToken = request.cookies?.firstOrNull { it.name == XSRF_TOKEN_HEADER_NAME }?.value
        )
        return checkRequiredFeatures(currentAuth, request, response) || checkRequiredPermissions(currentAuth, request, response)
    }

    private fun checkRequiredFeatures(
        auth: JustSessionAuthentication,
        request: HttpServletRequest,
        response: HttpServletResponse
    ): Boolean {
        if (requiredFeatures.isEmpty()) {
            return false
        }
        val accountFeatures = auth.userData.features
        if (requiredFeatures.any { !accountFeatures.contains(it) }) {
            ApiErrorHandler.writeErrorToResponse(
                errors = listOf(ApiError(ApiErrorCode.MISSING_REQUIRED_FEATURES)),
                response = response,
                method = request.method,
                requestUrl = request.requestURI
            )
            return true
        }
        return false
    }

    private fun checkRequiredPermissions(
        auth: JustSessionAuthentication,
        request: HttpServletRequest,
        response: HttpServletResponse
    ): Boolean {
        if (requiredPermissions.isEmpty()) {
            return false
        }
        val userPermissions = auth.userData.permissions
        if (requiredPermissions.any { !userPermissions.contains(it) }) {
            ApiErrorHandler.writeErrorToResponse(
                errors = listOf(ApiError(ApiErrorCode.ACCESS_DENIED)),
                response = response,
                method = request.method,
                requestUrl = request.requestURI
            )
            return true
        }
        return false
    }
}
