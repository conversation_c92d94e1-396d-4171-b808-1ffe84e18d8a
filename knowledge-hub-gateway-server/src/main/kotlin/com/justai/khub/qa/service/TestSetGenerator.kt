package com.justai.khub.qa.service

import com.justai.khub.common.configuration.dto.KHubUser
import com.justai.khub.common.connector.RagServiceConnector
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.common.service.MetricsService
import com.justai.khub.common.service.PromptsService
import com.justai.khub.common.util.CommonUtils.extractErrorCode
import com.justai.khub.common.util.LoggingUtils
import com.justai.khub.project.dto.FileLink
import com.justai.khub.project.entity.ProjectFileEntity
import com.justai.khub.project.entity.ProjectVersionEntity
import com.justai.khub.project.service.ProjectFileService
import com.justai.khub.project.service.ProjectVersionService
import com.justai.khub.qa.entity.TestSetEntity
import com.justai.khub.qa.mapper.QAMapper
import com.justai.khub.qa.mapper.QAMapper.Companion.DEFAULT_MAX_TESTS
import com.justai.khub.qa.mapper.QAMapper.Companion.DEFAULT_TESTS_PER_DOC
import org.slf4j.LoggerFactory
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Service
import java.util.*
import java.util.function.BooleanSupplier
import kotlin.math.min
import kotlin.time.measureTimedValue

@Service
class TestSetGenerator(
    private val testSetService: TestSetService,
    private val testService: TestService,
    private val projectVersionService: ProjectVersionService,
    private val projectFileService: ProjectFileService,
    private val metricsService: MetricsService,
    private val ragServiceConnector: RagServiceConnector,
    private val promptsService: PromptsService,
    private val qaMapper: QAMapper
) {

    private val log = LoggerFactory.getLogger(this::class.java)

    fun generateTestSetInBackground(testSet: TestSetEntity) {
        try {
            SecurityContextHolder.getContext().authentication = KHubUser(accountId = testSet.createdBy.accountId)
            LoggingUtils.addToMDC(accountId = testSet.createdBy.accountId, requestId = UUID.randomUUID().toString())
            val updated = testSetService.startGeneration(testSet)
            if (!updated) {
                log.info("Skip test set generation: {}: generation already started or cancelled", testSet.id)
                return
            }
            log.info("Started generating test set: {}", testSet.id)
            val (isFullyGenerated, duration) = measureTimedValue {
                metricsService.testSetGenerationTimer().record(BooleanSupplier { generateTestSet(testSet) })
            }
            if (isFullyGenerated) {
                testSetService.finishGeneration(testSet)
                log.info("Finished generating test set: {}. processing time = {}ms", testSet.id, duration.inWholeMilliseconds)
            } else {
                log.info("Cancelled generating test set: {}. processing time = {}ms", testSet.id, duration.inWholeMilliseconds)
            }
        } catch (ex: Exception) {
            handleTestSetGenerationException(testSet, ex)
        } finally {
            SecurityContextHolder.clearContext()
            LoggingUtils.clearAll()
        }
    }

    private fun generateTestSet(testSet: TestSetEntity): Boolean {
        val defaultVersion = projectVersionService.getDefaultVersion(testSet.project.id)
        val existedTests = testService.findAllByTestSetId(testSet.id)
        val existedTestsByFileId = existedTests.groupBy { it.groundTruthFile?.id }
        var allowedTestsLeft = (testSet.properties.maxTests ?: DEFAULT_MAX_TESTS) - existedTests.size
        if (allowedTestsLeft <= 0) {
            log.info("Skipped generating test set {}: max tests limit reached", testSet.id)
            return true
        }
        val testsPerDocument = testSet.properties.testsPerDocument ?: DEFAULT_TESTS_PER_DOC
        val testsToGenerate = projectFileService.findAllIngested(defaultVersion.id)
            .map { file ->
                val existingTestCount = existedTestsByFileId[file.id]?.size ?: 0
                val toGenerate = maxOf(0, minOf(allowedTestsLeft, testsPerDocument - existingTestCount))
                allowedTestsLeft -= toGenerate
                file to toGenerate
            }
            .filter { it.second > 0 }
        if (testsToGenerate.isEmpty()) {
            log.warn("Skipped generating test set {}", testSet.id)
            return true
        }
        for (testGeneration in testsToGenerate) {
            if (!testSetService.isProcessing(testSet.id)) {
                log.warn("Test set {} is no longer processing", testSet.id)
                return false
            }
            generateTestsForFile(defaultVersion, testSet, testGeneration.first, testGeneration.second)
        }

        return true
    }

    private fun generateTestsForFile(
        version: ProjectVersionEntity,
        testSet: TestSetEntity,
        file: ProjectFileEntity,
        testsCount: Int
    ) {
        val fileLink = FileLink(
            repositoryId = version.project.repositoryId!!,
            branchName = version.name,
            path = file.pathInStorage
        )
        val llmModel = testSet.properties.generationModel ?: version.searchSettings.llm.model ?: throw KhubException(
            ApiErrorCode.TESTSET_GENERATION_ERROR,
            mapOf("error" to "Missing llm model")
        )
        val response = ragServiceConnector.generateTests(
            fileLink,
            file.parsedFileName!!,
            qaMapper.toLLMConfig(llmModel),
            testSet.properties.generationPrompt ?: promptsService.testSetGenerationPrompt(),
            testsCount
        )
        if (response.error != null) {
            throw KhubException(ApiErrorCode.TESTSET_GENERATION_ERROR, mapOf("error" to response.error))
        }
        testService.createGeneratedTestsAndUpdateTestSet(testSet, file, response)
    }

    private fun handleTestSetGenerationException(testSet: TestSetEntity, ex: Exception) {
        log.error("Error generating test set {}", testSet.id, ex)
        val errorCode = ex.extractErrorCode(ApiErrorCode.TESTSET_GENERATION_ERROR.code)
        metricsService.incrementTestSetGenerationErrorsCounter()
        testSetService.failGeneration(testSet, errorCode)
    }
}
