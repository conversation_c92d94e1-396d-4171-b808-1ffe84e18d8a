package com.justai.khub.billing.job

import com.justai.khub.billing.entity.BillableOperationEntity
import com.justai.khub.billing.service.BillingService
import com.justai.shutdown.CallbackAwaiter
import com.justai.shutdown.GracefulShutDownAware
import com.justai.shutdown.ShutDownLatch
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@ConditionalOnProperty(value = ["scheduling.enabled"], havingValue = "true", matchIfMissing = true)
@Component
class RollbackOutdatedOperationsJob(
    private val billingService: BillingService
) : GracefulShutDownAware {
    val ROLLBACK_REASON = "timeout"

    private val log = LoggerFactory.getLogger(this::class.java)

    private val shutDownLatch = ShutDownLatch()

    @Scheduled(fixedDelay = 10 * 60_000)
    fun processOutdatedOperations() {
        shutDownLatch.run {
            val outdatedOperations = billingService.findOutdatedOperations()
            if (outdatedOperations.isNotEmpty()) {
                log.warn("Applying rollback for {} outdated billable operations", outdatedOperations.size)
                outdatedOperations.forEach { applyRollback(it) }
            }
        }
    }

    private fun applyRollback(operation: BillableOperationEntity) {
        try {
            billingService.rollbackBillableOperation(operation.id, ROLLBACK_REASON, true)
        } catch (ex: Exception) {
            log.error("Failed to apply rollback for billable operation {}", operation.id, ex)
        }
    }

    override fun shutDownGracefully(runnable: Runnable) {
        CallbackAwaiter.withCallback(runnable).awaitAsync {
            log.info("Stop RollbackOutdatedOperationsJob...")
            shutDownLatch.awaitShutdown()
            log.info("Stop RollbackOutdatedOperationsJob... DONE")
        }
    }
}
