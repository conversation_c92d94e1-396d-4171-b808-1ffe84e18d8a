package com.justai.khub.integration.entity

import com.justai.khub.common.entity.AuditableEntity
import com.justai.khub.integration.dto.IntegrationSettings
import com.justai.khub.integration.enumeration.IntegrationStatus
import com.justai.khub.integration.enumeration.IntervalUnits
import com.justai.khub.project.entity.ProjectVersionEntity
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.*
import org.hibernate.annotations.Type
import java.time.LocalDateTime

@Entity
@Table(name = "integration")
class IntegrationEntity : AuditableEntity() {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = -1

    @Column(nullable = false)
    lateinit var name: String

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    lateinit var status: IntegrationStatus

    @Type(JsonType::class)
    @Column(columnDefinition = "jsonb")
    lateinit var settings: IntegrationSettings

    @ManyToOne
    lateinit var version: ProjectVersionEntity

    @Column
    var lastCheckedAt: LocalDateTime? = null

    @Column
    var checkIntervalMinutes: Int = 60

    @Enumerated(EnumType.STRING)
    @Column
    var checkIntervalUnits: IntervalUnits = IntervalUnits.MINUTES

    @Column
    var autoIngest: Boolean = false

    @Column
    var autoSync: Boolean = false

    @Column
    var lastError: String? = null

    @Column
    var errorsCount: Int = 0

    @Column
    var lastLockedBy: String? = null
}

