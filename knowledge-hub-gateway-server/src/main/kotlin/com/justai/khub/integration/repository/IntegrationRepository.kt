package com.justai.khub.integration.repository

import com.justai.khub.integration.entity.IntegrationEntity
import com.justai.khub.integration.enumeration.IntegrationStatus
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface IntegrationRepository : JpaRepository<IntegrationEntity, Long> {
    @Query("SELECT i FROM IntegrationEntity i WHERE i.version.id = :versionId AND i.status IN :statuses")
    fun findAllByVersionIdAndStatusIn(versionId: Long, statuses: List<IntegrationStatus>, pageable: Pageable): Page<IntegrationEntity>

    @Query(
        """
    SELECT i.* FROM integration i
    WHERE i.status IN :statuses
    AND i.auto_sync = TRUE
    AND i.errors_count < :errorsLimit
    AND (i.last_checked_at IS NULL OR i.last_checked_at < NOW() - interval '1 minute' * i.check_interval_minutes)
""",
        nativeQuery = true
    )
    fun getReadyToCheck(statuses: List<String>, errorsLimit: Int, pageable: Pageable): Page<IntegrationEntity>

    @Query(
        "SELECT i FROM IntegrationEntity i" +
            " WHERE i.version.id = :versionId" +
            " AND i.status IN :statuses" +
            " AND LOWER(i.name) LIKE CONCAT('%', LOWER(:namePart), '%') ESCAPE '\\'"
    )
    fun findAllByVersionIdAndNameContainsAndStatusIn(
        versionId: Long,
        statuses: List<IntegrationStatus>,
        namePart: String,
        pageable: Pageable
    ): Page<IntegrationEntity>

    @Modifying
    @Query(
        "UPDATE IntegrationEntity i " +
            "SET i.status=:newStatus, i.updatedAt=:updatedAt, i.lastCheckedAt=:updatedAt " +
            "WHERE i.status = :oldStatus AND i.lastLockedBy = :lastLockedBy"
    )
    fun updateByStatusAndLastLockedBy(
        oldStatus: IntegrationStatus,
        newStatus: IntegrationStatus,
        lastLockedBy: String,
        updatedAt: LocalDateTime = LocalDateTime.now()
    ): Int


    @Modifying
    @Query(
        "UPDATE IntegrationEntity i " +
            "SET i.status=:newStatus, i.updatedAt=:updatedAt, i.lastCheckedAt=:updatedAt " +
            "WHERE i.status = :oldStatus AND i.updatedAt < :lastUpdateBefore"
    )
    fun releaseTimedOutChecks(
        oldStatus: IntegrationStatus,
        newStatus: IntegrationStatus,
        lastUpdateBefore: LocalDateTime,
        updatedAt: LocalDateTime = LocalDateTime.now()
    ): Int

    @Modifying
    @Query(
        "UPDATE IntegrationEntity i" +
            " SET i.status=:newStatus, i.lastLockedBy = :lastLockedBy, i.updatedAt=:updatedAt, i.lastError = NULL" +
            " WHERE i.id = :id AND i.status IN :expectedStatuses"
    )
    fun startChecking(
        id: Long,
        expectedStatuses: List<IntegrationStatus>,
        newStatus: IntegrationStatus,
        lastLockedBy: String,
        updatedAt: LocalDateTime = LocalDateTime.now()
    ): Int

    @Modifying
    @Query(
        "UPDATE IntegrationEntity i" +
            " SET i.status=:newStatus, i.updatedAt=:updatedAt, i.lastCheckedAt=:updatedAt, i.errorsCount = 0" +
            " WHERE i.id = :id AND i.status=:expectedStatus"
    )
    fun updateStatusAndClearErrors(
        id: Long,
        expectedStatus: IntegrationStatus,
        newStatus: IntegrationStatus,
        updatedAt: LocalDateTime = LocalDateTime.now()
    ): Int

    @Modifying
    @Query(
        "UPDATE IntegrationEntity i" +
            " SET i.status=:newStatus, i.updatedAt=:updatedAt, i.lastCheckedAt=:updatedAt, i.lastError=:lastError, i.errorsCount = i.errorsCount + 1" +
            " WHERE i.id = :id AND i.status=:expectedStatus"
    )
    fun updateStatusAndErrorsCount(
        id: Long,
        expectedStatus: IntegrationStatus,
        newStatus: IntegrationStatus,
        lastError: String?,
        updatedAt: LocalDateTime = LocalDateTime.now()
    ): Int

    @Modifying
    @Query(
        "UPDATE IntegrationEntity i" +
            " SET i.autoSync=:newValue, i.updatedAt=:updatedAt" +
            " WHERE i.version.project.id = :projectId"
    )
    fun updateAutoSyncByProjectId(
        projectId: Long,
        newValue: Boolean,
        updatedAt: LocalDateTime = LocalDateTime.now()
    )

    @Query(
        """
            SELECT * FROM integration
            WHERE settings ->> 'authState' = :authState
        """, nativeQuery = true
    )
    fun getIntegrationEntityBySettingsAuthState(authState: String): IntegrationEntity?

    @Modifying
    @Query(
        "UPDATE IntegrationEntity i" +
            " SET i.status=:newStatus, i.updatedAt=:updatedAt, i.lastError=:lastError" +
            " WHERE i.id = :id AND i.status = :expectedStatus"
    )
    fun updateStatusAndError(
        id: Long,
        expectedStatus: IntegrationStatus,
        newStatus: IntegrationStatus,
        lastError: String?,
        updatedAt: LocalDateTime = LocalDateTime.now()
    ): Int

    @Modifying
    @Query(
        """
            UPDATE integration
            SET settings = jsonb_set(settings, '{spaceKey}', to_jsonb(:spaceKey))
            WHERE id = :id
        """,
        nativeQuery = true
    )
    fun updateSpaceKey(id: Long, spaceKey: String)
}
