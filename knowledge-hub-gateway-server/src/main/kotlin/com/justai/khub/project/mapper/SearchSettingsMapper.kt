package com.justai.khub.project.mapper

import com.justai.khub.common.configuration.dto.KHubUser
import com.justai.khub.common.configuration.dto.RagProperties
import com.justai.khub.common.enumeration.AccountFeatures
import com.justai.khub.common.service.MessageService
import com.justai.khub.common.service.PromptsService
import com.justai.khub.common.util.JSON
import com.justai.khub.project.dto.*
import com.justai.khub.project.mapper.ProjectSettingsMapper.Companion.DEFAULT_CANDIDATE_RADIUS
import com.justai.khub.project.mapper.ProjectSettingsMapper.Companion.DEFAULT_SIMILARITY_TOP_K
import org.springframework.stereotype.Component

@Component
class SearchSettingsMapper(
    private val messageService: MessageService,
    private val promptsService: PromptsService,
    private val rerankerSettingsMapper: RerankerSettingsMapper,
    private val ragProperties: RagProperties
) {

    fun searchPartition(source: SearchPipelineConfig, currentUser: KHubUser): SettingPartition {
        return SettingPartition(
            name = "search",
            title = LocaleObject(
                ru = messageService.getMessage("project.settings.search.root.type.title.ru"),
                en = messageService.getMessage("project.settings.search.root.type.title.en")
            ),
            menuTitle = LocaleObject(
                ru = messageService.getMessage("project.settings.search.root.type.menutitle.ru"),
                en = messageService.getMessage("project.settings.search.root.type.menutitle.en")
            ),
            description = LocaleObject(
                ru = messageService.getMessage("project.settings.search.root.type.hint.ru"),
                en = messageService.getMessage("project.settings.search.root.type.hint.en")
            ),
            more = MoreObject(
                label = LocaleObject(
                    ru = messageService.getMessage("project.settings.search.docs.label.ru"),
                    en = messageService.getMessage("project.settings.search.docs.label.en")
                ),
                link = LocaleObject(
                    ru = messageService.getMessage("project.settings.search.docs.link.ru"),
                    en = messageService.getMessage("project.settings.search.docs.link.en")
                )
            ),
            sections = mutableMapOf(
                "main" to mainSearchSection(source),
                "retrieving" to retrievingSection(source),
                "reranker" to rerankerSettingsMapper.rerankerSection(source.reranker),
                "abbreviations" to abbreviationsSection(source)
            ).apply {
                if (currentUser.hasFeature(AccountFeatures.advancedSettings)) {
                    put("advanced", advancedSection(source))
                }
            }
        )
    }


    private fun abbreviationsSection(source: SearchPipelineConfig): SettingsSection {
        return SettingsSection(
            isCard = false,
            dependsOn = listOf(DependencyConfig("search.main.type", listOf("semantic"))),
            title = LocaleObject(
                ru = messageService.getMessage("project.settings.search.abbreviations.title.ru"),
                en = messageService.getMessage("project.settings.search.abbreviations.title.en")
            ),
            description = LocaleObject(
                ru = messageService.getMessage("project.settings.search.abbreviations.hint.ru"),
                en = messageService.getMessage("project.settings.search.abbreviations.hint.en")
            ),
            settings = mapOf(
                "data" to FileSettingDescription(
                    template = FileTemplate(
                        name = LocaleObject(
                            ru = messageService.getMessage("project.settings.search.abbreviations.template.label.ru"),
                            en = messageService.getMessage("project.settings.search.abbreviations.template.label.en")
                        ),
                        link = LocaleObject(
                            ru = messageService.getMessage("/static/templates/abbreviations.json"),
                            en = messageService.getMessage("/static/templates/abbreviations.json"),
                        ),
                    ),
                    label = null,
                    fileName = source.abbreviations?.fileName,
                    rawData = source.abbreviations?.values?.let { JSON.prettyStringify(it) },
                    acceptFormat = FileFormat.json,
                    maxSizeInMb = 1
                )
            )
        )
    }

    private fun advancedSection(source: SearchPipelineConfig): SettingsSection {
        return SettingsSection(
            isCard = false,
            title = LocaleObject(
                ru = messageService.getMessage("project.settings.advanced.title.ru"),
                en = messageService.getMessage("project.settings.advanced.title.en")
            ),
            settings = mapOf(
                "csv_processing_enabled" to SwitchSettingDescription(
                    label = LocaleObject(
                        ru = messageService.getMessage("project.settings.search.advanced.csv_processing_enabled.label.ru"),
                        en = messageService.getMessage("project.settings.search.advanced.csv_processing_enabled.label.en")
                    ),
                    value = source.csvProcessorConfig?.enabled ?: true
                )
            )
        )
    }

    private fun mainSearchSection(currentValue: SearchPipelineConfig): SettingsSection {
        val currentPrompt = when (currentValue) {
            is AgentSearchPipelineConfig -> currentValue.systemPrompt?.takeIf { it.isNotBlank() } ?: promptsService.agentPipelinePrompt()
            is SemanticSearchPipelineConfig -> currentValue.responseGenerator.prompt?.takeIf { it.isNotBlank() }
                ?: promptsService.responseGenerationPrompt()
        }
        val hasAnyLLMWithAgentPipelineSupport = ragProperties.llm.models.any { it.pipelineTypes.contains(SearchPipelineType.agent.name) || it.pipelineTypes.isEmpty() }
        val availablePipelineTypes = SearchPipelineType.entries.filter { it != SearchPipelineType.agent || hasAnyLLMWithAgentPipelineSupport }
        return SettingsSection(
            isCard = false,
            title = null,
            description = null,
            settings = mapOf(
                "type" to CardsSettingDescription(
                    label = null,
                    value = currentValue.type.name,
                    required = true,
                    options = asOptions(
                        availablePipelineTypes,
                        "project.settings.search.root.type.options",
                        changeDependenciesAfterSelect = { pipelineType ->
                            listOf(
                                DependencyConfig(
                                    path = "generation.main.system_prompt",
                                    value = listOf(
                                        if (pipelineType == currentValue.type.name) currentPrompt else when (pipelineType) {
                                            SearchPipelineType.agent.name -> promptsService.agentPipelinePrompt()
                                            SearchPipelineType.semantic.name -> promptsService.responseGenerationPrompt()
                                            else -> currentPrompt
                                        }
                                    ),
                                    onChangeMessage = LocaleObject(
                                        ru = messageService.getMessage("project.settings.search.root.type.onChange.hint.ru"),
                                        en = messageService.getMessage("project.settings.search.root.type.onChange.hint.en")
                                    ),
                                )
                            )
                        }
                    )
                )
            )
        )
    }

    private fun retrievingSection(currentValue: SearchPipelineConfig): SettingsSection {
        return SettingsSection(
            isCard = false,
            title = LocaleObject(
                ru = messageService.getMessage("project.settings.search.retrieving.title.ru"),
                en = messageService.getMessage("project.settings.search.retrieving.title.en")
            ),
            description = LocaleObject(
                ru = messageService.getMessage("project.settings.search.retrieving.description.ru"),
                en = messageService.getMessage("project.settings.search.retrieving.description.en")
            ),
            settings = mapOf(
                "similarity_top_k" to SliderSettingDescription(
                    format = NumberFormat.int,
                    label = LocaleObject(
                        ru = messageService.getMessage("project.settings.search.retrieving.similarityTopK.label.ru"),
                        en = messageService.getMessage("project.settings.search.retrieving.similarityTopK.label.en")
                    ),
                    value = currentValue.search?.similarityTopK?.toDouble() ?: DEFAULT_SIMILARITY_TOP_K.toDouble(),
                    min = 5.0,
                    max = 30.0,
                    step = 1.0,
                    visibleStep = 5.0,
                    hint = LocaleObject(
                        ru = messageService.getMessage("project.settings.search.retrieving.similarityTopK.hint.ru"),
                        en = messageService.getMessage("project.settings.search.retrieving.similarityTopK.hint.en")
                    )
                ),
                "candidate_radius" to SliderSettingDescription(
                    format = NumberFormat.int,
                    label = LocaleObject(
                        ru = messageService.getMessage("project.settings.search.retrieving.candidateRadius.label.ru"),
                        en = messageService.getMessage("project.settings.search.retrieving.candidateRadius.label.en")
                    ),
                    value = currentValue.search?.candidateRadius?.toDouble() ?: DEFAULT_CANDIDATE_RADIUS.toDouble(),
                    min = 0.0,
                    max = 10.0,
                    step = 1.0,
                    visibleStep = 5.0,
                    hint = LocaleObject(
                        ru = messageService.getMessage("project.settings.search.retrieving.candidateRadius.hint.ru"),
                        en = messageService.getMessage("project.settings.search.retrieving.candidateRadius.hint.en")
                    ),
                ),
                "metadata_search" to SwitchSettingDescription(
                    dependsOn = listOf(DependencyConfig("search.main.type", listOf("semantic"))),
                    label = LocaleObject(
                        ru = messageService.getMessage("project.settings.search.retrieving.useMetadata.label.ru"),
                        en = messageService.getMessage("project.settings.search.retrieving.useMetadata.label.en")
                    ),
                    value = currentValue.search?.useAllEmbeddings ?: false,
                    hint = LocaleObject(
                        ru = messageService.getMessage("project.settings.search.retrieving.useMetadata.hint.ru"),
                        en = messageService.getMessage("project.settings.search.retrieving.useMetadata.hint.en")
                    ),
                ),
                "rephrase" to BlockSettingDescription(
                    dependsOn = listOf(DependencyConfig("search.main.type", listOf("semantic"))),
                    label = null,
                    nestedSettings = mapOf(
                        "rephrase_query" to SwitchSettingDescription(
                            label = LocaleObject(
                                ru = messageService.getMessage("project.settings.search.retrieving.rephraseQuery.label.ru"),
                                en = messageService.getMessage("project.settings.search.retrieving.rephraseQuery.label.en")
                            ),
                            value = currentValue.search?.rephraseQuery ?: false
                        ),
                        "prompt" to PromptSettingDescription(
                            dependsOn = listOf(DependencyConfig("search.retrieving.rephrase.rephrase_query", listOf("true"))),
                            label = LocaleObject(
                                ru = messageService.getMessage("project.settings.search.retrieving.queryRephrasePrompt.label.ru"),
                                en = messageService.getMessage("project.settings.search.retrieving.queryRephrasePrompt.label.en")
                            ),
                            value = currentValue.search?.queryRephrasePrompt ?: promptsService.rephrasePrompt(),
                            hint = LocaleObject(
                                ru = messageService.getMessage("project.settings.search.retrieving.queryRephrasePrompt.hint.ru"),
                                en = messageService.getMessage("project.settings.search.retrieving.queryRephrasePrompt.hint.en")
                            ),
                            required = true
                        )
                    )
                ),
                "full_text" to BlockSettingDescription(
                    dependsOn = listOf(DependencyConfig("search.main.type", listOf("semantic"))),
                    label = null,
                    nestedSettings = mapOf(
                        "ft_search" to SwitchSettingDescription(
                            label = LocaleObject(
                                ru = messageService.getMessage("project.settings.search.retrieving.fullTextSearch.label.ru"),
                                en = messageService.getMessage("project.settings.search.retrieving.fullTextSearch.label.en")
                            ),
                            value = currentValue.search?.fullTextSearch?.ftSearch ?: false,
                            hint = LocaleObject(
                                ru = messageService.getMessage("project.settings.search.retrieving.fullTextSearch.hint.ru"),
                                en = messageService.getMessage("project.settings.search.retrieving.fullTextSearch.hint.en")
                            )
                        ),
                        "strategy" to CardsSettingDescription(
                            dependsOn = listOf(DependencyConfig("search.retrieving.full_text.ft_search", listOf("true"))),
                            label = null,
                            value = currentValue.search?.fullTextSearch?.strategy?.name ?: SearchStrategy.hybrid.name,
                            required = true,
                            options = asOptions(SearchStrategy.entries, "project.settings.search.retrieving.fullTextSearch.options")
                        ),
                        "semantic_portion" to SliderSettingDescription(
                            dependsOn = listOf(
                                DependencyConfig("search.retrieving.full_text.strategy", listOf(SearchStrategy.weighted.name)),
                                DependencyConfig("search.retrieving.full_text.ft_search", listOf("true"))
                            ),
                            format = NumberFormat.int,
                            label = LocaleObject(
                                ru = messageService.getMessage("project.settings.search.retrieving.fullTextSearch.hybrid.semanticPortion.label.ru"),
                                en = messageService.getMessage("project.settings.search.retrieving.fullTextSearch.hybrid.semanticPortion.label.en"),
                            ),
                            hint = LocaleObject(
                                ru = messageService.getMessage("project.settings.search.retrieving.fullTextSearch.hybrid.semanticPortion.hint.ru"),
                                en = messageService.getMessage("project.settings.search.retrieving.fullTextSearch.hybrid.semanticPortion.hint.en")
                            ),
                            value = currentValue.search?.fullTextSearch?.semanticPortion?.toDouble() ?: 10.0,
                            min = 0.0,
                            max = 10.0,
                            step = 1.0,
                            visibleStep = 5.0
                        ),
                        "fts_portion" to SliderSettingDescription(
                            dependsOn = listOf(
                                DependencyConfig("search.retrieving.full_text.strategy", listOf(SearchStrategy.weighted.name)),
                                DependencyConfig("search.retrieving.full_text.ft_search", listOf("true"))
                            ),
                            format = NumberFormat.int,
                            label = LocaleObject(
                                ru = messageService.getMessage("project.settings.search.retrieving.fullTextSearch.hybrid.ftsPortion.label.ru"),
                                en = messageService.getMessage("project.settings.search.retrieving.fullTextSearch.hybrid.ftsPortion.label.en"),
                            ),
                            hint = LocaleObject(
                                ru = messageService.getMessage("project.settings.search.retrieving.fullTextSearch.hybrid.ftsPortion.hint.ru"),
                                en = messageService.getMessage("project.settings.search.retrieving.fullTextSearch.hybrid.ftsPortion.hint.en")
                            ),
                            value = currentValue.search?.fullTextSearch?.ftsPortion?.toDouble() ?: 10.0,
                            min = 0.0,
                            max = 10.0,
                            step = 1.0,
                            visibleStep = 5.0
                        ),
                        "threshold" to SliderSettingDescription(
                            dependsOn = listOf(
                                DependencyConfig("search.retrieving.full_text.strategy", listOf(SearchStrategy.threshold.name)),
                                DependencyConfig("search.retrieving.full_text.ft_search", listOf("true"))
                            ),
                            format = NumberFormat.double,
                            label = LocaleObject(
                                ru = messageService.getMessage("project.settings.search.retrieving.fullTextSearch.threshold.label.ru"),
                                en = messageService.getMessage("project.settings.search.retrieving.fullTextSearch.threshold.label.en"),
                            ),
                            hint = LocaleObject(
                                ru = messageService.getMessage("project.settings.search.retrieving.fullTextSearch.threshold.hint.ru"),
                                en = messageService.getMessage("project.settings.search.retrieving.fullTextSearch.threshold.hint.en")
                            ),
                            value = currentValue.search?.fullTextSearch?.threshold ?: 10.0,
                            min = 0.0,
                            max = 1.0,
                            step = 0.1,
                            visibleStep = 0.5
                        )
                    )
                ),
                "history" to AccordionSettingDescription(
                    dependsOn = listOf(DependencyConfig("search.main.type", listOf("semantic"))),
                    label = null,
                    accordion = mapOf(
                        "use_history" to SwitchSettingDescription(
                            value = currentValue.search?.useHistory ?: false,
                            label = LocaleObject(
                                ru = messageService.getMessage("project.settings.search.retrieving.useHistory.label.ru"),
                                en = messageService.getMessage("project.settings.search.retrieving.useHistory.label.en")
                            )
                        )
                    ).entries.first(),
                    nestedSettings = mapOf(
                        "history_condense_prompt" to PromptSettingDescription(
                            dependsOn = listOf(DependencyConfig("search.retrieving.history.use_history", listOf("true"))),
                            label = LocaleObject(
                                ru = messageService.getMessage("project.settings.search.retrieving.historyCondensePrompt.label.ru"),
                                en = messageService.getMessage("project.settings.search.retrieving.historyCondensePrompt.label.en")
                            ),
                            hint = LocaleObject(
                                ru = messageService.getMessage("project.settings.search.retrieving.historyCondensePrompt.hint.ru"),
                                en = messageService.getMessage("project.settings.search.retrieving.historyCondensePrompt.hint.en")
                            ),
                            value = currentValue.search?.historyCondensePrompt ?: promptsService.rephraseWithHistoryPrompt(),
                            required = true
                        ),
                        "history_max_tokens" to SliderSettingDescription(
                            dependsOn = listOf(DependencyConfig("search.retrieving.history.use_history", listOf("true"))),
                            format = NumberFormat.int,
                            label = LocaleObject(
                                ru = messageService.getMessage("project.settings.search.retrieving.historyMaxSize.label.ru"),
                                en = messageService.getMessage("project.settings.search.retrieving.historyMaxSize.label.en"),
                            ),
                            value = currentValue.search?.historyMaxTokens?.toDouble() ?: 10_000.0,
                            min = 1000.0,
                            max = 16000.0,
                            step = 1000.0,
                            visibleStep = 5000.0
                        ),
                        "history_min_user_messages" to SliderSettingDescription(
                            dependsOn = listOf(DependencyConfig("search.retrieving.history.use_history", listOf("true"))),
                            format = NumberFormat.int,
                            label = LocaleObject(
                                ru = messageService.getMessage("project.settings.search.retrieving.historyMinUserMessages.label.ru"),
                                en = messageService.getMessage("project.settings.search.retrieving.historyMinUserMessages.label.en"),
                            ),
                            hint = LocaleObject(
                                ru = messageService.getMessage("project.settings.search.retrieving.historyMinUserMessages.hint.ru"),
                                en = messageService.getMessage("project.settings.search.retrieving.historyMinUserMessages.hint.en")
                            ),
                            value = currentValue.search?.historyMinUserMessages?.toDouble() ?: 2.0,
                            min = 1.0,
                            max = 50.0,
                            step = 1.0,
                            visibleStep = 7.0
                        )
                    )
                )
            )
        )
    }

    fun asOptions(entries: List<Enum<*>>, messagePrefix: String, changeDependenciesAfterSelect: ((String) -> List<DependencyConfig>)? = null): List<Option> {
        return entries.map {
            Option(
                title = LocaleObject(
                    ru = messageService.getMessage("${messagePrefix}.${it}.title.ru"),
                    en = messageService.getMessage("${messagePrefix}.${it}.title.en")
                ),
                description = LocaleObject(
                    ru = messageService.getMessage("${messagePrefix}.${it}.description.ru"),
                    en = messageService.getMessage("${messagePrefix}.${it}.description.en")
                ),
                value = it.name,
                changeDependenciesAfterSelect = changeDependenciesAfterSelect?.let { change -> change(it.name) } ?: emptyList()
            )
        }
    }
}
