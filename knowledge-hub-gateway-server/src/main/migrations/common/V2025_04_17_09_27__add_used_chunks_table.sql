CREATE TABLE public.used_chunks
(
    id          BIGINT GE<PERSON>RATED ALWAYS AS IDENTITY PRIMARY KEY,
    test_run_id  BIGINT UNIQUE REFERENCES public.test_run (id) ON DELETE CASCADE,
    chat_history_record_id  BIGINT UNIQUE REFERENCES public.chat_history (id) ON DELETE CASCADE,
    chunks JSONB NOT NULL
);

CREATE INDEX idx_used_chunks_test_run_id ON public.used_chunks(test_run_id);
CREATE INDEX idx_used_chunks_chat_history_record_id ON public.used_chunks(chat_history_record_id);

