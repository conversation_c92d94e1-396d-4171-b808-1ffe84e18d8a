package com.justai.khub.ratelimit.dto

import io.github.bucket4j.Bandwidth
import io.github.bucket4j.BandwidthBuilder
import java.time.Duration

data class UsageLimit(
    val capacity: Long,
    val refillDuration: Duration,
) {
    companion object {
        fun UsageLimit.asBandwidth(): Bandwidth {
            return BandwidthBuilder.builder()
                .capacity(this.capacity)
                .refillGreedy(this.capacity, this.refillDuration)
                .build()
        }
    }
}
