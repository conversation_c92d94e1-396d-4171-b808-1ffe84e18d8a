package com.justai.khub.project.dto

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
enum class ResponseMode {
    compact,
    compact_with_relevant_sources
}

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class ResponseGeneratorConfig(
    var prompt: String? = null,
    val mode: ResponseMode = ResponseMode.compact
)
