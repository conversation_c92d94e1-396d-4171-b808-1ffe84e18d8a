package com.justai.khub.ratelimit.service

import com.justai.khub.ratelimit.dto.UsageLimit
import com.justai.onprem.license.KHubModuleV1
import com.justai.onprem.license.KeyV1
import jakarta.annotation.PostConstruct
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.Clock
import java.time.Duration

@Service
class LicensingService(
    private val key: KeyV1?,
    private val clock: Clock,
) {
    private val log = LoggerFactory.getLogger(this::class.java)

    fun hasActiveKey(): Boolean = key?.isValidOnCurrentDate(clock) ?: false

    private val module = KHubModuleV1.findIn(key)
    val apiRequestsRpm = (module?.khubApiRequestsRpm ?: 0).toLong()

    @PostConstruct
    fun init() {
        if (!hasActiveKey()) {
            log.warn("No active license key found")
        } else {
            log.info("Successfully loaded license key: {}", key)
        }
    }

    val keyId: String
        get() = if (hasActiveKey()) {
            checkNotNull(key?.id)
        } else {
            "invalid-license-key"
        }

    fun getLimits(): List<UsageLimit> {
        return listOf(
            UsageLimit((module?.khubApiRequestsRpm ?: 1).toLong(), Duration.ofMinutes(1)),
        )
    }
}
