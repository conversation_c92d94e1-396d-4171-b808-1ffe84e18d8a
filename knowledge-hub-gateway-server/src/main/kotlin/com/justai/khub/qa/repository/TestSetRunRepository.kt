package com.justai.khub.qa.repository

import com.justai.khub.qa.entity.TestSetRunEntity
import com.justai.khub.qa.enumeration.TestSetRunStatus
import org.springframework.data.domain.PageRequest
import org.springframework.data.jpa.repository.*
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface TestSetRunRepository : JpaRepository<TestSetRunEntity, Long>, JpaSpecificationExecutor<TestSetRunEntity> {

    @Query(
        "SELECT new kotlin.Pair(t.testSet.id as testSetId, COUNT(t.id) as count)" +
            " FROM TestSetRunEntity t" +
            " WHERE t.testSet.id IN :testSetIds" +
            " GROUP BY t.testSet.id"
    )
    fun countByTestSetIdIn(testSetIds: List<Long>): List<Pair<Long, Long>>

    @EntityGraph(attributePaths = ["testSet"])
    @Query(
        """
        SELECT tsr
        FROM TestSetRunEntity tsr
        WHERE tsr.id IN (
            SELECT MAX(innerTsr.id)
            FROM TestSetRunEntity innerTsr
            WHERE innerTsr.testSet.id IN :testSetIds
              AND innerTsr.status IN :statuses
            GROUP BY innerTsr.testSet.id
        )
    """
    )
    fun findLastActiveByTestSetIdsAndStatuses(testSetIds: List<Long>, statuses: List<TestSetRunStatus>): List<TestSetRunEntity>

    @EntityGraph(attributePaths = ["testSet"])
    fun findByTestSetIdAndStatusIn(testSetId: Long, statuses: List<TestSetRunStatus>): List<TestSetRunEntity>

    @EntityGraph(attributePaths = ["testSet"])
    fun findAllByStatusIn(status: List<TestSetRunStatus>, pagination: PageRequest): List<TestSetRunEntity>

    @Modifying
    @Query(
        "UPDATE TestSetRunEntity tsr " +
            "SET tsr.status = :newStatus, tsr.processingTimeMs = :processingTimeMs, tsr.lastError = :lastError, tsr.updatedAt = :updatedAt " +
            "WHERE tsr.id = :id AND tsr.status IN :expectedStatuses"
    )
    fun updateStatus(
        id: Long,
        expectedStatuses: List<TestSetRunStatus>,
        newStatus: TestSetRunStatus,
        lastError: String? = null,
        processingTimeMs: Long? = null,
        updatedAt: LocalDateTime = LocalDateTime.now()
    ): Int

    @Modifying
    @Query(
        "UPDATE TestSetRunEntity tsr " +
            "SET tsr.finishedTests = tsr.finishedTests + :testsIncrement, " +
            "tsr.usedPromptTokens =  tsr.usedPromptTokens + :promptTokensIncrement, " +
            "tsr.usedCompletionTokens = tsr.usedCompletionTokens + :completionTokensIncrement, " +
            "tsr.updatedAt = :updatedAt " +
            "WHERE tsr.id = :id"
    )
    fun incrementFinishedTestsCountAndUsage(
        id: Long,
        testsIncrement: Int,
        promptTokensIncrement: Int,
        completionTokensIncrement: Int,
        updatedAt: LocalDateTime = LocalDateTime.now()
    ): Int

    @Modifying
    @Query(
        "UPDATE TestSetRunEntity tsr " +
            "SET tsr.meanScore = ( " +
            "SELECT AVG(te.score) " +
            "FROM TestEvaluationEntity te " +
            "JOIN te.testRun tr " +
            "WHERE tr.testSetRun.id = :testSetRunId " +
            "AND te.score IS NOT NULL " +
            ") WHERE tsr.id = :testSetRunId"
    )
    fun updateMeanScore(testSetRunId: Long)
}
