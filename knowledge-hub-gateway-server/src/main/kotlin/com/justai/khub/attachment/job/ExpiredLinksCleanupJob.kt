package com.justai.khub.attachment.job

import com.justai.khub.attachment.service.AttachmentService
import com.justai.shutdown.CallbackAwaiter
import com.justai.shutdown.GracefulShutDownAware
import com.justai.shutdown.ShutDownLatch
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@ConditionalOnProperty(value = ["scheduling.enabled"], havingValue = "true", matchIfMissing = true)
@Component
class ExpiredLinksCleanupJob(
    private val attachmentService: AttachmentService
) : GracefulShutDownAware {
    private val log = LoggerFactory.getLogger(this::class.java)

    private val shutDownLatch = ShutDownLatch()

    @Scheduled(fixedDelay = 5 * 60 * 60_00)
    fun cleanupExpiredLinks() {
        shutDownLatch.run {
            val deletedExpiredLinksCount = attachmentService.deleteExpiredLinks()
            if (deletedExpiredLinksCount > 0) {
                log.info("Deleted {} expired attachment links", deletedExpiredLinksCount)
            }
        }
    }

    override fun shutDownGracefully(runnable: Runnable) {
        CallbackAwaiter.withCallback(runnable).awaitAsync {
            log.info("Stop ExpiredLinksCleanupJob...")
            shutDownLatch.awaitShutdown()
            log.info("Stop ExpiredLinksCleanupJob... DONE")
        }
    }
}
