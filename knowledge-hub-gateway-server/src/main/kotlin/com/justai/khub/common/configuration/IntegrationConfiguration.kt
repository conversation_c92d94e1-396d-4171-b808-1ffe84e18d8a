package com.justai.khub.common.configuration

import com.justai.http.JustAIHttpClientBuilder
import com.justai.http.configuration.HttpClientSettings
import com.justai.http.configuration.HttpProxySettings
import com.justai.khub.common.configuration.dto.IntegrationProperties
import com.justai.khub.common.configuration.dto.LoggingProperties
import com.justai.khub.common.configuration.service.BasicAuthInterceptor
import com.justai.khub.common.configuration.service.LoggingInterceptor
import com.justai.khub.common.configuration.service.OkHttp3LoggingInterceptor
import com.justai.khub.common.enumeration.IntegrationName
import com.justai.khub.common.service.MetricsService
import com.justai.khub.common.util.CustomHeaders
import com.justai.khub.common.util.JSON
import com.justai.khub.common.util.LoggingUtils
import com.justai.khub.ratelimit.service.RequestSigningInterceptor
import com.justai.loadbalancer.annotations.HttpExternal
import com.justai.loadbalancer.annotations.HttpInternal
import io.lakefs.clients.sdk.ApiClient
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.cloud.client.loadbalancer.LoadBalanced
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpRequest
import org.springframework.http.client.*
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.stereotype.Component
import org.springframework.web.client.RestTemplate

@Component
@ConfigurationProperties(prefix = "external-http-client")
class ExternalClientSettings : HttpClientSettings()

@Component
@ConfigurationProperties(prefix = "internal-http-client")
class InternalHttpClientSettings : HttpClientSettings()

@Configuration
class IntegrationConfiguration(
    private val loggingProperties: LoggingProperties,
    private val requestSigningInterceptor: RequestSigningInterceptor
) {

    @Bean
    fun lakeFSClient(integrationProperties: IntegrationProperties): ApiClient {
        val lakefsProperties = integrationProperties.lakefs
        return io.lakefs.clients.sdk.Configuration.getDefaultApiClient().apply {
            setHttpClient(
                httpClient.newBuilder()
                    .addInterceptor(OkHttp3LoggingInterceptor(IntegrationName.lakefs, loggingProperties))
                    .build()
            )
            setUsername(lakefsProperties.accessKeyId)
            setBasePath(lakefsProperties.baseUrl)
            setPassword(lakefsProperties.secretAccessKey)
            setConnectTimeout(lakefsProperties.connectTimeout)
            setReadTimeout(lakefsProperties.readTimeout)
        }
    }

    @Bean
    @HttpInternal
    fun httpClient(metricsService: MetricsService, conf: InternalHttpClientSettings): CloseableHttpClient {
        val socketFactory = JustAIHttpClientBuilder.getConnectionSocketFactory(conf)
        val httpClientConnectionManager = JustAIHttpClientBuilder.createConnectionManager(conf, socketFactory)
        val httpClientBuilder = JustAIHttpClientBuilder.createHttpClientBuilder(conf, HttpProxySettings(), httpClientConnectionManager)
        metricsService.addApacheHttpClientMetrics("internal", httpClientConnectionManager)
        return httpClientBuilder.build()
    }

    @Bean
    @HttpExternal
    fun externalHttpClient(metricsService: MetricsService, conf: ExternalClientSettings): CloseableHttpClient {
        val socketFactory = JustAIHttpClientBuilder.getConnectionSocketFactory(conf)
        val httpClientConnectionManager = JustAIHttpClientBuilder.createConnectionManager(conf, socketFactory)
        val httpClientBuilder = JustAIHttpClientBuilder.createHttpClientBuilder(conf, HttpProxySettings(), httpClientConnectionManager)
        metricsService.addApacheHttpClientMetrics("external", httpClientConnectionManager)
        return httpClientBuilder.build()
    }

    @Bean
    @HttpExternal
    fun unbufferedRestTemplate(@HttpExternal externalHttpClient: CloseableHttpClient): RestTemplate {
        return buildUnBufferedRestTemplate(externalHttpClient).also {
            it.interceptors.addAll(listOf(LoggingInterceptor(null, loggingProperties)))
        }
    }

    @ConditionalOnProperty(value = ["integrations.rag.eureka-enabled"], havingValue = "true", matchIfMissing = true)
    @Bean(name = ["ragServiceRestTemplate"])
    @HttpInternal
    @LoadBalanced
    fun loadBalancedRagServiceRestTemplate(@HttpInternal internalHttpClient: CloseableHttpClient, integrationProperties: IntegrationProperties): RestTemplate {
        return createRagServiceRestTemplate(internalHttpClient, integrationProperties)
    }

    @ConditionalOnProperty(value = ["integrations.rag.eureka-enabled"], havingValue = "false", matchIfMissing = false)
    @Bean(name = ["ragServiceRestTemplate"])
    @HttpInternal
    fun ragServiceRestTemplate(@HttpInternal internalHttpClient: CloseableHttpClient, integrationProperties: IntegrationProperties): RestTemplate {
        return createRagServiceRestTemplate(internalHttpClient, integrationProperties)
    }

    fun createRagServiceRestTemplate(internalHttpClient: CloseableHttpClient, integrationProperties: IntegrationProperties): RestTemplate {
        return buildRestTemplate(internalHttpClient).also {
            it.interceptors.addAll(
                listOf(
                    BasicAuthInterceptor(integrationProperties.rag.username, integrationProperties.rag.password),
                    LoggingInterceptor(IntegrationName.ragService, loggingProperties),
                    requestSigningInterceptor,
                )
            )
        }
    }


    @ConditionalOnProperty(value = ["integrations.aa.eureka-enabled"], havingValue = "true", matchIfMissing = true)
    @Bean(name = ["accountsAdminRestTemplate"])
    @HttpInternal
    @LoadBalanced
    fun loadBalancedAccountsAdminRestTemplate(@HttpInternal internalHttpClient: CloseableHttpClient, integrationProperties: IntegrationProperties): RestTemplate {
        return createAccountsAdminRestTemplate(internalHttpClient, integrationProperties)
    }

    @ConditionalOnProperty(value = ["integrations.aa.eureka-enabled"], havingValue = "false", matchIfMissing = false)
    @Bean(name = ["accountsAdminRestTemplate"])
    @HttpInternal
    fun accountsAdminRestTemplate(@HttpInternal internalHttpClient: CloseableHttpClient, integrationProperties: IntegrationProperties): RestTemplate {
        return createAccountsAdminRestTemplate(internalHttpClient, integrationProperties)
    }

    fun createAccountsAdminRestTemplate(internalHttpClient: CloseableHttpClient, integrationProperties: IntegrationProperties): RestTemplate {
        return buildRestTemplate(internalHttpClient).also {
            it.interceptors.addAll(
                listOf(
                    BasicAuthInterceptor(integrationProperties.aa.username, integrationProperties.aa.password),
                    LoggingInterceptor(IntegrationName.aa, loggingProperties)
                )
            )
        }
    }

    @ConditionalOnProperty(value = ["integrations.ingester.eureka-enabled"], havingValue = "true", matchIfMissing = true)
    @Bean(name = ["ingesterRestTemplate"])
    @HttpInternal
    @LoadBalanced
    fun loadBalancedIngesterRestTemplate(@HttpInternal internalHttpClient: CloseableHttpClient, integrationProperties: IntegrationProperties): RestTemplate {
        return createIngesterRestTemplate(internalHttpClient, integrationProperties)
    }

    @ConditionalOnProperty(value = ["integrations.ingester.eureka-enabled"], havingValue = "false", matchIfMissing = false)
    @Bean(name = ["ingesterRestTemplate"])
    @HttpInternal
    fun ingesterRestTemplate(@HttpInternal internalHttpClient: CloseableHttpClient, integrationProperties: IntegrationProperties): RestTemplate {
        return createIngesterRestTemplate(internalHttpClient, integrationProperties)
    }

    fun createIngesterRestTemplate(internalHttpClient: CloseableHttpClient, integrationProperties: IntegrationProperties): RestTemplate {
        return buildRestTemplate(internalHttpClient).also {
            it.interceptors.addAll(
                listOf(
                    BasicAuthInterceptor(integrationProperties.ingester.username, integrationProperties.ingester.password),
                    LoggingInterceptor(IntegrationName.ingester, loggingProperties),
                    requestSigningInterceptor,
                )
            )
        }
    }

    @Bean
    @HttpInternal
    @LoadBalanced
    fun evaluationServiceRestTemplate(
        @HttpInternal internalHttpClient: CloseableHttpClient,
        integrationProperties: IntegrationProperties,
    ): RestTemplate {
        return buildRestTemplate(internalHttpClient).also {
            it.interceptors.addAll(
                listOf(
                    BasicAuthInterceptor(integrationProperties.evaluator.username, integrationProperties.evaluator.password),
                    LoggingInterceptor(IntegrationName.evaluationService, loggingProperties),
                    requestSigningInterceptor,
                )
            )
        }
    }

    @Bean
    @HttpExternal
    fun confluenceRestTemplate(@HttpExternal externalHttpClient: CloseableHttpClient): RestTemplate {
        return buildRestTemplate(externalHttpClient).also {
            it.interceptors.addAll(
                listOf(
                    LoggingInterceptor(IntegrationName.confluence, loggingProperties)
                )
            )
        }
    }

    @Bean
    @HttpExternal
    fun minervaRestTemplate(@HttpExternal externalHttpClient: CloseableHttpClient): RestTemplate {
        return buildRestTemplate(externalHttpClient).also {
            it.interceptors.addAll(
                listOf(
                    LoggingInterceptor(IntegrationName.minerva, loggingProperties)
                )
            )
        }
    }

    @ConditionalOnProperty(value = ["integrations.parser.eureka-enabled"], havingValue = "true", matchIfMissing = true)
    @Bean(name = ["parsingServiceRestTemplate"])
    @HttpInternal
    @LoadBalanced
    fun loadBalancedParsingServiceRestTemplate(@HttpInternal internalHttpClient: CloseableHttpClient, integrationProperties: IntegrationProperties): RestTemplate {
        return createParsingServiceRestTemplate(internalHttpClient, integrationProperties)
    }

    @ConditionalOnProperty(value = ["integrations.parser.eureka-enabled"], havingValue = "false", matchIfMissing = false)
    @Bean(name = ["parsingServiceRestTemplate"])
    @HttpInternal
    fun parsingServiceRestTemplate(@HttpInternal internalHttpClient: CloseableHttpClient, integrationProperties: IntegrationProperties): RestTemplate {
        return createParsingServiceRestTemplate(internalHttpClient, integrationProperties)
    }

    fun createParsingServiceRestTemplate(internalHttpClient: CloseableHttpClient, integrationProperties: IntegrationProperties): RestTemplate {
        return buildRestTemplate(internalHttpClient).also {
            it.uriTemplateHandler
            it.interceptors.addAll(
                listOf(
                    BasicAuthInterceptor(integrationProperties.parser.username, integrationProperties.parser.password),
                    LoggingInterceptor(IntegrationName.parsingService, loggingProperties, listOf("^/api/task/[0-9a-f-]+$".toRegex())),
                    requestSigningInterceptor,
                )
            )
        }
    }

    @Bean
    @HttpInternal
    fun jaicpRestTemplate(
        @HttpInternal internalHttpClient: CloseableHttpClient,
        integrationProperties: IntegrationProperties,
    ): RestTemplate {
        return buildRestTemplate(internalHttpClient).also {
            it.interceptors.addAll(
                listOf(
                    LoggingInterceptor(IntegrationName.jaicp, loggingProperties)
                )
            )
        }
    }

    fun buildMdcSettingInterceptor(): ClientHttpRequestInterceptor {
        return ClientHttpRequestInterceptor { request: HttpRequest, body: ByteArray?, execution: ClientHttpRequestExecution ->
            if (request.headers[CustomHeaders.REQUEST_ID] == null) {
                request.headers[CustomHeaders.REQUEST_ID] = LoggingUtils.getRequestId()
            }
            execution.execute(request, body!!)
        }
    }

    @Bean
    @HttpExternal
    fun atlassianRestTemplate(@HttpInternal internalHttpClient: CloseableHttpClient): RestTemplate {
        return buildRestTemplate(internalHttpClient).also {
            it.interceptors.addAll(
                listOf(
                    LoggingInterceptor(IntegrationName.atlassian, loggingProperties)
                )
            )
        }
    }

    private fun buildRestTemplate(httpClient: CloseableHttpClient): RestTemplate {
        val factory = HttpComponentsClientHttpRequestFactory(httpClient)
        val bufferingClientHttpRequestFactory = BufferingClientHttpRequestFactory(factory)
        val restTemplate = RestTemplate(bufferingClientHttpRequestFactory)

        val jacksonConverter = restTemplate.messageConverters.find {
            it is MappingJackson2HttpMessageConverter
        } as MappingJackson2HttpMessageConverter

        jacksonConverter.objectMapper = JSON.mapper
        restTemplate.interceptors.add(
            buildMdcSettingInterceptor()

        )
        return restTemplate
    }

    private fun buildUnBufferedRestTemplate(httpClient: CloseableHttpClient): RestTemplate {
        val factory = HttpComponentsClientHttpRequestFactory(httpClient)
        val bufferingClientHttpRequestFactory = InterceptingClientHttpRequestFactory(factory, listOf(buildMdcSettingInterceptor()))
        val restTemplate = RestTemplate(bufferingClientHttpRequestFactory)

        val jacksonConverter = restTemplate.messageConverters.find {
            it is MappingJackson2HttpMessageConverter
        } as MappingJackson2HttpMessageConverter

        jacksonConverter.objectMapper = JSON.mapper
        return restTemplate
    }
}
