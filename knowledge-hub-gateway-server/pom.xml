<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.justai</groupId>
        <artifactId>knowledge-hub-gateway</artifactId>
        <version>2025.2-rc-SNAPSHOT</version>
    </parent>

    <artifactId>knowledge-hub-gateway-server</artifactId>

    <properties>
        <testcontainers.version>1.19.8</testcontainers.version>
        <postgresql.version>42.7.3</postgresql.version>
        <testcontainers.version>1.15.3</testcontainers.version>
        <license-utils.version>1.1.0-SNAPSHOT</license-utils.version>
    </properties>

    <dependencies>
        <!--        JUSTAI        -->
        <dependency>
            <groupId>com.justai</groupId>
            <artifactId>http5</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.reactivex</groupId>
                    <artifactId>rxjava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-collections4</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.netflix.ribbon</groupId>
                    <artifactId>ribbon-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.netflix.ribbon</groupId>
                    <artifactId>ribbon-examples</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.netflix.ribbon</groupId>
                    <artifactId>ribbon-httpclient</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.netflix.ribbon</groupId>
                    <artifactId>ribbon-loadbalancer</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.netflix.ribbon</groupId>
                    <artifactId>ribbon-transport</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.justai</groupId>
            <artifactId>infrastructure-starter-spring-boot-${spring-boot.minor}</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.jetbrains.kotlin</groupId>
                    <artifactId>kotlin-stdlib-jdk8</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.yaml</groupId>
                    <artifactId>snakeyaml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.justai</groupId>
            <artifactId>migrations-spring-boot-${spring-boot.minor}</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-autoconfigure</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.flywaydb</groupId>
                    <artifactId>flyway-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.justai</groupId>
            <artifactId>protected-properties-starter-spring-boot-${spring-boot.minor}</artifactId>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
            <version>10.15.2</version>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-database-postgresql</artifactId>
            <version>10.15.2</version>
        </dependency>
        <dependency>
            <groupId>com.justai</groupId>
            <artifactId>load-balancer-starter-spring-boot-${spring-boot.minor}</artifactId>
        </dependency>
        <dependency>
            <groupId>com.justai</groupId>
            <artifactId>eureka-discovery-starter-spring-boot-${spring-boot.minor}</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.netflix.netflix-commons</groupId>
                    <artifactId>netflix-eventbus</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcpkix-jdk15on</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>joda-time</groupId>
                    <artifactId>joda-time</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.code.findbugs</groupId>
                    <artifactId>jsr305</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-commons</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.justai</groupId>
            <artifactId>just-security-servlet-starter-spring-boot-${spring-boot.minor}</artifactId>
        </dependency>
        <dependency>
            <groupId>com.justai</groupId>
            <artifactId>license-utils-starter-boot-${spring-boot.minor}</artifactId>
        </dependency>
        <dependency>
            <groupId>com.justai</groupId>
            <artifactId>micrometer-khub-${spring-boot.minor}</artifactId>
        </dependency>
        <dependency>
            <groupId>com.justai</groupId>
            <artifactId>signature-utils</artifactId>
        </dependency>

        <!--        SPRING        -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.yaml</groupId>
                    <artifactId>snakeyaml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.ow2.asm</groupId>
                    <artifactId>asm</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>


        <!--        KOTLIN        -->
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-reflect</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-test-junit</artifactId>
            <version>${kotlin.version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.jetbrains.kotlin</groupId>
                    <artifactId>kotlin-test</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-jdk8</artifactId>
            <version>${kotlin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mockito.kotlin</groupId>
            <artifactId>mockito-kotlin</artifactId>
            <version>5.3.1</version>
            <scope>test</scope>
        </dependency>

        <!--        CUSTOM        -->
        <dependency>
            <groupId>com.netflix.netflix-commons</groupId>
            <artifactId>netflix-eventbus</artifactId>
            <version>0.3.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.netflix.servo</groupId>
                    <artifactId>servo-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.netflix.archaius</groupId>
                    <artifactId>archaius-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.code.findbugs</groupId>
                    <artifactId>jsr305</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>joda-time</groupId>
                    <artifactId>joda-time</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.swagger.parser.v3</groupId>
            <artifactId>swagger-parser</artifactId>
            <version>2.1.22</version>
            <exclusions>
                <exclusion>
                    <groupId>io.swagger.parser.v3</groupId>
                    <artifactId>swagger-parser-v2-converter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.code.findbugs</groupId>
                    <artifactId>jsr305</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.fge</groupId>
                    <artifactId>jackson-coreutils</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${postgresql.version}</version>
        </dependency>
        <dependency>
            <groupId>com.impossibl.pgjdbc-ng</groupId>
            <artifactId>pgjdbc-ng</artifactId>
            <version>0.8.9</version>
        </dependency>
        <dependency>
            <groupId>com.ibm.icu</groupId>
            <artifactId>icu4j</artifactId>
            <version>71.1</version>
        </dependency>
        <dependency>
            <groupId>io.hypersistence</groupId>
            <artifactId>hypersistence-utils-hibernate-63</artifactId>
            <version>3.8.1</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
        </dependency>
        <dependency>
            <groupId>de.appelgriepsch.logback</groupId>
            <artifactId>logback-gelf-appender</artifactId>
            <version>1.5</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.16.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>1.11.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-core</artifactId>
            <version>3.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>postgresql</artifactId>
            <version>${testcontainers.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>${testcontainers.version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--Problem - When you migrate your SpringBoot project from older version to newer like 2.5.x then the existing junit test cases are ignored by maven. https://stackoverflow.com/questions/31681855/maven-not-running-spring-boot-tests-->
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <version>5.10.3</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apiguardian</groupId>
                    <artifactId>apiguardian-api</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-core</artifactId>
            <version>2.2</version>
            <scope>test</scope>
        </dependency>

        <!-- Manual fix for outdated transitive dependencies. TODO remove after updating infrastructure-starter and other libs -->
        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
            <version>5.2.1</version>
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>2.2</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-cbor</artifactId>
            <version>${jackson.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.nimbusds</groupId>
            <artifactId>nimbus-jose-jwt</artifactId>
            <version>9.40</version>
        </dependency>
        <dependency>
            <groupId>jakarta.activation</groupId>
            <artifactId>jakarta.activation-api</artifactId>
            <version>2.1.3</version>
        </dependency>
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.3.1</version>
        </dependency>
        <dependency>
            <groupId>io.lakefs</groupId>
            <artifactId>sdk</artifactId>
            <version>1.28.2</version>
        </dependency>
        <dependency>
            <groupId>com.bucket4j</groupId>
            <artifactId>bucket4j_jdk17-redisson</artifactId>
            <version>8.14.0</version>
        </dependency>
        <dependency>
            <groupId>com.bucket4j</groupId>
            <artifactId>bucket4j_jdk17-postgresql</artifactId>
            <version>8.14.0</version>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.justai</groupId>
                <artifactId>license-utils-starter-boot-${spring-boot.minor}</artifactId>
                <version>${license-utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.justai</groupId>
                <artifactId>micrometer-khub-${spring-boot.minor}</artifactId>
                <version>${license-utils.version}</version>
            </dependency>
            <dependency>
                <groupId>com.justai</groupId>
                <artifactId>signature-utils</artifactId>
                <version>${license-utils.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <sourceDirectory>src/main/kotlin</sourceDirectory>
        <testSourceDirectory>src/test/kotlin</testSourceDirectory>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>version.json</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <excludes>
                    <exclude>version.json</exclude>
                </excludes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <version>${openapi-generator-maven.version}</version>
                <executions>
                    <execution>
                        <id>generate-public-server</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/specs/knowledge-hub-gateway.yml</inputSpec>
                            <templateDirectory>${project.basedir}/src/main/resources/specs/templates</templateDirectory>
                            <generatorName>kotlin-spring</generatorName>
                            <generateSupportingFiles>false</generateSupportingFiles>
                            <typeMappings>
                                <mapping>file=org.springframework.core.io.Resource</mapping>
                                <mapping>date-time=java.time.Instant</mapping>
                                <mapping>DateTime=java.time.Instant</mapping>
                            </typeMappings>
                            <skipValidateSpec>true</skipValidateSpec>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <configOptions>
                                <useTags>true</useTags>
                                <java8>true</java8>
                                <sourceFolder>/</sourceFolder>
                                <serviceInterface>true</serviceInterface>
                                <enumPropertyNaming>UPPERCASE</enumPropertyNaming>
                                <useSpringBoot3>true</useSpringBoot3>
                                <discriminatorCaseSensitive>true</discriminatorCaseSensitive>
                            </configOptions>
                            <schemaMappings>ObjectNode=com.fasterxml.jackson.databind.node.ObjectNode,JsonNode=com.fasterxml.jackson.databind.JsonNode
                            </schemaMappings>
                            <modelPackage>com.justai.khub.api.fe.model</modelPackage>
                            <apiPackage>com.justai.khub.api.fe</apiPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>generate-public-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/specs/knowledge-hub-public-api.yml</inputSpec>
                            <templateDirectory>${project.basedir}/src/main/resources/specs/templates</templateDirectory>
                            <generatorName>kotlin-spring</generatorName>
                            <generateSupportingFiles>false</generateSupportingFiles>
                            <typeMappings>
                                <mapping>file=org.springframework.core.io.Resource</mapping>
                                <mapping>date-time=java.time.Instant</mapping>
                                <mapping>DateTime=java.time.Instant</mapping>
                            </typeMappings>
                            <skipValidateSpec>true</skipValidateSpec>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <configOptions>
                                <useTags>true</useTags>
                                <java8>true</java8>
                                <sourceFolder>/</sourceFolder>
                                <serviceInterface>true</serviceInterface>
                                <enumPropertyNaming>UPPERCASE</enumPropertyNaming>
                                <useSpringBoot3>true</useSpringBoot3>
                            </configOptions>
                            <schemaMappings>ObjectNode=com.fasterxml.jackson.databind.node.ObjectNode,JsonNode=com.fasterxml.jackson.databind.JsonNode
                            </schemaMappings>
                            <modelPackage>com.justai.khub.api.public.model</modelPackage>
                            <apiPackage>com.justai.khub.api.public</apiPackage>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <id>build info</id>
                        <goals>
                            <goal>build-info</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                        <configuration>
                            <skip>true</skip>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.0</version>
                <configuration>
                    <proc>none</proc>
                    <source>17</source>
                    <target>17</target>
                </configuration>
                <executions>
                    <execution>
                        <id>default-compile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>default-testCompile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>java-compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>java-test-compile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
                <version>${kotlin.version}</version>
                <configuration>
                    <args>
                        <arg>-Xjsr305=strict</arg>
                    </args>
                    <compilerPlugins>
                        <plugin>spring</plugin>
                        <plugin>jpa</plugin>
                    </compilerPlugins>
                    <apiVersion>${kotlin.apiVersion}</apiVersion>
                    <languageVersion>${kotlin.languageVersion}</languageVersion>
                    <jvmTarget>17</jvmTarget>
                </configuration>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-allopen</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-noarg</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                </dependencies>
            </plugin>

            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>2.2.6</version>
                <executions>
                    <execution>
                        <id>get-the-git-infos</id>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                </configuration>
            </plugin>

            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>jks</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <filtering>true</filtering>
                                    <directory>${basedir}/src/main/build</directory>
                                </resource>
                            </resources>
                            <outputDirectory>${project.build.directory}</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>${project.build.directory}/generated-sources/openapi</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.5.2</version>
                <configuration>
                    <argLine>-Dfile.encoding=UTF-8 -Xmx512m</argLine>
                    <excludes>
                        <exclude>**/*WBTest.java</exclude>
                        <exclude>**/*WBT.java</exclude>
                        <exclude>**/*IT.java</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <version>3.5.2</version>
                <configuration>
                    <argLine>-Dfile.encoding=UTF-8</argLine>
                    <includes>
                        <include>**/*IT.java</include>
                        <include>**/*WBT.java</include>
                        <include>**/*WBTest.java</include>
                    </includes>
                    <excludes>
                        <exclude/>
                    </excludes>
                    <skipITs>${skipITs}</skipITs>
                    <rerunFailingTestsCount>3</rerunFailingTestsCount>
                    <forkCount>1</forkCount>
                    <reuseForks>true</reuseForks>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>integration-test</goal>
                            <goal>verify</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>docker</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.justai</groupId>
                        <artifactId>package-maven-plugin</artifactId>
                        <version>1.1.6-SNAPSHOT</version>
                        <configuration>
                            <mainClass>com.justai.khub.KHGatewayApplicationKt</mainClass>
                            <useJava17>true</useJava17>
                            <useRedHatBaseImage>${useRedHatBaseImage}</useRedHatBaseImage>
                            <migrationsMainClass>com.justai.khub.Migrations</migrationsMainClass>
                            <serviceName>knowledge-hub-gateway</serviceName>
                            <port>9333</port>
                            <confSourceExclusions>
                                <exclusion>application-dev.yml</exclusion>
                                <exclusion>application-local.yml</exclusion>
                                <exclusion>application-test.yml</exclusion>
                            </confSourceExclusions>
                            <dockerDependencyLayers>
                                <spring>org.springframework*:*</spring>
                                <lib>*:*</lib>
                                <justai>com.justai:*</justai>
                            </dockerDependencyLayers>
                            <skipRpm>false</skipRpm>
                            <skipDocker>false</skipDocker>
                            <additionalJvmOpts>--add-opens java.base/java.time=ALL-UNNAMED</additionalJvmOpts>
                        </configuration>
                        <executions>
                            <execution>
                                <id>generate-docker-files</id>
                                <goals>
                                    <goal>docker</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>com.spotify</groupId>
                        <artifactId>dockerfile-maven-plugin</artifactId>
                        <version>1.4.10</version>
                        <executions>
                            <execution>
                                <id>default</id>
                                <goals>
                                    <goal>build</goal>
                                    <goal>push</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <skipBuild>false</skipBuild>
                            <skipPush>false</skipPush>
                            <contextDirectory>${project.build.directory}/docker-context</contextDirectory>
                            <repository>docker-hub.just-ai.com/components/knowledge-hub-gateway-${branch.name.lower}</repository>
                            <noCache>false</noCache>
                            <tag>trunk-${build.number}</tag>
                        </configuration>
                    </plugin>
                    <plugin>
                        <artifactId>exec-maven-plugin</artifactId>
                        <groupId>org.codehaus.mojo</groupId>
                        <version>1.6.0</version>
                        <executions>
                            <execution>
                                <id>docker tag</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker</executable>
                                    <arguments>
                                        <argument>tag</argument>
                                        <argument>
                                            docker-hub.just-ai.com/components/knowledge-hub-gateway-${branch.name.lower}:trunk-${build.number}
                                        </argument>
                                        <argument>
                                            docker-hub.just-ai.com/components/knowledge-hub-gateway-${branch.name.lower}:trunk-latest
                                        </argument>
                                    </arguments>
                                </configuration>
                            </execution>
                            <execution>
                                <id>docker push</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker</executable>
                                    <arguments>
                                        <argument>push</argument>
                                        <argument>
                                            docker-hub.just-ai.com/components/knowledge-hub-gateway-${branch.name.lower}:trunk-latest
                                        </argument>
                                    </arguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
