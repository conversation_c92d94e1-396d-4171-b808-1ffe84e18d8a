package com.justai.khub.project.mapper

import com.justai.khub.common.configuration.dto.RagProperties
import com.justai.khub.common.service.MessageService
import com.justai.khub.project.dto.*
import org.springframework.stereotype.Component

@Component
class RerankerSettingsMapper(
    private val messageService: MessageService,
    private val ragProperties: RagProperties
) {

    fun rerankerSection(currentValue: RerankerConfig?): SettingsSection {
        val availableRerankerTypes = RerankerType.entries.filter {
            it != RerankerType.noop // for backward compatibility
            && ragProperties.reranker[it]?.enabled ?: true
        }
        return SettingsSection(
            dependsOn = listOf(DependencyConfig("search.main.type", listOf("semantic"))),
            isCard = false,
            title = null,
            description = null,
            settings = mapOf(
                "use_reranker" to SwitchSettingDescription(
                    label = LocaleObject(
                        ru = messageService.getMessage("project.settings.search.reranker.title.ru"),
                        en = messageService.getMessage("project.settings.search.reranker.title.en")
                    ),
                    hint = LocaleObject(
                        ru = messageService.getMessage("project.settings.search.reranker.hint.ru"),
                        en = messageService.getMessage("project.settings.search.reranker.hint.en")
                    ),
                    value = currentValue != null
                ),
                "type" to CardsSettingDescription(
                    dependsOn = listOf(
                        DependencyConfig("search.reranker.use_reranker", listOf("true"))
                    ),
                    label = null,
                    value = currentValue?.type?.takeIf { it != RerankerType.noop }?.name ?: RerankerType.manual.name,
                    required = true,
                    options = asOptions(availableRerankerTypes, "project.settings.search.reranker.type.options")
                ),
                "max_chunks" to SliderSettingDescription(
                    dependsOn = listOf(
                        DependencyConfig(
                            "search.reranker.type",
                            listOf(RerankerType.manual.name, RerankerType.model.name)
                        ),
                        DependencyConfig("search.reranker.use_reranker", listOf("true"))
                    ),
                    format = NumberFormat.int,
                    label = LocaleObject(
                        ru = messageService.getMessage("project.settings.search.reranker.maxChunks.label.ru"),
                        en = messageService.getMessage("project.settings.search.reranker.maxChunks.label.en")
                    ),
                    value = if (currentValue is ManualRerankerConfig) currentValue.maxChunks.toDouble() else if (currentValue is ModelRerankerConfig) currentValue.maxChunks.toDouble() else 5.0,
                    min = 5.0,
                    max = 20.0,
                    step = 1.0,
                    visibleStep = 5.0,
                    hint = LocaleObject(
                        ru = messageService.getMessage("project.settings.search.reranker.maxChunks.hint.ru"),
                        en = messageService.getMessage("project.settings.search.reranker.maxChunks.hint.en")
                    )
                ),
                "max_chunks_per_doc" to SliderSettingDescription(
                    dependsOn = listOf(
                        DependencyConfig("search.reranker.type", listOf(RerankerType.manual.name)),
                        DependencyConfig("search.reranker.use_reranker", listOf("true"))
                    ),
                    format = NumberFormat.int,
                    label = LocaleObject(
                        ru = messageService.getMessage("project.settings.search.reranker.maxChunksPerDoc.label.ru"),
                        en = messageService.getMessage("project.settings.search.reranker.maxChunksPerDoc.label.en")
                    ),
                    value = if (currentValue is ManualRerankerConfig) currentValue.maxChunksPerDoc.toDouble() else 3.0,
                    min = 1.0,
                    max = 10.0,
                    step = 1.0,
                    visibleStep = 1.0,
                    hint = LocaleObject(
                        ru = messageService.getMessage("project.settings.search.reranker.maxChunksPerDoc.hint.ru"),
                        en = messageService.getMessage("project.settings.search.reranker.maxChunksPerDoc.hint.en")
                    )
                ),
                "min_score" to SliderSettingDescription(
                    dependsOn = listOf(
                        DependencyConfig(
                            "search.reranker.type",
                            listOf(RerankerType.manual.name, RerankerType.model.name)
                        ),
                        DependencyConfig("search.reranker.use_reranker", listOf("true"))
                    ),
                    format = NumberFormat.double,
                    label = LocaleObject(
                        ru = messageService.getMessage("project.settings.search.reranker.minScore.label.ru"),
                        en = messageService.getMessage("project.settings.search.reranker.minScore.label.en")
                    ),
                    value = currentValue?.minScore?.toDouble() ?: -5.0,
                    min = -5.0,
                    max = 10.0,
                    step = 0.1,
                    visibleStep = 1.0,
                    hint = LocaleObject(
                        ru = messageService.getMessage("project.settings.search.reranker.minScore.hint.ru"),
                        en = messageService.getMessage("project.settings.search.reranker.minScore.hint.en")
                    )
                ),
                "score_reduction_limit" to SliderSettingDescription(
                    dependsOn = listOf(
                        DependencyConfig("search.reranker.type", listOf(RerankerType.manual.name)),
                        DependencyConfig("search.reranker.use_reranker", listOf("true"))
                    ),
                    format = NumberFormat.int,
                    label = LocaleObject(
                        ru = messageService.getMessage("project.settings.search.reranker.scoreReductionLimit.label.ru"),
                        en = messageService.getMessage("project.settings.search.reranker.scoreReductionLimit.label.en")
                    ),
                    value = if (currentValue is ManualRerankerConfig) currentValue.scoreReductionLimit.toDouble() else 20.0,
                    min = 1.0,
                    max = 40.0,
                    step = 1.0,
                    visibleStep = 5.0,
                    hint = LocaleObject(
                        ru = messageService.getMessage("project.settings.search.reranker.scoreReductionLimit.hint.ru"),
                        en = messageService.getMessage("project.settings.search.reranker.scoreReductionLimit.hint.en")
                    )
                ),
                "model" to SelectSettingDescription(
                    dependsOn = listOf(
                        DependencyConfig("search.reranker.type", listOf(RerankerType.model.name)),
                        DependencyConfig("search.reranker.use_reranker", listOf("true"))
                    ),
                    required = true,
                    value = if (currentValue is ModelRerankerConfig) currentValue.model else null,
                    label = LocaleObject(
                        ru = messageService.getMessage("project.settings.llm.model.label.ru"),
                        en = messageService.getMessage("project.settings.llm.model.label.en")
                    ),
                    options = listOf(
                        Option(
                            title = LocaleObject(
                                ru = "BAAI/bge-reranker-v2-m3",
                                en = "BAAI/bge-reranker-v2-m3"
                            ), value = "BAAI/bge-reranker-v2-m3"
                        ),
                    )
                )
            )

        )
    }

    fun asOptions(entries: List<Enum<*>>, messagePrefix: String): List<Option> {
        return entries.map {
            Option(
                title = LocaleObject(
                    ru = messageService.getMessage("${messagePrefix}.${it}.title.ru"),
                    en = messageService.getMessage("${messagePrefix}.${it}.title.en")
                ),
                description = LocaleObject(
                    ru = messageService.getMessage("${messagePrefix}.${it}.description.ru"),
                    en = messageService.getMessage("${messagePrefix}.${it}.description.en")
                ),
                value = it.name
            )
        }
    }
}
