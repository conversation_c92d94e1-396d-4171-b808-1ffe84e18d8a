package com.justai.khub.common.service.e2e

import com.justai.e2echeck.E2EComponentDTO
import com.justai.khub.common.dto.ServiceVersion

abstract class E2EChecker(
    val serviceName: String
) {
    open fun performE2E(requestId: String, loggingEnabledE2ECheck: Boolean): List<E2EComponentDTO> = emptyList()

    open fun getVersion(): List<ServiceVersion> = emptyList()

    fun formatE2ECheckName(): String = "knowledge-hub-gateway -> $serviceName"
}
