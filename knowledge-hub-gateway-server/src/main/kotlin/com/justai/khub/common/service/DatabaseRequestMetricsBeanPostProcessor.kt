package com.justai.khub.common.service

import org.aopalliance.intercept.MethodInterceptor
import org.springframework.aop.framework.ProxyFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.config.BeanPostProcessor
import org.springframework.context.annotation.Lazy
import org.springframework.core.annotation.AnnotationUtils
import org.springframework.stereotype.Component
import org.springframework.stereotype.Repository
import java.util.function.Supplier

@Component
class DatabaseRequestMetricsBeanPostProcessor : BeanPostProcessor {
    // WARNING: It is crucial to use lazy initialization to ensure the metrics system is fully initialized.
    // Without this, some metrics (such as system_cpu_usage) would not be exported.
    @Lazy
    @Autowired
    lateinit var metricsService: MetricsService

    override fun postProcessAfterInitialization(bean: Any, beanName: String): Any? {
        if (AnnotationUtils.findAnnotation(bean::class.java, Repository::class.java) != null) {
            return ProxyFactory(bean).apply {
                addAdvice(MethodInterceptor {
                    val timer = metricsService.postgresRequestTimer(beanName, it.method.name)
                    timer.record(Supplier { it.proceed() })
                })
            }.proxy
        }

        return bean
    }
}
