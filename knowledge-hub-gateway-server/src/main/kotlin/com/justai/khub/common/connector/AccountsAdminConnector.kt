package com.justai.khub.common.connector

import com.justai.khub.common.configuration.dto.IntegrationProperties
import com.justai.khub.common.dto.ServiceVersionResponse
import com.justai.loadbalancer.annotations.HttpInternal
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate

@Service
class AccountsAdminConnector(
    @HttpInternal private val accountsAdminRestTemplate: RestTemplate,
    integrationProperties: IntegrationProperties
) {

    val baseUrl = integrationProperties.aa.baseUrl

    fun healthCheck(): Boolean {
        return try {
            accountsAdminRestTemplate.getForEntity("${baseUrl}/healthCheck", String::class.java).statusCode.is2xxSuccessful
        } catch (e: Exception) {
            false
        }
    }

    fun getVersion(): ServiceVersionResponse {
        return accountsAdminRestTemplate.getForObject("${baseUrl}/version", ServiceVersionResponse::class.java)!!
    }
}
