package com.justai.khub.qa.repository

import com.justai.khub.qa.entity.TestEvaluationEntity
import org.springframework.data.jpa.repository.EntityGraph
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface TestEvaluationRepository : JpaRepository<TestEvaluationEntity, Long> {
    @EntityGraph(attributePaths = ["testRun"])
    @Query("SELECT te FROM TestEvaluationEntity te WHERE te.testRun.testSetRun.id = :testSetRunId")
    fun findByTestSetRunId(testSetRunId: Long): List<TestEvaluationEntity>
}
