package com.justai.khub.chat.service

import com.justai.khub.chat.entity.ChatEntity
import com.justai.khub.chat.entity.ChatHistoryRecordEntity
import com.justai.khub.chat.enumeration.ChatHistoryRecordStatus
import com.justai.khub.chat.mapper.ChatMapper
import com.justai.khub.chat.repository.ChatHistoryRepository
import com.justai.khub.chat.repository.UsedChunksRepository
import com.justai.khub.common.connector.RagServiceConnector
import com.justai.khub.common.dto.AdditionalGenerationInformation
import com.justai.khub.common.dto.rag.QueryResponse
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.project.dto.SearchPipelineConfig
import com.justai.khub.query.dto.UserHistory
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.util.*

@Service
class ChatHistoryService(
    private val chatHistoryRepository: ChatHistoryRepository,
    private val chatService: ChatService,
    private val ragServiceConnector: RagServiceConnector,
    @Value("\${eureka.instance.instance-id}")
    private val instanceId: String,
    private val chatMapper: ChatMapper,
    private val usedChunksRepository: UsedChunksRepository,
    @Value("\${spring.messages.default-locale}") private val defaultLocale: String
) {
    val locale = Locale(defaultLocale)
    val DEFAULT_CHAT_HISTORY_PAGEABLE = PageRequest.of(0, 100, Sort.by(Sort.Direction.DESC, "createdAt"))

    @Transactional(readOnly = true)
    fun getChatHistory(projectId: Long, chatId: Long, accountId: Long, pageable: PageRequest): Page<ChatHistoryRecordEntity> {
        val chat = chatService.getChat(projectId, chatId, accountId)
        return chatHistoryRepository.findAllNotArchivedByChatId(chat.id, pageable)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun clearChatHistory(projectId: Long, chatId: Long, accountId: Long): Page<ChatHistoryRecordEntity> {
        val chat = chatService.getChat(projectId, chatId, accountId)
        clearChatHistoryNoChecks(chat)
        return chatHistoryRepository.findAllNotArchivedByChatId(chat.id, DEFAULT_CHAT_HISTORY_PAGEABLE)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun getDefaultChatHistory(projectId: Long, versionName: String, accountId: Long, pageable: PageRequest): Page<ChatHistoryRecordEntity> {
        val chat = chatService.getOrCreateDefaultChat(projectId, versionName, accountId)
        return chatHistoryRepository.findAllNotArchivedByChatId(chat.id, pageable)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun clearDefaultChatHistory(projectId: Long, versionName: String, accountId: Long): Page<ChatHistoryRecordEntity> {
        val chat = chatService.getOrCreateDefaultChat(projectId, versionName, accountId)
        clearChatHistoryNoChecks(chat)
        return chatHistoryRepository.findAllNotArchivedByChatId(chat.id, DEFAULT_CHAT_HISTORY_PAGEABLE)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun cancelRecordProcessing(projectId: Long, chatId: Long, recordId: Long, accountId: Long): ChatHistoryRecordEntity {
        val chat = chatService.getChat(projectId, chatId, accountId)
        val record = chatHistoryRepository.findByIdAndChatId(recordId, chat.id) ?: throw KhubException(ApiErrorCode.CHAT_HISTORY_RECORD_NOT_FOUND)
        if (!record.status.isFinished()) {
            record.status = ChatHistoryRecordStatus.CANCELED
            chatHistoryRepository.saveAndFlush(record)
        }
        return record
    }

    @Transactional(readOnly = true)
    fun getChatRecord(projectId: Long, chatId: Long, recordId: Long, accountId: Long): ChatHistoryRecordEntity {
        val chat = chatService.getChat(projectId, chatId, accountId)
        return chatHistoryRepository.findByIdAndChatId(recordId, chat.id) ?: throw KhubException(ApiErrorCode.CHAT_HISTORY_RECORD_NOT_FOUND)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun createRecordInDefaultChat(
        projectId: Long,
        versionName: String,
        message: String,
        segment: String?,
        accountId: Long
    ): ChatHistoryRecordEntity {
        val defaultChat = chatService.getOrCreateDefaultChat(projectId, versionName, accountId)
        return createRecordNoChecks(defaultChat, message, segment)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun createQueryRecordInternal(
        projectId: Long,
        versionName: String,
        message: String,
        segment: String?,
        accountId: Long,
        settings: SearchPipelineConfig? = null,
        userHistory: UserHistory? = null,
    ): ChatHistoryRecordEntity {
        val defaultChatInternal = chatService.getOrCreateDefaultChat(projectId, versionName, accountId, internal = true)
        return createRecordNoChecks(
            defaultChatInternal,
            message,
            segment,
            settings = settings,
            userHistory = userHistory
        )
    }

    @Transactional(rollbackFor = [Exception::class])
    fun createRecord(
        projectId: Long,
        chatId: Long,
        message: String,
        segment: String?,
        accountId: Long,
        settings: SearchPipelineConfig? = null
    ): ChatHistoryRecordEntity {
        val chat = chatService.getChat(projectId, chatId, accountId)
        return createRecordNoChecks(chat = chat, message = message, segment = segment, settings = settings)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun finishProcessing(record: ChatHistoryRecordEntity, response: QueryResponse, additionalInfo: AdditionalGenerationInformation?): Boolean {
        val updated = chatHistoryRepository.updateStatusAndResponse(
            record.id,
            ChatHistoryRecordStatus.PROCESSING,
            ChatHistoryRecordStatus.FINISHED,
            response.answer,
            response.promptTokens,
            response.completionTokens,
            additionalInfo
        ) > 0
        if (updated) {
            val usedChunks = chatMapper.initUsedChunksEntity(response.usedChunks).apply { this.chatHistoryRecord = record }
            usedChunksRepository.saveAndFlush(usedChunks)
        }
        return updated
    }

    @Transactional(rollbackFor = [Exception::class])
    fun failProcessing(record: ChatHistoryRecordEntity, response: String): Boolean {
        return chatHistoryRepository.updateStatusAndResponse(record.id, ChatHistoryRecordStatus.PROCESSING, ChatHistoryRecordStatus.FAILED, response) > 0
    }

    @Transactional(rollbackFor = [Exception::class])
    fun startProcessing(record: ChatHistoryRecordEntity): Boolean {
        return chatHistoryRepository.startProcessing(
            record.id,
            ChatHistoryRecordStatus.READY_TO_PROCESS,
            ChatHistoryRecordStatus.PROCESSING,
            instanceId
        ) > 0
    }

    private fun createRecordNoChecks(
        chat: ChatEntity,
        message: String,
        segment: String?,
        settings: SearchPipelineConfig? = null,
        userHistory: UserHistory? = null
    ): ChatHistoryRecordEntity {
        val newRecord = chatMapper.initChatRecordEntity(chat, message, segment, settings, userHistory)
        return chatHistoryRepository.saveAndFlush(newRecord)
    }

    private fun clearChatHistoryNoChecks(chat: ChatEntity) {
        ragServiceConnector.clearHistory("${chat.id}")
        chatHistoryRepository.setArchivedByChatId(chat.id)
        val initialChatRecord = chatMapper.initialChatRecord(chat, locale)
        chatHistoryRepository.saveAndFlush(initialChatRecord)
    }

    @Transactional(readOnly = true)
    fun getNewChatRecords(limit: Int): List<ChatHistoryRecordEntity> {
        return chatHistoryRepository.findAllByStatusAndArchived(ChatHistoryRecordStatus.READY_TO_PROCESS, false, PageRequest.of(0, limit))
    }

    @Transactional(rollbackFor = [Exception::class])
    fun releaseLockedActiveRecords(): Int {
        return chatHistoryRepository.updateByStatusAndLastLockedBy(
            ChatHistoryRecordStatus.PROCESSING,
            ChatHistoryRecordStatus.READY_TO_PROCESS,
            instanceId
        )
    }

    @Transactional(rollbackFor = [Exception::class])
    fun releaseAllTimedOutRecords(lastUpdateBefore: LocalDateTime): Int {
        return chatHistoryRepository.releaseTimedOutRecords(
            ChatHistoryRecordStatus.PROCESSING,
            ChatHistoryRecordStatus.READY_TO_PROCESS,
            lastUpdateBefore
        )
    }
}
