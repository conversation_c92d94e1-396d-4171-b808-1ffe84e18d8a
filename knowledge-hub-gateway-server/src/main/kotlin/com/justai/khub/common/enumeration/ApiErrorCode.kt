package com.justai.khub.common.enumeration

import org.springframework.http.HttpStatus

enum class ApiErrorCode(
    val code: String,
    val message: String,
    val status: HttpStatus,
    val loggingLevel: LoggingLevel = if (status.is5xxServerError) LoggingLevel.ERROR else LoggingLevel.WARN
) {
    INTERNAL_SERVER_ERROR(
        code = "khub.common.common_error",
        message = "Internal system error",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    ),
    CLIENT_ERROR(
        code = "khub.common.common_client_error",
        message = "Client error",
        status = HttpStatus.BAD_REQUEST
    ),
    FILE_DOWNLOAD_ERROR(
        code = "khub.common.file_download_error",
        message = "Failed to download file",
        status = HttpStatus.UNPROCESSABLE_ENTITY
    ),
    INVALID_URL_ERROR(
        code = "khub.common.invalid_url_error",
        message = "Invalid url",
        status = HttpStatus.BAD_REQUEST
    ),
    TOO_LARGE_FILE_ERROR(
        code = "khub.common.too_large_file_error",
        message = "File is too large",
        status = HttpStatus.UNPROCESSABLE_ENTITY
    ),
    AUTHENTICATION_REQUIRED(
        code = "khub.common.auth_required",
        message = "Authentication required",
        status = HttpStatus.UNAUTHORIZED
    ),
    TOO_MANY_REQUESTS(
        code = "khub.common.too_many_requests",
        message = "Too Many Requests",
        status = HttpStatus.TOO_MANY_REQUESTS
    ),
    INVALID_LICENSE_KEY(
        code = "khub.license.invalid_license_key",
        message = "License key is absent or expired",
        status = HttpStatus.PAYMENT_REQUIRED
    ),
    MISSING_REQUIRED_FEATURES(
        code = "khub.common.missing_required_features",
        message = "Missing required features",
        status = HttpStatus.FORBIDDEN
    ),
    NOT_FOUND(
        code = "khub.common.not_found",
        message = "Not Found",
        status = HttpStatus.NOT_FOUND
    ),
    ACCESS_DENIED(
        code = "khub.common.access_denied",
        message = "Access is denied",
        status = HttpStatus.FORBIDDEN
    ),
    INVALID_ACCESS_TOKEN(
        "khub.common.invalid_auth_token",
        "Invalid authentication token",
        HttpStatus.FORBIDDEN
    ),
    ATLASSIAN_ACCESS_NOT_GRANTED(
        "khub.common.atlassian_access_not_granted",
        "Invalid authentication token",
        HttpStatus.INTERNAL_SERVER_ERROR,
    ),
    UNSUPPORTED_MEDIA_TYPE(
        code = "khub.common.unsupported_media_type",
        message = "Unsupported media type \${type}, supported types: \${supported}",
        status = HttpStatus.UNSUPPORTED_MEDIA_TYPE
    ),
    HANDLER_NOT_FOUND(
        code = "khub.common.handler_not_found",
        message = "No handler found for \${method} \${uri}",
        status = HttpStatus.NOT_FOUND
    ),
    MISSING_REQUIRED_PARAMETER(
        code = "khub.common.missing_required_parameter",
        message = "Required \${parameterType} parameter '\${parameterName}' is not present",
        status = HttpStatus.BAD_REQUEST
    ),
    REQUEST_REJECTED_ERROR(
        code = "khub.request.rejected.error",
        message = "\${message}",
        status = HttpStatus.BAD_REQUEST
    ),
    METHOD_ARGUMENT_NOT_VALID(
        code = "khub.request.method_argument_not_valid_exception",
        message = "\${message}",
        status = HttpStatus.BAD_REQUEST,
        loggingLevel = LoggingLevel.WARN
    ),
    MESSAGE_NOT_READABLE(
        code = "khub.request.message_not_readable_exception",
        message = "\${message}",
        status = HttpStatus.BAD_REQUEST,
        loggingLevel = LoggingLevel.WARN
    ),
    MISSING_ACCOUNT_ID(
        code = "khub.common.missing_account_id",
        message = "Can't find account id for current user",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    ),
    PROJECT_NOT_FOUND(
        code = "khub.common.project_not_found",
        message = "Project not found",
        status = HttpStatus.NOT_FOUND
    ),
    API_KEY_NOT_FOUND(
        code = "khub.common.api_key_not_found",
        message = "Api key not found",
        status = HttpStatus.NOT_FOUND
    ),
    EMPTY_PROJECT_NAME(
        code = "khub.common.empty_project_name",
        message = "Empty project name not allowed",
        status = HttpStatus.BAD_REQUEST
    ),
    PROJECT_NAME_TOO_LONG(
        code = "khub.common.project_name_too_long",
        message = "Too long project name",
        status = HttpStatus.BAD_REQUEST
    ),
    PROJECT_NAME_INVALID_FORMAT(
        code = "khub.common.project_name_invalid_format",
        message = "Invalid project name format",
        status = HttpStatus.BAD_REQUEST
    ),
    DUPLICATE_PROJECT_NAME(
        code = "khub.common.duplicate_project_name",
        message = "Project name must be unique",
        status = HttpStatus.BAD_REQUEST
    ),
    DUPLICATE_API_KEY_NAME(
        code = "khub.common.duplicate_api_key_name",
        message = "Api key name must be unique in project",
        status = HttpStatus.BAD_REQUEST
    ),
    PROJECT_FILE_NOT_FOUND(
        code = "khub.common.project_file_not_found",
        message = "Project file not found",
        status = HttpStatus.NOT_FOUND
    ),
    PROJECT_FILE_NOT_INGESTED(
        code = "khub.common.project_file_not_ingested",
        message = "Project file not ingested",
        status = HttpStatus.NOT_FOUND
    ),
    DUPLICATE_PROJECT_FILE(
        code = "khub.common.duplicate_project_file",
        message = "File name must be unique",
        status = HttpStatus.BAD_REQUEST
    ),
    UNSUPPORTED_EXTENSION(
        code = "khub.common.unsupported_extension",
        message = "Unsupported extension",
        status = HttpStatus.BAD_REQUEST
    ),
    TOO_MANY_CSV_FILES(
        code = "khub.common.too_many_csv_files",
        message = "Too many CSV files",
        status = HttpStatus.BAD_REQUEST
    ),
    DELETE_PROCESSING_FILE_ERROR(
        code = "khub.common.delete_processing_file_error",
        message = "Cannot delete file during processing",
        status = HttpStatus.BAD_REQUEST
    ),
    PROJECT_REPOSITORY_NOT_INITIALIZED(
        code = "khub.common.project_repository_not_initialized",
        message = "Project repository not initialized",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    ),
    PROJECT_VERSION_NOT_FOUND(
        code = "khub.common.project_version_not_found",
        message = "Project version not found",
        status = HttpStatus.NOT_FOUND
    ),
    INGESTION_JOB_ALREADY_STARTED(
        code = "khub.common.ingestion_job_already_started",
        message = "Ingestion job has already started",
        status = HttpStatus.BAD_REQUEST
    ),
    INGESTION_JOB_ALREADY_FINISHED(
        code = "khub.common.ingestion_job_already_finished",
        message = "Ingestion job has already finished",
        status = HttpStatus.BAD_REQUEST
    ),
    INGESTION_JOB_NOT_FOUND(
        code = "khub.common.ingestion_job_not_found",
        message = "Ingestion job not found",
        status = HttpStatus.NOT_FOUND
    ),
    CHAT_NOT_FOUND(
        code = "khub.common.chat_not_found",
        message = "Chat not found",
        status = HttpStatus.NOT_FOUND
    ),
    CHANNEL_NOT_FOUND(
        code = "khub.common.channel_not_found",
        message = "Channel not found",
        status = HttpStatus.NOT_FOUND
    ),
    EXTERNAL_CHANNEL_NOT_FOUND(
        code = "khub.common.external_channel_not_found",
        message = "External channel not found",
        status = HttpStatus.NOT_FOUND
    ),
    CHAT_HISTORY_RECORD_NOT_FOUND(
        code = "khub.common.chat_history_record_not_found",
        message = "Chat history record not found",
        status = HttpStatus.NOT_FOUND
    ),
    RAG_INTEGRATION_ERROR(
        code = "khub.common.rag_integration_error",
        message = "RAG service integration error",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    ),
    INGESTER_INTEGRATION_ERROR(
        code = "khub.common.ingester_integration_error",
        message = "Ingester integration error",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    ),
    EVALUATOR_INTEGRATION_ERROR(
        code = "khub.common.evaluator_integration_error",
        message = "Evaluation service integration error",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    ),
    PROMPT_NOT_FOUND(
        code = "khub.common.prompt_not_found",
        message = "Prompt not found",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    ),
    INGEST_ERROR(
        code = "khub.common.ingest_error",
        message = "Can't ingest all files",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    ),
    PARSING_ERROR(
        code = "khub.common.parsing_error",
        message = "Parsing error",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    ),
    MISSED_PARSED_FILE_ERROR(
        code = "khub.common.missed_parsed_file_error",
        message = "Parsed file missed",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    ),
    PARSING_TIMEOUT(
        code = "khub.common.parsing_timeout",
        message = "Parsing timeout",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    ),
    PARSING_TASK_NOT_FOUND(
        code = "khub.common.parsing_task_not_found",
        message = "Parsing task not found",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    ),
    INTEGRATION_TYPE_NOT_SUPPORTED(
        code = "khub.common.integration.type_not_supported",
        message = "Integration type not supported",
        status = HttpStatus.BAD_REQUEST
    ),
    INTEGRATION_NOT_FOUND(
        code = "khub.common.integration_not_found",
        message = "Integration not found",
        status = HttpStatus.NOT_FOUND
    ),
    INTEGRATION_CHECK_ERROR(
        code = "khub.common.integration_check_error",
        message = "Integration check error",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    ),
    INTEGRATION_OAUTH_TIMEOUT(
        code = "khub.common.integration_oauth_timeout",
        message = "Integration OAuth authorization timed out",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    ),
    INTEGRATION_INVALID_CREDENTIALS(
        code = "khub.common.integration_invalid_credentials",
        message = "Invalid credentials",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    ),
    INTEGRATION_CONFLUENCE_SPACE_NOT_FOUND(
        code = "khub.common.integration_confluence_space_not_found",
        message = "Space not found",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    ),
    INTEGRATION_CONFLUENCE_SITE_NOT_FOUND(
        code = "khub.common.integration_confluence_site_not_found",
        message = "Site not found",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    ),
    INTEGRATION_AUTH_ERROR(
        code = "khub.common.integration_auth_error",
        message = "Integration not accessible",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    ),
    INTEGRATION_UNKNOWN_ATTACHMENT_TYPE(
        code = "khub.common.integration_unknown_attachment_type",
        message = "Integration attachment type unknown",
        status = HttpStatus.BAD_REQUEST,
    ),
    ACCOUNT_BALANCE_NOT_FOUND(
        code = "khub.common.account_balance_not_found",
        message = "Can't find balance for account",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    ),
    BILLABLE_OPERATIONS_SUSPENDED(
        code = "khub.common.billable_operations_suspended",
        message = "Billable operations suspended",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    ),
    INSUFFICIENT_ACCOUNT_BALANCE(
        code = "khub.common.insufficient_balance",
        message = "Insufficient account balance",
        status = HttpStatus.BAD_REQUEST
    ),
    INCORRECT_ABBREVIATION_FORMAT(
        code = "khub.common.incorrect_abbreviation_format",
        message = "Incorrect abbreviation format",
        status = HttpStatus.BAD_REQUEST
    ),
    LLM_CHUNKING_LIMIT_EXCEEDED(
        code = "khub.common.llm_chunking_limit_exceeded",
        message = "LLM chunking limit exceeded",
        status = HttpStatus.BAD_REQUEST
    ),
    USER_QUERY_RECORD_NOT_FOUND(
        code = "khub.common.user_query_record_not_found",
        message = "Query record not found",
        status = HttpStatus.NOT_FOUND
    ),

    // error code must be synchronized with all services
    SERVER_SHUTTING_DOWN(
        code = "khub.common.server_shutting_down",
        message = "Server shut down",
        status = HttpStatus.SERVICE_UNAVAILABLE
    ),
    TESTSET_NOT_FOUND(
        code = "khub.common.testset_not_found",
        message = "TestSet not found",
        status = HttpStatus.NOT_FOUND
    ),
    DUPLICATE_TESTSET_NAME(
        code = "khub.common.duplicate_testset_name",
        message = "TestSet name must be unique",
        status = HttpStatus.BAD_REQUEST
    ),
    TESTSET_MISSING_SOURCES(
        code = "khub.common.testset_missing_sources",
        message = "No indexed files found",
        status = HttpStatus.BAD_REQUEST
    ),
    INVALID_TESTSET_FORMAT(
        code = "khub.common.testset_invalid_format",
        message = "Invalid testset format",
        status = HttpStatus.BAD_REQUEST
    ),
    TESTSET_RUN_NOT_FOUND(
        code = "khub.common.testset_run_not_found",
        message = "TestSetRun not found",
        status = HttpStatus.NOT_FOUND
    ),
    TESTSET_RUN_NOT_READY(
        code = "khub.common.testset_run_not_ready",
        message = "TestSetRun not ready",
        status = HttpStatus.BAD_REQUEST
    ),
    TESTSET_NOT_READY(
        code = "khub.common.testset_not_ready",
        message = "TestSet not ready",
        status = HttpStatus.BAD_REQUEST
    ),
    TESTSET_GENERATION_ERROR(
        code = "khub.common.testset_generation_error",
        message = "Failed to generate test set: error = \${error}",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    ),
    TESTSET_GENERATION_CANCELLED(
        code = "khub.common.testset_generation_cancelled",
        message = "TestSet generation cancelled",
        status = HttpStatus.BAD_REQUEST
    ),
    TESTSET_SCHEDULE_INVALID_FORMAT(
        code = "khub.common.testset_schedule_invalid_format",
        message = "Invalid testset shedule format",
        status = HttpStatus.BAD_REQUEST
    ),
    TEST_RUN_ERROR(
        code = "khub.common.test_run_error",
        message = "Test run error",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    ),
    TEST_EVALUATION_ERROR(
        code = "khub.common.test_evaluation_error",
        message = "Test evaluation error",
        status = HttpStatus.INTERNAL_SERVER_ERROR
    );


    companion object {
        const val SOURCE_ERROR_CODE_ARG = "sourceErrorCode"

        fun fromCode(code: String): ApiErrorCode? {
            return ApiErrorCode.entries.firstOrNull { it.code == code }
        }
    }
}

enum class LoggingLevel {
    INFO, DEBUG, WARN, ERROR
}
