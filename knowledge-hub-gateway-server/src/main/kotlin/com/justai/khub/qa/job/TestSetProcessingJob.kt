package com.justai.khub.qa.job

import com.justai.khub.common.configuration.dto.QAProperties
import com.justai.khub.qa.service.TestSetGenerator
import com.justai.khub.qa.service.TestSetRunService
import com.justai.khub.qa.service.TestSetRunner
import com.justai.khub.qa.service.TestSetService
import com.justai.shutdown.CallbackAwaiter
import com.justai.shutdown.GracefulShutDownAware
import com.justai.shutdown.ShutDownLatch
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.util.concurrent.ExecutorService
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit

@ConditionalOnProperty(value = ["scheduling.enabled"], havingValue = "true", matchIfMissing = true)
@Component
class TestSetProcessingJob(
    private val testSetService: TestSetService,
    private val testSetRunService: TestSetRunService,
    private val testSetGenerator: TestSetGenerator,
    private val testSetRunner: TestSetRunner,
    private val testSetRunThreadPool: ThreadPoolExecutor,
    private val testSetRunExecutor: ExecutorService,
    private val testSetGenerationThreadPool: ThreadPoolExecutor,
    private val testSetGenerationExecutor: ExecutorService,
    private val qaProperties: QAProperties
) : GracefulShutDownAware {
    private val shutDownLatch = ShutDownLatch()

    private val log = LoggerFactory.getLogger(this::class.java)

    @Scheduled(fixedDelay = 1000)
    fun scheduleTasks() {
        scheduleTestSetGeneration()
        scheduleTestSetRun()
    }

    private fun scheduleTestSetRun() {
        shutDownLatch.run {
            val availableThreads = qaProperties.testSetRunThreadPoolSize - testSetRunThreadPool.activeCount
            if (availableThreads <= 0) {
                return@run
            }
            val testSetsToProcess = testSetRunService
                .findReadyToRun(availableThreads)
            testSetsToProcess.forEach {
                testSetRunExecutor.submit { testSetRunner.runTestSetInBackground(it) }
            }
        }
    }

    private fun scheduleTestSetGeneration() {
        shutDownLatch.run {
            val availableThreads =
                qaProperties.testSetGenerationThreadPoolSize - testSetGenerationThreadPool.activeCount
            if (availableThreads <= 0) {
                return@run
            }
            val testSetsToProcess = testSetService
                .getReadyToGenerate(availableThreads)
            testSetsToProcess.forEach {
                testSetGenerationExecutor.submit { testSetGenerator.generateTestSetInBackground(it) }
            }
        }
    }

    override fun shutDownGracefully(runnable: Runnable) {
        CallbackAwaiter.withCallback(runnable).awaitAsync {
            log.info("Stop TestSetProcessingJob...")
            shutDownLatch.awaitShutdown()
            testSetRunExecutor.shutdownNow()
            testSetRunExecutor.awaitTermination(20, TimeUnit.SECONDS)
            testSetGenerationExecutor.shutdownNow()
            testSetGenerationExecutor.awaitTermination(20, TimeUnit.SECONDS)
            log.info("Stop TestSetProcessingJob... DONE")
        }
    }
}
