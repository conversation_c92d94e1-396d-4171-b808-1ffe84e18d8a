package com.justai.khub.common.configuration.service

import com.justai.khub.common.configuration.dto.KHubUser
import org.springframework.security.authentication.AuthenticationProvider
import org.springframework.security.core.Authentication
import org.springframework.stereotype.Component

@Component
class KhubAuthenticationProvider : AuthenticationProvider {
    override fun authenticate(authentication: Authentication): Authentication {
        return authentication
    }

    override fun supports(authentication: Class<*>): <PERSON><PERSON><PERSON> {
        return authentication.isAssignableFrom(KHubUser::class.java)
    }
}
