package com.justai.khub.qa.entity

import com.justai.khub.project.entity.ProjectFileEntity
import jakarta.persistence.*
import org.springframework.data.jpa.domain.support.AuditingEntityListener

@Entity
@Table(name = "test")
@EntityListeners(AuditingEntityListener::class)
class TestEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = -1

    @ManyToOne
    @JoinColumn(name = "test_set_id", nullable = false)
    lateinit var testSet: TestSetEntity

    @Column(nullable = false)
    lateinit var question: String

    @Column
    var segment: String? = null

    @Column
    var conversationId: String? = null

    @Column(nullable = false)
    lateinit var groundTruthAnswer: String

    @ManyToOne
    @JoinColumn(name = "ground_truth_file_id")
    var groundTruthFile: ProjectFileEntity? = null
}
