package com.justai.khub.util

import com.justai.khub.api.fe.model.*
import com.justai.khub.chat.enumeration.ChatHistoryRecordStatus
import com.justai.khub.common.util.JSON
import com.justai.khub.project.dto.SearchPipelineConfig
import com.justai.khub.project.dto.SettingsSchema
import com.justai.khub.project.dto.UISettings
import com.justai.khub.project.mapper.ProjectVersionMapper.Companion.DEFAULT_VERSION
import com.justai.khub.util.WebUtils.parseTo
import com.justai.security.session.auth.JustSessionAuthentication
import org.junit.jupiter.api.Assertions.fail
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors
import org.springframework.stereotype.Component
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.ResultActions
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@Component
class ChatHelper {

    @Autowired
    protected lateinit var mvc: MockMvc

    @Autowired
    protected lateinit var defaultAuth: JustSessionAuthentication

    fun createChat(
        projectId: Long,
        versionName: String? = null,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ChatDTO {
        val request = ChatCreateRequest()
        val response = mvc.perform(
            post("/api/khub/projects/${projectId}/chat")
                .param("version", versionName)
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
            .andExpect(status().isOk)
            .andReturn().parseTo<ChatDTO>()
        return response
    }

    fun getChatsPage(
        projectId: Long,
        version: String = DEFAULT_VERSION,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ChatsPage {
        val response = mvc.perform(
            get("/api/khub/projects/${projectId}/chat?version=${version}")
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
            .andExpect(status().isOk)
            .andReturn().parseTo<ChatsPage>()
        return response
    }

    fun getChatHistoryAndExpectOk(
        projectId: Long,
        chatId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ChatHistoryPage {
        return getChatHistory(projectId, chatId, ccAuth, apiKey).andExpect(status().isOk).andReturn().parseTo<ChatHistoryPage>()
    }

    fun getChatHistory(
        projectId: Long,
        chatId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ResultActions {
        return mvc.perform(
            get("/api/khub/projects/${projectId}/chat/${chatId}/history")
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
    }

    fun clearChatHistory(
        projectId: Long,
        chatId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ChatHistoryPage {
        val response = mvc.perform(
            post("/api/khub/projects/${projectId}/chat/${chatId}/history/clear")
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
            .andExpect(status().isOk)
            .andReturn().parseTo<ChatHistoryPage>()
        return response
    }

    fun getDefaultChatHistory(
        projectId: Long,
        version: String = DEFAULT_VERSION,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ChatHistoryPage {
        val response = mvc.perform(
            get("/api/khub/projects/${projectId}/chat/default/history")
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
            .andExpect(status().isOk)
            .andReturn().parseTo<ChatHistoryPage>()
        return response
    }

    fun clearDefaultChatHistory(
        projectId: Long,
        version: String = DEFAULT_VERSION,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ChatHistoryPage {
        val response = mvc.perform(
            post("/api/khub/projects/${projectId}/chat/default/history/clear")
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
            .andExpect(status().isOk)
            .andReturn().parseTo<ChatHistoryPage>()
        return response
    }

    fun getChatRecord(
        projectId: Long,
        chatId: Long,
        recordId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ChatHistoryRecordDTO {
        return mvc.perform(
            get("/api/khub/projects/${projectId}/chat/${chatId}/records/${recordId}")
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
            .andExpect(status().isOk)
            .andReturn().parseTo<ChatHistoryRecordDTO>()
    }

    fun makeRequestAndExpectOk(
        projectId: Long,
        chatId: Long,
        message: String,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ChatHistoryRecordDTO {
        return makeRequest(projectId, chatId, message, ccAuth, apiKey)
            .andExpect(status().isOk)
            .andReturn().parseTo<ChatHistoryRecordDTO>()
    }

    fun makeRequest(
        projectId: Long,
        chatId: Long,
        message: String,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ResultActions {
        val request = UserQuery(message = message)
        return mvc.perform(
            post("/api/khub/projects/${projectId}/chat/${chatId}")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
    }

    fun makeRequestToDefaultChatAndExpectOk(
        projectId: Long,
        message: String,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ChatHistoryRecordDTO {
        return makeRequestToDefaultChat(projectId, message, ccAuth, apiKey)
            .andExpect(status().isOk)
            .andReturn().parseTo<ChatHistoryRecordDTO>()
    }

    fun makeRequestToDefaultChat(
        projectId: Long,
        message: String,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ResultActions {
        val request = UserQuery(message = message)
        return mvc.perform(
            post("/api/khub/projects/${projectId}/chat/default")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
    }


    fun cancelRecordProcessing(
        projectId: Long,
        chatId: Long,
        recordId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ChatHistoryRecordDTO {
        return mvc.perform(
            post("/api/khub/projects/${projectId}/chat/${chatId}/records/${recordId}/cancel")
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
            .andExpect(status().isOk)
            .andReturn().parseTo<ChatHistoryRecordDTO>()
    }

    fun getChatSettings(
        projectId: Long,
        chatId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): Map<String, Any> {
        val response = mvc.perform(
            get("/api/khub/projects/${projectId}/chat/${chatId}/settings")
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
            .andExpect(status().isOk)
            .andReturn().parseTo<Map<String, Any>>()
        return response
    }

    fun getDefaultChatSettings(
        projectId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): SettingsSchema {
        val response = mvc.perform(
            get("/api/khub/projects/${projectId}/chat/default/settings")
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
            .andExpect(status().isOk)
            .andReturn().parseTo<SettingsSchema>()
        return response
    }

    fun setDefaultChatSettings(
        projectId: Long,
        settings: UISettings,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): SettingsSchema {
        val response = mvc.perform(
            post("/api/khub/projects/${projectId}/chat/default/settings")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(settings))
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
            .andExpect(status().isOk)
            .andReturn().parseTo<SettingsSchema>()
        return response
    }

    fun setChatSettings(
        projectId: Long,
        chatId: Long,
        settings: SearchPipelineConfig,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): SettingsSchema {
        val response = mvc.perform(
            post("/api/khub/projects/${projectId}/chat/${chatId}/settings")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(settings))
                .header("Authorization", "Bearer $apiKey")
                .with(SecurityMockMvcRequestPostProcessors.authentication(ccAuth))
        )
            .andExpect(status().isOk)
            .andReturn().parseTo<SettingsSchema>()
        return response
    }

    fun waitProcessing(projectId: Long,
                       record: ChatHistoryRecordDTO,
                       auth: JustSessionAuthentication = defaultAuth): ChatHistoryRecordDTO {
        var updatedRecord = getChatRecord(projectId, record.chatId, record.id, auth)
        var currentIteration = 0
        val maxIterations = 10
        while (currentIteration < maxIterations &&
            (updatedRecord.status == ChatHistoryRecordStatus.READY_TO_PROCESS.name || updatedRecord.status == ChatHistoryRecordStatus.READY_TO_PROCESS.name)
        ) {
            Thread.sleep(1000)
            updatedRecord = getChatRecord(projectId, record.chatId, record.id, auth)
            currentIteration++
        }
        if (updatedRecord.status == ChatHistoryRecordStatus.READY_TO_PROCESS.name || updatedRecord.status == ChatHistoryRecordStatus.READY_TO_PROCESS.name) {
            fail<Unit>("Chat record processing takes too long")
        }
        return updatedRecord
    }
}
