package com.justai.khub.project.validator

import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.util.WebUtils
import com.justai.khub.project.entity.ProjectEntity
import com.justai.khub.project.repository.ProjectRepository
import jakarta.validation.Constraint
import jakarta.validation.ConstraintValidator
import jakarta.validation.ConstraintValidatorContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import kotlin.reflect.KClass

@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
@Constraint(validatedBy = [ProjectValidator::class])
annotation class ValidProject(
    val message: String = "Invalid project",
    val groups: Array<KClass<*>> = [],
    val payload: Array<KClass<out Any>> = []
)

@Component
class ProjectValidator : ConstraintValidator<ValidProject, ProjectEntity> {
    @Autowired
    lateinit var projectsRepository: ProjectRepository

    override fun isValid(project: ProjectEntity, context: ConstraintValidatorContext): Boolean {
        if (checkNameIsBlank(project.name, context)) return false
        if (checkNameIsTooLong(project.name, context)) return false
        if (checkNameIsInvalid(project.name, context)) return false

        val currentAccountId = WebUtils.getCurrentAccountId()
        val existingProject = projectsRepository.findByNameAndCreatedByAndDeletedAtIsNull(project.name, currentAccountId)
        if (existingProject != null && existingProject.id != project.id) {
            context.disableDefaultConstraintViolation()
            context.buildConstraintViolationWithTemplate(ApiErrorCode.DUPLICATE_PROJECT_NAME.code)
                .addPropertyNode("name")
                .addConstraintViolation()
            return false
        }

        return true
    }

    private fun checkNameIsBlank(name: String, context: ConstraintValidatorContext): Boolean {
        if (name.isBlank()) {
            context.buildConstraintViolationWithTemplate(ApiErrorCode.EMPTY_PROJECT_NAME.code)
                .addPropertyNode("name")
                .addConstraintViolation()
            return true
        }
        return false
    }

    private fun checkNameIsTooLong(name: String, context: ConstraintValidatorContext): Boolean {
        if (name.length > MAX_PROJECT_NAME_LENGTH) {
            context.buildConstraintViolationWithTemplate(ApiErrorCode.PROJECT_NAME_TOO_LONG.code)
                .addPropertyNode("name")
                .addConstraintViolation()
            return true
        }
        return false
    }

    private fun checkNameIsInvalid(name: String, context: ConstraintValidatorContext): Boolean {
        if (name.none { it.isLetterOrDigit() }) {
            context.buildConstraintViolationWithTemplate(ApiErrorCode.PROJECT_NAME_INVALID_FORMAT.code)
                .addPropertyNode("name")
                .addConstraintViolation()
            return true
        }
        return false
    }

    companion object {
        const val MAX_PROJECT_NAME_LENGTH = 100 // must be not greater than the constraint in db
    }
}
