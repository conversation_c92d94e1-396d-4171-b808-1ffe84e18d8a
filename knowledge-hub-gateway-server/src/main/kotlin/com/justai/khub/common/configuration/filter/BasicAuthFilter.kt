package com.justai.khub.common.configuration.filter

import com.justai.khub.common.dto.ApiError
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.configuration.service.ApiErrorHandler
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.slf4j.LoggerFactory
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.Authentication
import org.springframework.security.core.authority.SimpleGrantedAuthority
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.web.authentication.www.BasicAuthenticationConverter
import org.springframework.web.filter.OncePerRequestFilter

class BasicAuthFilter(
    private val username: String,
    private val password: String
) : OncePerRequestFilter() {
    private val log = LoggerFactory.getLogger(this::class.java)
    private val authenticationConverter = BasicAuthenticationConverter()

    override fun doFilterInternal(request: HttpServletRequest, response: HttpServletResponse, filterChain: FilterChain) {
        val securityContext = checkNotNull(SecurityContextHolder.getContext())
        val authenticated = authenticateByAuthorizationHeader(request)

        if (authenticated == null) {
            log.warn("Invalid basic credentials for {}", request.requestURI)
            ApiErrorHandler.writeErrorToResponse(
                errors = listOf(ApiError(ApiErrorCode.INVALID_ACCESS_TOKEN)),
                response = response,
                method = request.method,
                requestUrl = request.requestURI
            )
            return
        }

        securityContext.authentication = authenticated
        filterChain.doFilter(request, response)
    }

    private fun authenticateByAuthorizationHeader(request: HttpServletRequest): Authentication? {
        val authentication = try {
            authenticationConverter.convert(request) ?: return null
        } catch (e: Exception) {
            log.warn("An exception occurred while parsing basic auth token", e)
            return null
        }
        if (authentication.principal == username && authentication.credentials == password) {
            return UsernamePasswordAuthenticationToken(
                authentication.principal,
                authentication.credentials,
                listOf(SimpleGrantedAuthority("SERVER_USER"))
            )
        }
        return null
    }

}
