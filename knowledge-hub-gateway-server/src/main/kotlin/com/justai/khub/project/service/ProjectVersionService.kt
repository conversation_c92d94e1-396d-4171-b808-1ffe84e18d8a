package com.justai.khub.project.service

import com.justai.khub.common.connector.IngesterConnector
import com.justai.khub.common.connector.LakefsConnector
import com.justai.khub.common.entity.CCUserEntity
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.project.dto.IngestPipelineConfig
import com.justai.khub.project.dto.SearchPipelineConfig
import com.justai.khub.project.dto.UISettings
import com.justai.khub.project.entity.ProjectEntity
import com.justai.khub.project.entity.ProjectVersionEntity
import com.justai.khub.project.enumeration.ProjectSourceStatus
import com.justai.khub.project.mapper.ProjectSettingsMapper
import com.justai.khub.project.mapper.ProjectVersionMapper
import com.justai.khub.project.mapper.ProjectVersionMapper.Companion.DEFAULT_VERSION
import com.justai.khub.project.repository.ProjectFileRepository
import com.justai.khub.project.repository.ProjectVersionRepository
import org.slf4j.LoggerFactory
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import kotlin.jvm.optionals.getOrNull

@Service
class ProjectVersionService(
    private val projectVersionRepository: ProjectVersionRepository,
    private val lakefsConnector: LakefsConnector,
    private val projectSettingsMapper: ProjectSettingsMapper,
    private val ingesterConnector: IngesterConnector,
    private val projectFileRepository: ProjectFileRepository,
    private val projectVersionMapper: ProjectVersionMapper
) {

    private val log = LoggerFactory.getLogger(this::class.java)

    // Internal use only; no access rights check applied
    @Transactional(readOnly = true)
    fun getDefaultVersion(projectId: Long): ProjectVersionEntity {
        return projectVersionRepository.findByProjectIdAndNameAndDeletedAtIsNull(projectId, DEFAULT_VERSION)
            ?: throw KhubException(ApiErrorCode.PROJECT_VERSION_NOT_FOUND)
    }

    @Transactional(readOnly = true)
    fun getVersion(projectId: Long, versionName: String, accountId: Long): ProjectVersionEntity {
        val version = projectVersionRepository.findByProjectIdAndNameAndDeletedAtIsNull(projectId, versionName)
            ?: throw KhubException(ApiErrorCode.PROJECT_VERSION_NOT_FOUND)
        if (version.project.createdBy.accountId != accountId) throw KhubException(ApiErrorCode.ACCESS_DENIED)
        return version
    }

    @Transactional(readOnly = true)
    fun getVersionById(versionId: Long, accountId: Long): ProjectVersionEntity {
        val version = projectVersionRepository.findById(versionId).getOrNull() ?: throw KhubException(ApiErrorCode.PROJECT_VERSION_NOT_FOUND)
        if (version.project.createdBy.accountId != accountId) throw KhubException(ApiErrorCode.PROJECT_VERSION_NOT_FOUND)
        return version
    }

    @Transactional(readOnly = true)
    fun getVersionsForAccountProjects(versionName: String, accountId: Long, pageable: Pageable): Page<ProjectVersionEntity> {
        return projectVersionRepository.findAllByAccountAndNameNotDeleted(accountId, versionName, pageable)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun getOrCreateVersion(project: ProjectEntity, versionName: String): ProjectVersionEntity {
        return projectVersionRepository.findByProjectIdAndNameAndDeletedAtIsNull(project.id, versionName)
            ?: createVersion(projectVersionMapper.initVersion(project, versionName))
    }

    private fun createVersion(version: ProjectVersionEntity): ProjectVersionEntity {
        val repositoryId = version.project.repositoryId ?: throw KhubException(ApiErrorCode.PROJECT_REPOSITORY_NOT_INITIALIZED)
        lakefsConnector.createBranch(repositoryId, version.name)
        return projectVersionRepository.saveAndFlush(version)
    }

    fun createDefaultVersion(project: ProjectEntity): ProjectVersionEntity {
        val defaultVersion = projectVersionMapper.initVersion(project, DEFAULT_VERSION)
        return projectVersionRepository.saveAndFlush(defaultVersion)
    }

    @Transactional(readOnly = true)
    fun getDefaultVersionIngestSettings(projectId: Long, accountId: Long): IngestPipelineConfig {
        val version = getVersion(projectId, DEFAULT_VERSION, accountId)
        return version.ingestSettings
    }

    @Transactional(readOnly = true)
    fun getDefaultVersionSearchSettings(projectId: Long, accountId: Long): SearchPipelineConfig {
        val version = getVersion(projectId, DEFAULT_VERSION, accountId)
        return version.searchSettings
    }

    @Transactional(rollbackFor = [Exception::class])
    fun setDefaultVersionSettings(projectId: Long, settings: UISettings, accountId: Long): Pair<IngestPipelineConfig, SearchPipelineConfig> {
        val version = getVersion(projectId, DEFAULT_VERSION, accountId)
        val currentIngest = version.ingestSettings
        val prevalidatedSettings = projectSettingsMapper.validateSearchPipelineWithGenerationLLM(settings)
        if (prevalidatedSettings.indexation != null) {
            val settingsWithUpdatedChunker =
                prevalidatedSettings.copy(
                    indexation = prevalidatedSettings.indexation.copy(
                        chunker = prevalidatedSettings.indexation.chunker.copy(
                            type = prevalidatedSettings.indexation.chunkMethod.type
                        )
                    )
                )
            val (updatedIngest, updatedSearch) = projectSettingsMapper.mapUIProjectSettingsToInternalConfig(
                settingsWithUpdatedChunker,
                version.ingestSettings.dao!!.indexName,
                version.searchSettings
            )
            if (currentIngest != updatedIngest) {
                val ingestingFilesCount =
                    projectFileRepository.getFilesCountByVersionIdAndStatuses(version.id, listOf(ProjectSourceStatus.READY_TO_INGEST, ProjectSourceStatus.PROCESSING))
                if (ingestingFilesCount > 0) {
                    throw KhubException(ApiErrorCode.INGESTION_JOB_ALREADY_STARTED)
                }

                log.info("Ingest settings changed - clear search index")
                version.ingestSettings = updatedIngest
                version.settingsUpdatedAt = LocalDateTime.now()
                projectVersionRepository.saveAndFlush(version)
                ingesterConnector.deleteAllFiles(updatedIngest.dao!!.indexName)
                projectFileRepository.updateStatusByVersionId(version.id, ProjectSourceStatus.NOT_INGESTED)
            }
            version.searchSettings = updatedSearch
        } else {
            version.searchSettings = projectSettingsMapper.mapUIRagSettingsToInternalSearchConfig(
                prevalidatedSettings,
                version.ingestSettings,
                version.searchSettings
            )
        }
        version.settingsUpdatedAt = LocalDateTime.now()
        val saved = projectVersionRepository.save(version)
        return saved.ingestSettings to saved.searchSettings
    }


    @Transactional(rollbackFor = [Exception::class])
    fun deleteProjectVersion(projectId: Long, versionName: String, accountId: Long, userId: Long?): Boolean {
        val version = getVersion(projectId, versionName, accountId)
        if (version.createdBy.accountId != accountId) {
            throw KhubException(ApiErrorCode.ACCESS_DENIED)
        }
        version.apply {
            val now = LocalDateTime.now()
            val user = CCUserEntity().also { it.accountId = accountId; it.userId = userId }
            deletedAt = now
            deletedBy = user
            updatedAt = now
            updatedBy = user
        }
        projectVersionRepository.saveAndFlush(version)
        return true
    }

    @Transactional(rollbackFor = [Exception::class])
    fun onSourcesChanged(version: ProjectVersionEntity) {
        version.sourcesUpdatedAt = LocalDateTime.now()
        projectVersionRepository.updateSourcesUpdatedAt(version.id, version.sourcesUpdatedAt!!)
    }
}
