package com.justai.khub.qa.entity

import jakarta.persistence.*
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

@Entity
@Table(name = "test_set_schedule")
class TestSetScheduleEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = -1

    @Column(nullable = false)
    lateinit var schedule: String

    @Column
    var scheduledAt: Long? = null

    @Column
    var runOnSettingsChange: Boolean = false

    @Column
    var runOnSourcesChange: Boolean = false

    @OneToOne
    @JoinColumn(name = "test_set_id", nullable = false)
    lateinit var testSet: TestSetEntity

}

