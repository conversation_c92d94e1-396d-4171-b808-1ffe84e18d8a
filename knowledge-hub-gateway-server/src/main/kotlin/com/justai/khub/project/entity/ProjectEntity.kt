package com.justai.khub.project.entity

import com.justai.khub.common.entity.AuditableEntity
import com.justai.khub.common.entity.CCUserEntity
import com.justai.khub.project.validator.ValidProject
import jakarta.persistence.*
import java.time.LocalDateTime

@Entity
@Table(name = "project")
@ValidProject
class ProjectEntity : AuditableEntity() {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = -1

    @Column(nullable = false)
    lateinit var name: String

    @Column
    var deletedAt: LocalDateTime? = null

    @Column(nullable = true, unique = true)
    var repositoryId: String? = null

    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "accountId", column = Column(name = "deleted_by_account_id")),
        AttributeOverride(name = "userId", column = Column(name = "deleted_by_user_id"))
    )
    var deletedBy: CCUserEntity? = null
}
