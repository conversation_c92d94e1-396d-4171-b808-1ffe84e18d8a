package com.justai.khub.qa.repository

import com.justai.khub.qa.entity.TestEntity
import org.springframework.data.jpa.repository.EntityGraph
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface TestRepository : JpaRepository<TestEntity, Long> {
    @EntityGraph(attributePaths = ["groundTruthFile", "testSet"])
    fun findAllByTestSetId(testSetId: Long): List<TestEntity>
}
