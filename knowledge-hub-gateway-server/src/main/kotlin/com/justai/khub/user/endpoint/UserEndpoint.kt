package com.justai.khub.user.endpoint

import com.justai.khub.api.fe.UserApiService
import com.justai.khub.api.fe.model.UserBalanceDTO
import com.justai.khub.api.fe.model.UserInfoDTO
import com.justai.khub.billing.service.AccountBalanceService
import com.justai.khub.common.configuration.AnalyticsConfiguration
import com.justai.khub.common.configuration.LocalFeaturesConfiguration
import com.justai.khub.common.configuration.dto.IngestProperties
import com.justai.khub.common.util.WebUtils.getCurrentUser
import com.justai.khub.user.mapper.UserMapper.toUserBalanceDTO
import com.justai.khub.user.mapper.UserMapper.toUserInfoDTO
import org.springframework.stereotype.Service

@Service
class UserEndpoint(
    private val accountBalanceService: AccountBalanceService,
    private val localFeaturesConfiguration: LocalFeaturesConfiguration,
    private val ingestProperties: IngestProperties,
    private val analyticsConfiguration: AnalyticsConfiguration
) : UserApiService {

    override fun getMe(): UserInfoDTO {
        val currentUser = getCurrentUser()
        return currentUser.toUserInfoDTO(localFeaturesConfiguration.serialize(), ingestProperties, analyticsConfiguration)
    }

    override fun getCurrentUserBalance(): UserBalanceDTO {
        val currentUser = getCurrentUser()
        val userBalance = accountBalanceService.getBalanceByAccountIds(listOf(currentUser.accountId)).firstOrNull { it.accountId == currentUser.accountId }
        return userBalance.toUserBalanceDTO()
    }
}
