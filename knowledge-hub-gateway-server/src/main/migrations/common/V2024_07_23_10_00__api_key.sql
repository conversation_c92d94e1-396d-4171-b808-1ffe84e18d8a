create table public.api_key
(
    id                    BIGINT GE<PERSON>RATED ALWAYS AS IDENTITY PRIMARY KEY,
    name                  <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    status                VARCHAR(255) NOT NULL,
    prefix                VA<PERSON>HA<PERSON>,
    hash                  VARCHAR,
    owner_login           VARCHAR,

    created_at            TIMESTAMP    NOT NULL,
    expired_at            TIMESTAMP,
    last_used_at          TIMESTAMP,
    created_by_account_id BIGINT       NOT NULL,
    created_by_user_id    BIGINT,
    updated_at            TIMESTAMP    NOT NULL,
    updated_by_account_id BIGINT       NOT NULL,
    updated_by_user_id    BIGINT
);
ALTER SEQUENCE public.api_key_id_seq RESTART WITH ***********;
CREATE UNIQUE INDEX idx_api_key_hash ON public.api_key (hash);
CREATE INDEX idx_api_key_owner ON public.api_key (created_by_account_id);
CREATE UNIQUE INDEX idx_api_key_prefix ON public.api_key (created_by_account_id, prefix) WHERE status = 'ACTIVE';
CREATE UNIQUE INDEX idx_api_key_name ON public.api_key (created_by_account_id, name) WHERE status = 'ACTIVE';
