package com.justai.khub.common.service.e2e

import com.justai.khub.common.dto.ServiceVersion
import org.springframework.stereotype.Component

@Component
class JavaE2EChecker : E2EChecker("") {
    override fun getVersion(): List<ServiceVersion> {
        return listOf(
            ServiceVersion(
                name = "java",
                version = "${System.getProperty("java.vendor")} ${System.getProperty("java.version")}",
                status = ServiceVersion.STATUS_OK
            )
        )
    }
}
