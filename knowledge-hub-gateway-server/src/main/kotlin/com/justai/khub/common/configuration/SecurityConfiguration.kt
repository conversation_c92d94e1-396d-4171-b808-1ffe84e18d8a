package com.justai.khub.common.configuration

import com.justai.khub.common.configuration.filter.BasicAuthFilter
import com.justai.khub.common.configuration.filter.CurrentAuthRewriteFilter
import com.justai.khub.common.configuration.filter.TokenAuthenticationFilter
import com.justai.khub.common.configuration.service.ApiErrorHandler
import com.justai.khub.common.configuration.service.KhubAuthenticationProvider
import com.justai.khub.ratelimit.filter.GlobalRateLimitingFilter
import com.justai.security.servlet.security.servlet.filter.JustSessionAuthenticationFilterFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.Ordered
import org.springframework.core.annotation.Order
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.authentication.AnonymousAuthenticationFilter
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter

@Configuration
class SecurityConfiguration(
    private val khubAuthenticationProvider: KhubAuthenticationProvider,
    private val tokenAuthenticationFilter: TokenAuthenticationFilter,
) {

    @Value("\${just-session.required-features}")
    private var requiredFeaturesRaw: String? = null

    @Value("\${just-session.required-permissions}")
    private var requiredPermissionsRaw: String? = null

    @Order(Ordered.HIGHEST_PRECEDENCE)
    @Bean
    fun publicResourcesSecurityFilterChain(http: HttpSecurity): SecurityFilterChain {
        return http
            .securityMatcher(
                "/version",
                "/actuator",
                "/actuator/**",
                "/healthCheck",
                "/static/specs/**",
                "/static/templates/**",
                "/api/khub/e2e-check",
                "/api/khub/versions",
            )
            .disableStandardBasicFilters()
            .enableExceptionHandling()
            .authorizeHttpRequests { it.anyRequest().permitAll() }
            .build()
    }

    @Order(Ordered.HIGHEST_PRECEDENCE + 2)
    @Bean
    fun basicAuthSecurityFilterChain(
        http: HttpSecurity,
        @Value("\${server.username}") username: String,
        @Value("\${server.password}") password: String
    ): SecurityFilterChain {
        return http
            .securityMatcher("$INTERNAL_URL_PREFIX/internal/**")
            .disableStandardBasicFilters()
            .enableExceptionHandling()
            .authorizeHttpRequests { it.anyRequest().authenticated() }
            .addFilterBefore(BasicAuthFilter(username, password), AnonymousAuthenticationFilter::class.java)
            .build()
    }

    @Order(Ordered.HIGHEST_PRECEDENCE + 3)
    @Bean
    fun ccSecurityFilterChain(
        http: HttpSecurity,
        globalRateLimitingFilter: GlobalRateLimitingFilter,
        justSessionAuthenticationFilterFactory: JustSessionAuthenticationFilterFactory
    ): SecurityFilterChain {
        return http
            .securityMatcher("$INTERNAL_URL_PREFIX/api-keys/**")
            .disableStandardBasicFilters()
            .enableExceptionHandling()
            .authenticationProvider(khubAuthenticationProvider)
            .authorizeHttpRequests { it.anyRequest().authenticated() }
            .addFilterAt(justSessionAuthenticationFilterFactory.instance, BasicAuthenticationFilter::class.java)
            .addFilterAfter(CurrentAuthRewriteFilter(requiredFeaturesRaw, requiredPermissionsRaw), BasicAuthenticationFilter::class.java)
            .addFilterAfter(globalRateLimitingFilter, CurrentAuthRewriteFilter::class.java)
            .build()
    }

    @Order(Ordered.HIGHEST_PRECEDENCE + 4)
    @Bean
    fun ccAndTokenSecurityFilterChain(
        http: HttpSecurity,
        globalRateLimitingFilter: GlobalRateLimitingFilter,
        justSessionAuthenticationFilterFactory: JustSessionAuthenticationFilterFactory
    ): SecurityFilterChain {
        return http
            .securityMatcher("$INTERNAL_URL_PREFIX/**", "$PUBLIC_URL_PREFIX/**")
            .disableStandardBasicFilters()
            .enableExceptionHandling()
            .authenticationProvider(khubAuthenticationProvider)
            .authorizeHttpRequests {
                it
                    .requestMatchers("$INTERNAL_URL_PREFIX/public/**").permitAll()
                    .anyRequest().authenticated()
            }
            .addFilterAt(justSessionAuthenticationFilterFactory.instance, BasicAuthenticationFilter::class.java)
            .addFilterAfter(CurrentAuthRewriteFilter(requiredFeaturesRaw, requiredPermissionsRaw), BasicAuthenticationFilter::class.java)
            .addFilterAfter(globalRateLimitingFilter, CurrentAuthRewriteFilter::class.java)
            .addFilterAfter(tokenAuthenticationFilter, BasicAuthenticationFilter::class.java)
            .build()
    }

    fun HttpSecurity.disableStandardBasicFilters(): HttpSecurity {
        return this
            .httpBasic { it.disable() }
            .csrf { it.disable() }
            .formLogin { it.disable() }
            .logout { it.disable() }
    }

    fun HttpSecurity.enableExceptionHandling(): HttpSecurity {
        return this
            .exceptionHandling {
                it.authenticationEntryPoint { request, response, authException ->
                    ApiErrorHandler.writeErrorToResponse(
                        throwable = authException,
                        response = response,
                        requestUrl = request.requestURI,
                        method = request.method
                    )
                }
            }
    }

    companion object {
        const val INTERNAL_URL_PREFIX = "/api/khub" // expected to be used only by our fe client
        const val PUBLIC_URL_PREFIX = "/api/knowledge-hub"
    }
}
