package com.justai.khub.util

import com.justai.khub.api.fe.model.IntegrationDTO
import com.justai.khub.api.fe.model.IntegrationFull
import com.justai.khub.api.fe.model.IntegrationShort
import com.justai.khub.api.fe.model.IntegrationsPage
import com.justai.khub.common.util.JSON
import com.justai.khub.integration.dto.ConfluenceIntegrationSettings
import com.justai.khub.integration.dto.IntegrationSettings
import com.justai.khub.integration.enumeration.IntegrationStatus
import com.justai.khub.util.WebUtils.parseTo
import com.justai.security.session.auth.JustSessionAuthentication
import org.apache.commons.lang3.RandomStringUtils
import org.junit.jupiter.api.Assertions
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.authentication
import org.springframework.stereotype.Component
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.ResultActions
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import org.springframework.util.LinkedMultiValueMap

@Component
class IntegrationHelper {

    @Autowired
    protected lateinit var mvc: MockMvc

    @Autowired
    protected lateinit var defaultAuth: JustSessionAuthentication

    fun getIntegrationsPage(
        projectId: Long,
        params: Map<String, String> = mapOf(),
        namePart: String? = null,
        statusFilter: List<IntegrationStatus>? = null,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): IntegrationsPage {
        val response = mvc.perform(
            get("/api/khub/projects/${projectId}/integrations")
                .header("Authorization", "Bearer $apiKey")
                .queryParams(LinkedMultiValueMap(params.mapValues { mutableListOf(it.value) }.toMutableMap()))
                .with(authentication(ccAuth))
        )
            .andExpect(status().isOk)
            .andReturn().parseTo<IntegrationsPage>()
        return response
    }


    fun deleteIntegrationAndExpectOk(
        projectId: Long,
        integrationId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ) {
        deleteIntegration(projectId, integrationId, ccAuth, apiKey).andExpect(status().isOk).andReturn()
    }

    fun deleteIntegration(
        projectId: Long,
        integrationId: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ResultActions {
        return mvc.perform(
            delete("/api/khub/projects/${projectId}/integrations/${integrationId}")
                .header("Authorization", "Bearer $apiKey")
                .with(authentication(ccAuth))
        )
    }

    fun createIntegration(
        projectId: Long,
        name: String = "integration-name-${RandomStringUtils.randomAlphabetic(8)}",
        settings: IntegrationSettings = ConfluenceIntegrationSettings(
            baseUrl = "localhost",
            space = "admin",
            token = "admin",
        ),
        autoIngest: Boolean = true,
        autoSync: Boolean = true,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): IntegrationShort {
        val response = mvc.perform(
            post("/api/khub/projects/${projectId}/integrations")
                .header("Authorization", "Bearer $apiKey")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(TestDataFactory.integrationDTO(
                    name,
                    settings,
                    autoIngest,
                    autoSync
                )))
                .with(authentication(ccAuth))
        )
            .andExpect(status().isOk)
            .andReturn().parseTo<IntegrationShort>()

        Assertions.assertNotNull(response.audit.createdAt)
        Assertions.assertNotNull(response.audit.createdBy)

        Assertions.assertNotNull(response.audit.updatedAt)
        Assertions.assertNotNull(response.audit.updatedBy)
        return response
    }

    fun getIntegrationAndExpectOk(
        projectId: Long,
        id: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): IntegrationFull {
        return getIntegration(projectId, id, ccAuth, apiKey).andExpect(status().isOk).andReturn().parseTo<IntegrationFull>()
    }

    fun getIntegration(
        projectId: Long,
        id: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ResultActions {
        return mvc.perform(
            get("/api/khub/projects/${projectId}/integrations/${id}")
                .header("Authorization", "Bearer $apiKey")
                .with(authentication(ccAuth))
        )
    }

    fun syncIntegration(
        projectId: Long,
        id: Long,
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): IntegrationShort {
        val response = mvc.perform(
            get("/api/khub/projects/${projectId}/integrations/${id}/sync")
                .header("Authorization", "Bearer $apiKey")
                .with(authentication(ccAuth))
        )
            .andExpect(status().isOk)
            .andReturn().parseTo<IntegrationShort>()
        return response
    }

    fun updateIntegrationAndExpectOk(
        projectId: Long,
        id: Long,
        updateRequest: IntegrationDTO = TestDataFactory.integrationDTO("integration-name-${RandomStringUtils.randomAlphabetic(8)}"),
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): IntegrationFull {
        return updateIntegration(projectId, id, updateRequest, ccAuth, apiKey).andExpect(status().isOk).andReturn().parseTo<IntegrationFull>()
    }

    fun updateIntegration(
        projectId: Long,
        id: Long,
        updateRequest: IntegrationDTO = TestDataFactory.integrationDTO("integration-name-${RandomStringUtils.randomAlphabetic(8)}"),
        ccAuth: JustSessionAuthentication? = defaultAuth,
        apiKey: String? = ""
    ): ResultActions {
        return mvc.perform(
            put("/api/khub/projects/${projectId}/integrations/${id}")
                .header("Authorization", "Bearer $apiKey")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(updateRequest))
                .with(authentication(ccAuth))
        )
    }

}
