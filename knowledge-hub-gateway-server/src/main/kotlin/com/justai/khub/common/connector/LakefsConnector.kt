package com.justai.khub.common.connector


import com.justai.khub.common.configuration.dto.IntegrationProperties
import com.justai.khub.common.dto.WithIntegrationTimer
import com.justai.khub.common.enumeration.IntegrationName
import com.justai.khub.common.util.CommonUtils
import com.justai.khub.project.mapper.ProjectVersionMapper
import com.justai.loadbalancer.annotations.HttpExternal
import io.lakefs.clients.sdk.*
import io.lakefs.clients.sdk.model.BranchCreation
import io.lakefs.clients.sdk.model.PathList
import io.lakefs.clients.sdk.model.RepositoryCreation
import org.slf4j.LoggerFactory
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service
import org.springframework.web.client.RestClientException
import org.springframework.web.client.RestTemplate
import java.net.URI
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.StandardCopyOption
import kotlin.Pair
import kotlin.io.path.fileSize


@Service
@WithIntegrationTimer(integrationName = IntegrationName.lakefs)
class LakefsConnector(
    private val lakeFSClient: ApiClient,
    private val integrationProperties: IntegrationProperties
) {

    private val log = LoggerFactory.getLogger(this::class.java)

    fun createRepository(repositoryName: String): String {
        val api = RepositoriesApi(lakeFSClient)
        val createRepositoryRequest = RepositoryCreation()
            .name(repositoryName)
            .storageNamespace(integrationProperties.lakefs.storageNamespace.replace("{repository_name}", repositoryName))
            .defaultBranch(ProjectVersionMapper.DEFAULT_VERSION)
        val createRepositoryResponse = api.createRepository(createRepositoryRequest).execute()
        return createRepositoryResponse.id
    }

    fun deleteRepository(repositoryId: String) {
        val api = RepositoriesApi(lakeFSClient)
        api.deleteRepository(repositoryId).execute()
    }

    fun createBranch(repositoryId: String, branchName: String) {
        val api = BranchesApi(lakeFSClient)
        val request = BranchCreation()
            .name(branchName)
        api.createBranch(repositoryId, request).execute()
    }

    fun addFile(repositoryId: String, branchName: String, path: String, file: Path) {
        val api = ObjectsApi(lakeFSClient)
        api.uploadObject(repositoryId, branchName, path)
            .content(file.toFile())
            .force(true)
            .execute()
    }

    fun deleteFile(repositoryId: String, branchName: String, path: String) {
        val api = ObjectsApi(lakeFSClient)
        api.deleteObject(repositoryId, branchName, path).execute()
        deleteListObjects(repositoryId, branchName, ".khub/${path}/")
    }

    fun getFileContent(repositoryId: String, branchName: String, path: String): ByteArray {
        val api = ObjectsApi(lakeFSClient)
        val response = api.getObject(repositoryId, branchName, path).execute()
        val content = Files.readAllBytes(response.toPath())
        Files.delete(response.toPath())
        return content
    }

    fun getAttachmentContent(repositoryId: String, branchName: String, sourceFilePath: String, attachmentId: String): ByteArray {
        val api = ObjectsApi(lakeFSClient)
        val path = ".khub/${sourceFilePath}/parsed/${attachmentId}"
        val response = api.getObject(repositoryId, branchName, path).execute()
        val content = Files.readAllBytes(response.toPath())
        Files.delete(response.toPath())
        return content
    }

    fun getParsedContent(repositoryId: String, branchName: String, path: String): Pair<String, ByteArray?> {
        val api = ObjectsApi(lakeFSClient)
        val parsedFiles = getParsedFiles(repositoryId, branchName, path)
        if (parsedFiles.isEmpty()) {
            return "" to null
        }
        if (parsedFiles.size == 1) {
            val response = api.getObject(repositoryId, branchName, parsedFiles.first()).execute()
            return parsedFiles.first().substringAfterLast("/") to Files.readAllBytes(response.toPath()).also {
                Files.delete(response.toPath())
            }
        }
        val filesContentByName = parsedFiles.map {
            val response = api.getObject(repositoryId, branchName, it).execute()
            it.substringAfterLast("/") to Files.readAllBytes(response.toPath()).also {
                Files.delete(response.toPath())
            }
        }.toMap()

        val zipArchive = CommonUtils.createZipArchive(filesContentByName)
        return "${path.substringBeforeLast(".")}.zip" to zipArchive
    }

    private fun getParsedFiles(repositoryId: String, branchName: String, path: String): List<String> {
        val parsedFiles = mutableListOf<String>()
        val api = ObjectsApi(lakeFSClient)
        val parsedFilesRequest = api.listObjects(repositoryId, branchName).prefix(".khub/${path}/parsed/")
        var after = ""
        do {
            val resp = parsedFilesRequest.after(after).execute()
            parsedFiles.addAll(resp.results.map { it.path })
            after = resp.results.lastOrNull()?.path ?: ""
        } while (resp.pagination.hasMore)
        return parsedFiles.filterNot {
            it.substringAfterLast("/").startsWith(".")
        }
    }

    private fun deleteListObjects(repositoryId: String, branchName: String, prefix: String) {
        val api = ObjectsApi(lakeFSClient)
        val fileTempDataRequest = api.listObjects(repositoryId, branchName).prefix(prefix)
        var after = ""
        do {
            val resp = fileTempDataRequest.after(after).execute()
            val deleteRequest = PathList().apply { this.paths = resp.results.map { it.path } }
            api.deleteObjects(repositoryId, branchName, deleteRequest).execute()
            after = resp.results.lastOrNull()?.path ?: ""
        } while (resp.pagination.hasMore)
    }

    fun ensureAlive(): Boolean {
        val api = HealthCheckApi(lakeFSClient)
        api.healthCheck().execute()
        return true
    }

    fun getVersion(): String? {
        val api = ConfigApi(lakeFSClient)
        return api.config.execute().versionConfig?.version
    }
}
