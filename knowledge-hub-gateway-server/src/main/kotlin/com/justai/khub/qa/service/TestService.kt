package com.justai.khub.qa.service

import com.justai.khub.project.entity.ProjectFileEntity
import com.justai.khub.common.dto.rag.TestGenerateResponse
import com.justai.khub.qa.entity.TestEntity
import com.justai.khub.qa.entity.TestSetEntity
import com.justai.khub.qa.mapper.QAMapper
import com.justai.khub.qa.repository.TestRepository
import com.justai.khub.qa.repository.TestSetRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class TestService(
    private val testRepository: TestRepository,
    private val qaMapper: QAMapper,
    private val testSetRepository: TestSetRepository,
) {

    @Transactional(readOnly = true)
    fun findAllByTestSetId(testSetId: Long): List<TestEntity> {
        return testRepository.findAllByTestSetId(testSetId).sortedBy { it.id }
    }

    @Transactional(rollbackFor = [Exception::class])
    fun createTests(tests: List<TestEntity>): List<TestEntity> {
        return testRepository.saveAllAndFlush(tests)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun createGeneratedTestsAndUpdateTestSet(
        testSet: TestSetEntity,
        file: ProjectFileEntity,
        generationResponse: TestGenerateResponse
    ) {
        val newEntities = generationResponse.tests
            .map { qaMapper.initTest(testSet, file, it.question, it.groundTruthAnswer) }
        testRepository.saveAllAndFlush(newEntities)
        testSetRepository.incrementTestsCountAndUsage(
            testSet.id,
            generationResponse.tests.size,
            generationResponse.usage?.promptTokens ?: 0,
            generationResponse.usage?.completionTokens ?: 0
        )
    }
}
