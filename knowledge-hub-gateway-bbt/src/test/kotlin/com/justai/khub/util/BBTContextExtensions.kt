package com.justai.khub.util

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.ArrayNode
import com.fasterxml.jackson.databind.node.ObjectNode
import com.justai.khub.api.model.*
import com.justai.khub.util.TestDataGenerator.DEFAULT_VERSION
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue

object BBTContextExtensions {
    val INGEST_TIMEOUT_SECONDS = 30
    val CHAT_RECORD_PROCESSING_TIMEOUT_SECONDS = 30
    val TESTSET_GENERATING_TIMEOUT_SECONDS = 30
    val TESTSET_RUN_TIMEOUT_SECONDS = 180
    val DEFAULT_TEST_SET = GenerateTestSetRequest().apply {
        name = "generated test set"
        testsPerDoc = 10
    }

    fun BBTContext.createProjectWithFile(
        fileName: String = "test.md",
        segment: String? = null
    ): ProjectDTO {
        val project = projectsApi.createProject(TestDataGenerator.testProject())
        val testFile = TestDataGenerator.testFile(fileName)
        projectFilesApi.addFile(
            project.id,
            DEFAULT_VERSION,
            segment?.let { JSON.stringify(mapOf("segment" to segment)) },
            testFile
        )
        return project
    }

    fun BBTContext.addFile(
        projectId: Long,
        fileName: String = "test.md",
        segment: String? = null
    ) {
        val testFile = TestDataGenerator.testFile(fileName)
        projectFilesApi.addFile(
            projectId,
            DEFAULT_VERSION,
            segment?.let { JSON.stringify(mapOf("segment" to segment)) },
            testFile
        )
    }

    fun BBTContext.retrieveChunks(
        projectId: Long,
        message: String,
    ): List<ChunkWithScoreDTO> {
        val request = RetrieveChunksRequest().query(message)
        return retrieveApi.retrieveChunks(projectId, request, null).chunks
    }

    fun BBTContext.sendMessage(
        projectId: Long,
        message: String,
        segment: String? = null
    ): ChatHistoryRecordDTO {
        val query = UserQuery()
            .message(message)
            .segment(segment)
        return chatApi.makeRequestToDefaultChat(projectId, query, null)
    }

    fun BBTContext.sendMessageAndExpectSuccess(
        projectId: Long,
        message: String,
        segment: String? = null
    ): ChatHistoryRecordDTO {
        val record = sendMessage(projectId, message, segment)
        return waitRecordProcessingSuccessfullyFinished(projectId, record)
    }

    fun BBTContext.sendMessageAndExpectError(
        projectId: Long,
        message: String,
        segment: String? = null
    ): ChatHistoryRecordDTO {
        val record = sendMessage(projectId, message, segment)
        val finishedRecord = waitRecordProcessingFinished(projectId, record)
        assertEquals("FAILED", finishedRecord.status)
        return finishedRecord
    }

    fun BBTContext.waitIngestFinished(projectId: Long): ProjectDTO {
        Thread.sleep(10_000)
        val startedAt = System.currentTimeMillis()
        var currentState: ProjectDTO
        do {
            currentState = projectsApi.getProject(projectId)
            if (currentState.status != "INGESTING_DOCUMENTS") {
                return currentState
            }
            Thread.sleep(5000)
        } while (System.currentTimeMillis() - startedAt < INGEST_TIMEOUT_SECONDS * 1000)
        // TODO restore delete request
//        ingestApi.cancelIngest(projectId, IngestionJobCancelRequest())
        throw AssertionError("Ingest project ${projectId} failed to finish in $INGEST_TIMEOUT_SECONDS seconds. Cancelling it. Last known state is: ${currentState.status}")
    }

    fun BBTContext.ingestSync(projectId: Long): ProjectDTO {
        ingestApi.ingest(projectId, DEFAULT_VERSION)
        return waitIngestFinished(projectId)
    }

    fun BBTContext.waitIngestSuccessfullyFinished(projectId: Long): ProjectDTO {
        val finishedProject = waitIngestFinished(projectId)
        assertEquals("FINISHED", finishedProject.status, "Ingest project ${finishedProject.id} failed with status: ${finishedProject.status}")
        val files = projectFilesApi.getFiles(finishedProject.id, 10, null, 0, null, null, null, null)
        assertTrue(files.content.size >= 1)
        for (file in files.content) {
            assertEquals(
                "INGESTED",
                file.status,
                "Failed to ingest file '${file.name}'(id = ${file.id}). Status = ${file.status}"
            )
        }
        return finishedProject
    }

    fun BBTContext.waitRecordProcessingFinished(projectId: Long, record: ChatHistoryRecordDTO): ChatHistoryRecordDTO {
        Thread.sleep(5_000)
        val startedAt = System.currentTimeMillis()
        var currentState: ChatHistoryRecordDTO
        do {
            currentState = chatApi.getChatRecord(projectId, record.chatId, record.id)
            if (currentState.status == "FINISHED" || currentState.status == "FAILED" || currentState.status == "CANCELED") {
                return currentState
            }
            Thread.sleep(5000)
        } while (System.currentTimeMillis() - startedAt < CHAT_RECORD_PROCESSING_TIMEOUT_SECONDS * 1000)
        throw AssertionError("Chat record ${record.id} failed to finish in $CHAT_RECORD_PROCESSING_TIMEOUT_SECONDS seconds. Last known state is: ${currentState.status}")
    }

    fun BBTContext.waitRecordProcessingSuccessfullyFinished(
        projectId: Long,
        record: ChatHistoryRecordDTO
    ): ChatHistoryRecordDTO {
        val finishedRecord = waitRecordProcessingFinished(projectId, record)
        assertEquals(
            "FINISHED",
            finishedRecord.status,
            "Chat record ${record.id} with status: ${finishedRecord.status}"
        )
        return finishedRecord
    }

    fun BBTContext.getFiles(projectId: Long): List<SourceDTO> {
        return projectFilesApi
            .getFiles(projectId, 1000, DEFAULT_VERSION, 0, null, null, null, null)
            .content
    }

    fun BBTContext.getSettings(projectId: Long): MutableMap<String, MutableMap<String, MutableMap<String, JsonNode?>>> {
        val settingsWithSchema = projectsApi.getDefaultVersionSettings(projectId)
        val settingsWithSchemaJson = JSON.toObject(settingsWithSchema)
        val result = mutableMapOf<String, MutableMap<String, MutableMap<String, JsonNode?>>>()
        for (partitionRaw in settingsWithSchemaJson["partitions"] as ArrayNode) {
            val partition = partitionRaw as ObjectNode
            result[partition["name"].asText()] = mapSections(partition["sections"] as ObjectNode)
        }
        return result
    }

    private fun mapSections(partitionsRaw: ObjectNode): MutableMap<String, MutableMap<String, JsonNode?>> {
        return mutableMapOf<String, MutableMap<String, JsonNode?>>().also { result ->
            partitionsRaw.fields().forEach { entry ->
                result[entry.key] = (entry.value as ObjectNode).let {
                    mapSettings(it["settings"] as ObjectNode)
                }
            }
        }
    }

    private fun mapSettings(settingsRaw: ObjectNode): MutableMap<String, JsonNode?> {
        return mutableMapOf<String, JsonNode?>().also { result ->
            settingsRaw.fields().forEach { entry ->
                result[entry.key] = (entry.value as ObjectNode)["value"]
            }
        }
    }

    fun BBTContext.generateTestSetSync(
        projectId: Long,
        request: GenerateTestSetRequest = DEFAULT_TEST_SET
    ): TestSetDTO {
        val newTestSet = qaApi.generateTestSet(projectId, request)
        return waitTestSetGenerationFinished(projectId, newTestSet)
    }

    fun BBTContext.waitTestSetGenerationFinished(projectId: Long, testSet: TestSetDTO): TestSetDTO {
        Thread.sleep(5_000)
        val startedAt = System.currentTimeMillis()
        var currentState: TestSetDTO
        do {
            currentState = qaApi.getTestSet(projectId, testSet.id)
            if (currentState.status == TestSetDTO.StatusEnum.ACTIVE || currentState.status == TestSetDTO.StatusEnum.FAILED) {
                return currentState
            }
            Thread.sleep(5000)
        } while (System.currentTimeMillis() - startedAt < TESTSET_GENERATING_TIMEOUT_SECONDS * 1000)
        throw AssertionError("Failed to generate test set ${testSet.id} in $TESTSET_GENERATING_TIMEOUT_SECONDS seconds. Last known state is: ${currentState.status}")
    }

    fun BBTContext.runTestSetSync(
        projectId: Long,
        testSetId: Long,
        autoEvaluate: Boolean = false
    ): TestSetRunDTO {
        val newTestSetRun = qaApi.runTestSet(projectId, testSetId, TestSetRunRequest().autoEvaluate(autoEvaluate))
        return waitTestSetRunFinished(projectId, newTestSetRun)
    }

    fun BBTContext.waitTestSetRunFinished(projectId: Long, testSetRun: TestSetRunDTO): TestSetRunDTO {
        Thread.sleep(5_000)
        val startedAt = System.currentTimeMillis()
        var currentState: TestSetRunDTO
        do {
            currentState = qaApi.getTestSetRun(projectId, testSetRun.id)
            if (currentState.status == TestSetRunDTO.StatusEnum.FINISHED || currentState.status == TestSetRunDTO.StatusEnum.FAILED) {
                return currentState
            }
            Thread.sleep(5000)
        } while (System.currentTimeMillis() - startedAt < TESTSET_RUN_TIMEOUT_SECONDS * 1000)
        throw AssertionError("Failed to finish test set run ${testSetRun.id} in $TESTSET_RUN_TIMEOUT_SECONDS seconds. Last known state is: ${currentState.status}")
    }
}
