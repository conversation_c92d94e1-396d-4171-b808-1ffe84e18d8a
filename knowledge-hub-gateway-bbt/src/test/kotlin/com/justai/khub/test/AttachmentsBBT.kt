package com.justai.khub.test

import com.justai.khub.base.BaseBBT
import com.justai.khub.util.BBTContextExtensions.createProjectWithFile
import com.justai.khub.util.BBTContextExtensions.ingestSync
import com.justai.khub.util.BBTContextExtensions.retrieveChunks
import com.justai.khub.util.BBTContextExtensions.sendMessageAndExpectSuccess
import com.justai.khub.util.TestDataGenerator
import org.junit.jupiter.api.Assertions.assertArrayEquals
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.Test
import org.springframework.core.io.ByteArrayResource

class AttachmentsBBT : BaseBBT() {
    val ATTACHMENT_LINK_REGEXP = """https?://[a-zA-Z0-9.-]+(:[0-9]{1,5})?/api/khub/public/attachments/([a-zA-Z0-9_-]+)/([a-zA-Z0-9_-]+\.[a-zA-Z0-9]+)""".toRegex()

    @Test
    fun testIngest() = withDefaultAuth {
        val project = createProjectWithFile(fileName = "pdf-with-images.pdf")
        val updatedProject = ingestSync(project.id)
        assertEquals("ACTIVE", updatedProject.status)
        val firstResponseWithFirstImage = sendMessageAndExpectSuccess(project.id, "Как выглядит ольха? Ответ пришли со ссылкой, если есть").response!!
        val firstAttachmentWithFirstLink = ATTACHMENT_LINK_REGEXP.findAll(firstResponseWithFirstImage).map { it.groupValues[2] to it.groupValues[3] }.toList()
        assertEquals(1, firstAttachmentWithFirstLink.size, "Unexpected number of attachments")
        val secondResponseWithFirstImage = sendMessageAndExpectSuccess(project.id, "Как выглядит ольха? Ответ пришли со ссылкой.").response!!
        val firstAttachmentWithSecondLink = ATTACHMENT_LINK_REGEXP.findAll(secondResponseWithFirstImage).map { it.groupValues[2] to it.groupValues[3] }.toList()
        assertEquals(1, firstAttachmentWithSecondLink.size, "Unexpected number of attachments")
        assertEquals(firstAttachmentWithFirstLink[0].first, firstAttachmentWithSecondLink[0].first, "Attachment ids must be equal")
        assertNotEquals(firstAttachmentWithFirstLink[0].second, firstAttachmentWithSecondLink[0].second, "Attachment links must be different")

        val firstAttachmentContent = attachmentsApi.downloadFileAttachment(firstAttachmentWithFirstLink[0].first, firstAttachmentWithFirstLink[0].second) as ByteArrayResource
        assertArrayEquals (firstAttachmentContent.byteArray, TestDataGenerator.testFile("attachment-1.png").file.readBytes(), "Unexpected attachment")



        val responseWithSecondImage = sendMessageAndExpectSuccess(project.id, "Как выглядит тополь? Ответ пришли со ссылкой.").response!!
        val secondAttachmentWithLink = ATTACHMENT_LINK_REGEXP.findAll(responseWithSecondImage).map { it.groupValues[2] to it.groupValues[3] }.toList()
        assertEquals(1, secondAttachmentWithLink.size, "Unexpected number of attachments")
        val secondAttachmentContent = attachmentsApi.downloadFileAttachment(secondAttachmentWithLink[0].first, secondAttachmentWithLink[0].second) as ByteArrayResource
        assertArrayEquals (secondAttachmentContent.byteArray, TestDataGenerator.testFile("attachment-2.png").file.readBytes(), "Unexpected attachment")

        val chunkAttachments = retrieveChunks(project.id,"Как выглядит ольха?")
            .map { ATTACHMENT_LINK_REGEXP.findAll(it.content).map { it.groupValues[2] to it.groupValues[3] }.toList() }
            .filter { it.isNotEmpty() }
        assertFalse(chunkAttachments.isEmpty())
    }
}
