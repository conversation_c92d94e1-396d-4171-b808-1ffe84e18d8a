package com.justai.khub.common.configuration.dto

import org.springframework.boot.context.properties.ConfigurationProperties

data class VectorizerProperties(
    val options: List<PropertiesOption>
)

data class ParsingProperties(
    val parsingTimeoutSeconds: Int
)

data class ImageProcessorProperties(
    val model: String,
    val enabled: Boolean
)

data class ChunkingProperties(
    val llmChunkerEnabled: Boolean,
    val llmChunkerTotalCharsLimit: Int
)

@ConfigurationProperties(prefix = "ingest", ignoreUnknownFields = true)
data class IngestProperties(
    val imageProcessor: ImageProcessorProperties,
    val vectorizer: VectorizerProperties,
    val parser: ParsingProperties,
    val chunker: ChunkingProperties,
    val fileProcessingThreadPoolSize: Int,
    val ingestJobThreadPoolSize: Int,
    val supportedExtensions: Set<String>
)
