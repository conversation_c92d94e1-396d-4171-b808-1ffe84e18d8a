package com.justai.khub.util

import org.springframework.http.MediaType
import org.springframework.test.web.servlet.request.RequestPostProcessor
import org.springframework.test.web.servlet.setup.ConfigurableMockMvcBuilder
import org.springframework.test.web.servlet.setup.MockMvcConfigurer
import org.springframework.web.context.WebApplicationContext

class JsonDefaultContentTypeMockMvcConfigurer : MockMvcConfigurer {
    override fun beforeMockMvcCreated(
        builder: ConfigurableMockMvcBuilder<*>,
        context: WebApplicationContext
    ): RequestPostProcessor {
        return RequestPostProcessor {
            if (it.contentType == null) {
                it.contentType = MediaType.APPLICATION_JSON_VALUE
                it.characterEncoding = Charsets.UTF_8.name()
            }
            it
        }
    }
}
