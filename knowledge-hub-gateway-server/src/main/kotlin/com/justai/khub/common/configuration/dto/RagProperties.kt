package com.justai.khub.common.configuration.dto

import com.justai.khub.project.dto.ImageProcessingStrategy
import com.justai.khub.project.dto.RerankerType
import org.springframework.boot.context.properties.ConfigurationProperties

data class ModelProperties(
    val title: String,
    val value: String,
    val qaEnabled: Boolean = false,
    val indexationEnabled: Boolean = false,
    val pipelineTypes: List<String> = listOf()
)

data class LLMProperties(
    val models: List<ModelProperties>
)

data class RerankerProperties(
    val value: String,
    val enabled: <PERSON>olean
)

@ConfigurationProperties(prefix = "rag", ignoreUnknownFields = true)
data class RagProperties(
    val reranker: Map<RerankerType, RerankerProperties>,
    val llm: LLMProperties,
    val chatQueryProcessingJobThreadPoolSize: Int,
    val imageSearchStrategy: ImageProcessingStrategy
) {
    fun getModelTitle(model: String?): String? {
        if (model.isNullOrBlank()) return null
        return llm.models.firstOrNull { it.value == model }?.title
    }
}
