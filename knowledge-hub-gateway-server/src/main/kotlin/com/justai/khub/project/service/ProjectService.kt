package com.justai.khub.project.service

import com.justai.khub.channel.service.ChannelService
import com.justai.khub.chat.service.ChatService
import com.justai.khub.common.connector.LakefsConnector
import com.justai.khub.common.entity.CCUserEntity
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.common.util.StringUtils
import com.justai.khub.common.util.WebUtils.getCurrentUser
import com.justai.khub.integration.service.IntegrationService
import com.justai.khub.project.entity.ProjectEntity
import com.justai.khub.project.entity.ProjectVersionEntity
import com.justai.khub.project.mapper.ProjectVersionMapper.Companion.DEFAULT_VERSION
import com.justai.khub.project.repository.ProjectRepository
import com.justai.khub.qa.repository.TestSetScheduleRepository
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import java.time.LocalDateTime

@Service
@Validated
class ProjectService(
    private val projectRepository: ProjectRepository,
    private val chatService: ChatService,
    private val projectVersionService: ProjectVersionService,
    private val integrationService: IntegrationService,
    private val lakefsConnector: LakefsConnector,
    private val channelService: ChannelService,
    private val testSetScheduleRepository: TestSetScheduleRepository,
) {

    @Transactional(readOnly = true)
    fun getProject(projectId: Long, accountId: Long): ProjectEntity {
        val project = projectRepository.findByIdAndDeletedAtIsNull(projectId) ?: throw KhubException(ApiErrorCode.PROJECT_NOT_FOUND)
        if (project.createdBy.accountId != accountId) throw KhubException(ApiErrorCode.ACCESS_DENIED)
        return project
    }

    @Transactional(rollbackFor = [Exception::class])
    fun updateProject(@Valid updatedProject: ProjectEntity, accountId: Long): ProjectEntity {
        val currentProject = getProject(updatedProject.id, accountId)
        return if (currentProject.name != updatedProject.name) {
            currentProject.name = updatedProject.name
            projectRepository.saveAndFlush(currentProject)
        } else {
            currentProject
        }
    }


    @Transactional(rollbackFor = [Exception::class])
    fun createProject(@Valid newProject: ProjectEntity, accountId: Long): Pair<ProjectEntity, ProjectVersionEntity> {
        val createdProject = projectRepository.saveAndFlush(newProject)
        // important to do it after saving project to be able to use project id
        createdProject.repositoryId = StringUtils.generateRepositoryId(createdProject, accountId)
        newProject.repositoryId = lakefsConnector.createRepository(createdProject.repositoryId!!)

        projectRepository.saveAndFlush(createdProject)
        val version = projectVersionService.createDefaultVersion(createdProject)
        chatService.getOrCreateDefaultChat(createdProject.id, version.name, accountId)

        return Pair(createdProject, version)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun deleteProject(projectId: Long, accountId: Long, userId: Long?): Boolean {
        val project = projectRepository.findByIdAndDeletedAtIsNull(projectId) ?: throw KhubException(ApiErrorCode.ACCESS_DENIED)
        if (project.createdBy.accountId != accountId) {
            throw KhubException(ApiErrorCode.ACCESS_DENIED)
        }

        project.apply {
            deletedAt = LocalDateTime.now()
            deletedBy = CCUserEntity().also { it.accountId = accountId; it.userId = userId }
        }

        projectRepository.saveAndFlush(project)
        channelService.deleteAvailableChannels(
            projectId = projectId,
            versionName = DEFAULT_VERSION,
            currentUser = getCurrentUser()
        )
        projectVersionService.deleteProjectVersion(
            projectId = projectId,
            versionName = DEFAULT_VERSION,
            accountId = accountId,
            userId = userId
        )
        integrationService.disableIntegrationsByProject(project)
        testSetScheduleRepository.deleteByProjectId(projectId)

        return true
    }
}
