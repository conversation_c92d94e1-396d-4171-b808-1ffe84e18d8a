package com.justai.khub.project.endpoint

import com.justai.khub.api.fe.ProjectsApiService
import com.justai.khub.api.fe.model.*
import com.justai.khub.chat.service.ChatService
import com.justai.khub.common.util.JSON
import com.justai.khub.common.util.WebUtils.getCurrentAccountId
import com.justai.khub.common.util.WebUtils.getCurrentUser
import com.justai.khub.project.mapper.ProjectMapper.toDTO
import com.justai.khub.project.mapper.ProjectMapper.toEntity
import com.justai.khub.project.mapper.ProjectSettingsMapper
import com.justai.khub.project.mapper.ProjectVersionMapper.Companion.DEFAULT_VERSION
import com.justai.khub.project.service.ProjectFileService
import com.justai.khub.project.service.ProjectService
import com.justai.khub.project.service.ProjectVersionService
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service

@Service
class ProjectEndpoint(
    private val projectService: ProjectService,
    private val projectVersionService: ProjectVersionService,
    private val projectSettingsMapper: ProjectSettingsMapper,
    private val projectFileService: ProjectFileService,
    private val chatService: ChatService
) : ProjectsApiService {

    override fun createProject(projectCreateRequest: ProjectCreateRequest): ProjectDTO {
        val currentAccountId = getCurrentAccountId()
        val newProject = projectCreateRequest.toEntity()
        val (createdProject, createdVersion) = projectService.createProject(newProject, currentAccountId)
        val fileStatuses = projectFileService.getFilesSummaryByStatus(listOf(createdVersion.id))
        return createdProject.toDTO(fileStatuses)
    }

    override fun getProject(projectId: Long): ProjectDTO {
        val currentAccountId = getCurrentAccountId()
        val project = projectService.getProject(projectId, currentAccountId)
        val version = projectVersionService.getVersion(projectId, DEFAULT_VERSION, currentAccountId)
        val fileStatuses = projectFileService.getFilesSummaryByStatus(listOf(version.id))
        return project.toDTO(fileStatuses)
    }

    override fun updateProject(projectId: Long, projectUpdateRequest: ProjectUpdateRequest): ProjectDTO {
        val currentAccountId = getCurrentAccountId()
        val updatedProject = projectUpdateRequest.toEntity(projectId)
        val project = projectService.updateProject(updatedProject, currentAccountId)
        val version = projectVersionService.getVersion(projectId, DEFAULT_VERSION, currentAccountId)
        val fileStatuses = projectFileService.getFilesSummaryByStatus(listOf(version.id))
        return project.toDTO(fileStatuses)
    }

    override fun deleteProject(projectId: Long) {
        val currentUser = getCurrentUser()
        projectService.deleteProject(projectId, currentUser.accountId, currentUser.userId)
    }

    override fun getProjects(pageSize: Int, pageNum: Int): ProjectsPage {
        val pageable = PageRequest.of(pageNum, pageSize, Sort.by(Sort.Direction.DESC, "project.createdAt"))
        val currentAccountId = getCurrentAccountId()
        val projectVersions = projectVersionService.getVersionsForAccountProjects(DEFAULT_VERSION, currentAccountId, pageable)
        val fileStatusesByVersionId = projectFileService.getFilesSummaryByStatus(projectVersions.content.map { it.id }).groupBy { it.versionId }
        return ProjectsPage(
            content = projectVersions.content.map { it.project.toDTO(fileStatusesByVersionId[it.id] ?: listOf()) },
            paging = PagingResponse(
                totalCount = projectVersions.totalElements,
                pageNum = projectVersions.number,
                pageSize = projectVersions.size
            )
        )
    }

    override fun setDefaultVersionSettings(projectId: Long, requestBody: Map<String, Any>): Map<String, Any> {
        val currentUser = getCurrentUser()
        val (ingestion, search) = projectVersionService.setDefaultVersionSettings(projectId, JSON.parse(JSON.toNode(requestBody)), currentUser.accountId)
        chatService.setSettingsForAllVersionChats(projectId, DEFAULT_VERSION, search, currentUser.accountId)
        return projectSettingsMapper.toDTO(ingestion, search, currentUser)
    }

    override fun getDefaultVersionSettings(projectId: Long): Map<String, Any> {
        val currentUser = getCurrentUser()
        val ingestSettings = projectVersionService.getDefaultVersionIngestSettings(projectId, currentUser.accountId)
        val searchSettings = projectVersionService.getDefaultVersionSearchSettings(projectId, currentUser.accountId)
        return projectSettingsMapper.toDTO(ingestSettings, searchSettings, currentUser)
    }
}
