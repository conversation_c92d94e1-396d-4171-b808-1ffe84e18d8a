package com.justai.khub.query.endpoint

import com.justai.khub.api.public.QueriesPublicApiService
import com.justai.khub.api.public.model.GenerateAnswerRequest
import com.justai.khub.api.public.model.QueryProcessingResult
import com.justai.khub.api.public.model.RetrieveChunksRequest
import com.justai.khub.api.public.model.RetrievedChunks
import com.justai.khub.chat.entity.ChatHistoryRecordEntity
import com.justai.khub.chat.service.ChatHistoryService
import com.justai.khub.chat.service.ChatService
import com.justai.khub.common.mapper.CommonMapper.toRetrievedChunks
import com.justai.khub.common.mapper.SearchConfigMapper
import com.justai.khub.common.util.CommonUtils.waitRecordProcessingFinish
import com.justai.khub.common.util.WebUtils.withUserAndProject
import com.justai.khub.project.mapper.ProjectVersionMapper.Companion.DEFAULT_VERSION
import com.justai.khub.project.service.ProjectVersionService
import com.justai.khub.project.service.RetrieveChunksService
import com.justai.khub.query.mapper.QueryGenerationMapper.toQueryProcessingResult
import com.justai.khub.query.mapper.QueryGenerationMapper.toUserHistory
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import kotlin.time.Duration.Companion.seconds

@Service
class QueriesPublicEndpoint(
    private val projectVersionService: ProjectVersionService,
    private val chatHistoryService: ChatHistoryService,
    private val chatHistory: ChatService,
    private val retrieveChunksService: RetrieveChunksService,
    private val searchConfigMapper: SearchConfigMapper
) : QueriesPublicApiService {
    private val log = LoggerFactory.getLogger(this::class.java)

    override fun generateQueryAnswer(generateAnswerRequest: GenerateAnswerRequest): QueryProcessingResult = withUserAndProject { currentUser ->
        val queryResult = startProcessingQuery(generateAnswerRequest, currentUser.projectId!!, currentUser.accountId)
        val generationResult = runBlocking {
            chatHistoryService.waitRecordProcessingFinish(
                currentUser.projectId,
                queryResult.chat.id,
                queryResult.id,
                currentUser.accountId,
                logger = log
            )
        }
        generationResult.toQueryProcessingResult()
    }

    override fun generateQueryAnswerAsync(generateAnswerRequest: GenerateAnswerRequest): QueryProcessingResult = withUserAndProject { currentUser ->
        val queryResult = startProcessingQuery(generateAnswerRequest, currentUser.projectId!!, currentUser.accountId)
        queryResult.toQueryProcessingResult()
    }

    override fun cancelQueryProcessing(queryId: Long): QueryProcessingResult = withUserAndProject { currentUser ->
        val internalQueryChat = chatHistory.getOrCreateDefaultChat(
            projectId = currentUser.projectId!!,
            versionName = DEFAULT_VERSION,
            accountId = currentUser.accountId,
            internal = true
        )
        val canceled = chatHistoryService.cancelRecordProcessing(currentUser.projectId, internalQueryChat.id, queryId, currentUser.accountId)
        canceled.toQueryProcessingResult()
    }

    override fun getQueryAnswer(queryId: Long, waitTimeSeconds: Int): QueryProcessingResult =
        withUserAndProject { currentUser ->
            val internalQueryChat = chatHistory.getOrCreateDefaultChat(
                projectId = currentUser.projectId!!,
                versionName = DEFAULT_VERSION,
                accountId = currentUser.accountId,
                internal = true
            )
            val record = chatHistoryService.getChatRecord(
                currentUser.projectId,
                internalQueryChat.id,
                queryId,
                currentUser.accountId
            )
            val generationResult = runBlocking {
                chatHistoryService.waitRecordProcessingFinish(
                    currentUser.projectId,
                    record.chat.id,
                    record.id,
                    currentUser.accountId,
                    logger = log,
                    timeout = waitTimeSeconds.seconds
                )
            }
            generationResult.toQueryProcessingResult()
        }

    override fun retrieveChunks(retrieveChunksRequest: RetrieveChunksRequest): RetrievedChunks = withUserAndProject { currentUser ->
        val version = projectVersionService.getVersion(currentUser.projectId!!, DEFAULT_VERSION, currentUser.accountId)
        val defaultSearchSettings = version.searchSettings
        val history = retrieveChunksRequest.history?.toUserHistory()
        val searchSettings = searchConfigMapper.run {
            retrieveChunksRequest.settings?.toSearchSettings(defaultSearchSettings, history != null) ?: defaultSearchSettings
        }
        val usedChunks = retrieveChunksService.retrieveChunks(
            query = retrieveChunksRequest.query,
            version = version,
            customSearchSettings = searchSettings,
            history = retrieveChunksRequest.history?.toUserHistory(),
            createPublicLinksForSources = true
        )
        usedChunks.toRetrievedChunks()
    }

    private fun startProcessingQuery(generateAnswerRequest: GenerateAnswerRequest, projectId: Long, accountId: Long): ChatHistoryRecordEntity {
        val defaultSearchSettings = projectVersionService.getDefaultVersionSearchSettings(projectId, accountId)
        val segment = generateAnswerRequest.settings?.search?.segment
        val history = generateAnswerRequest.history?.toUserHistory()
        val searchPipelineConfig = searchConfigMapper.run {
            generateAnswerRequest.settings?.toSearchSettings(defaultSearchSettings, history != null) ?: defaultSearchSettings
        }
        val userQuery = chatHistoryService.createQueryRecordInternal(
            projectId = projectId,
            versionName = DEFAULT_VERSION,
            message = generateAnswerRequest.query,
            segment = segment,
            accountId = accountId,
            settings = searchPipelineConfig,
            userHistory = history
        )
        return userQuery
    }
}
