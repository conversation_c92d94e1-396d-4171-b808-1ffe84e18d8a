package com.justai.khub.chat.service

import com.justai.khub.attachment.service.AttachmentService
import com.justai.khub.billing.enumeration.BillableOperationType
import com.justai.khub.billing.service.BillingService
import com.justai.khub.chat.entity.ChatHistoryRecordEntity
import com.justai.khub.common.connector.RagServiceConnector
import com.justai.khub.common.dto.AdditionalGenerationInformation
import com.justai.khub.common.dto.rag.QueryResponse
import com.justai.khub.common.dto.RelevantSource
import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.service.MetricsService
import com.justai.khub.common.util.CommonUtils.extractErrorCode
import com.justai.khub.common.util.JSON.toObjectNode
import com.justai.khub.project.service.ProjectFileService
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.Duration
import java.time.LocalDateTime
import java.util.*

@Service
class ChatQueryProcessor(
    private val ragServiceConnector: RagServiceConnector,
    private val chatHistoryService: ChatHistoryService,
    private val billingService: BillingService,
    private val metricsService: MetricsService,
    private val projectFileService: ProjectFileService,
    private val attachmentService: AttachmentService
) {
    private val log = LoggerFactory.getLogger(this::class.java)

    fun process(record: ChatHistoryRecordEntity) {
        val updated = chatHistoryService.startProcessing(record)
        if (!updated) {
            log.info("Skip processing chat query: {}: already processed", record.id)
            return
        }
        log.info("Started processing chat query: {}", record.id)
        metricsService.chatQueryWaitingTimeTimer().record(Duration.between(record.createdAt, LocalDateTime.now()))
        metricsService.processChatQueryTimer().record<Runnable> {
            try {
                val response = doProcess(record)
                val additionalInfo = response.relevantSources?.takeIf { it.isNotEmpty() }?.let {
                    val projectFiles = projectFileService.getFilesByStoragePaths(record.chat.version, it)
                    val filePublicLinks = projectFiles.filter { it.metadata.externalLink == null }.takeIf { it.isNotEmpty() }?.let { filesWithoutExternalLink ->
                        attachmentService.createAttachmentLinksForFiles(filesWithoutExternalLink)
                    }
                    AdditionalGenerationInformation(relevantSources = projectFiles.map {
                        RelevantSource(
                            sourceId = it.id,
                            path = it.relativePath,
                            externalLink = it.metadata.externalLink ?: filePublicLinks?.get(it.id)
                        )
                    })
                }
                chatHistoryService.finishProcessing(record, response, additionalInfo)
                log.info("Finished processing chat query: {}", record.id)
            } catch (ex: Exception) {
                val errorCode = ex.extractErrorCode(ApiErrorCode.RAG_INTEGRATION_ERROR.code)
                log.error("Error processing chat record {}", record.id, ex)
                metricsService.incrementProcessChatQueryErrorsCounter()
                chatHistoryService.failProcessing(record, errorCode)
            }
            null
        }
    }

    private fun doProcess(record: ChatHistoryRecordEntity): QueryResponse {
        val metadata = mapOf(
            "type" to BillableOperationType.ragQuery.name,
            "projectId" to record.chat.version.project.id,
            "chatId" to record.chat.id,
        ).toObjectNode()
        return billingService.billSuccessfulCall(record.chat.createdBy.accountId, 1, metadata) {
            val searchSettings = record.searchSettings ?: record.chat.searchSettings
            val ingestSettings = record.chat.version.ingestSettings
            val (history, conversationId) = record.chat.takeIf { it.internal }
                ?.let { record.userHistory to UUID.randomUUID().toString() }
                ?: (null to "${record.chat.id}")
            ragServiceConnector.query(
                message = record.request,
                segment = record.segment,
                conversationId = conversationId,
                searchPipelineConfig = searchSettings,
                ingestSettings = ingestSettings,
                history = history
            )
        }
    }

}
