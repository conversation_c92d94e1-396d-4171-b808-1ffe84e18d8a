package com.justai.khub.util

import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationContextInitializer
import org.springframework.context.ConfigurableApplicationContext
import org.springframework.test.context.support.TestPropertySourceUtils
import org.testcontainers.containers.PostgreSQLContainer

class DockerPostgresDataSourceInitializer : ApplicationContextInitializer<ConfigurableApplicationContext?> {

    class PgContainer(imageName: String) : PostgreSQLContainer<PgContainer>(imageName)

    companion object {
        private val log = LoggerFactory.getLogger(this::class.java)
        private val postgreContainer = PgContainer("postgres:13")
            .withDatabaseName("kh_gateway")
            .withUsername("postgres")
            .withPassword("postgres")

        init {
            postgreContainer.start()
        }
    }

    override fun initialize(applicationContext: ConfigurableApplicationContext) {
        log.info("Initializing PostgresDataSourceInitializer for context {}", applicationContext)
        TestPropertySourceUtils.addInlinedPropertiesToEnvironment(
            applicationContext,
            "spring.datasource.url=" + postgreContainer.jdbcUrl,
            "spring.datasource.username=" + postgreContainer.username,
            "spring.datasource.password=" + postgreContainer.password,
            "spring.datasource.driver-class-name=" + postgreContainer.driverClassName
        )
    }
}
