package com.justai.khub.qa.service

import com.justai.khub.common.enumeration.ApiErrorCode
import com.justai.khub.common.exception.KhubException
import com.justai.khub.qa.entity.TestEntity
import org.apache.poi.ss.usermodel.*
import org.springframework.stereotype.Service
import java.io.ByteArrayInputStream

@Service
class TestSetParser {

    fun parse(testsRaw: ByteArray): List<TestEntity> {
        val sheet = try {
            val workbook = WorkbookFactory.create(ByteArrayInputStream(testsRaw))
            workbook.getSheetAt(0)
        } catch (ex: Exception) {
            throw KhubException(ApiErrorCode.INVALID_TESTSET_FORMAT, ex)
        }
        validateHeaders(sheet)
        val headersRow = sheet.getRow(0)
        val hasSegmentColumn = hasColumnWithName(headersRow, 2, SEGMENT_HEADER)
        val hasConversationIdColumn = hasColumnWithName(headersRow, 3, CONVERSTION_ID_HEADER)

        return sheet.drop(1).mapNotNull { row ->
            try {
                createTestEntity(row, hasSegmentColumn, hasConversationIdColumn)
            } catch (ex: Exception) {
                throw KhubException(ApiErrorCode.INVALID_TESTSET_FORMAT, ex)
            }
        }
    }

    private fun hasColumnWithName(row: Row?, index: Int, expectedName: String): Boolean {
        return row?.getCell(index)?.stringCellValue?.trim()?.lowercase() == expectedName
    }

    private fun validateHeaders(sheet: Sheet) {
        val headerRow = sheet.getRow(0) ?: throw KhubException(ApiErrorCode.INVALID_TESTSET_FORMAT)
        val actualHeaders = REQUIRED_HEADERS.indices.mapNotNull { headerRow.getCell(it)?.stringCellValue?.trim()?.lowercase() }
        if (actualHeaders != REQUIRED_HEADERS) {
            throw KhubException(ApiErrorCode.INVALID_TESTSET_FORMAT, mapOf("error" to "invalid required headers"))
        }
    }

    private fun createTestEntity(
        row: Row,
        hasSegmentColumn: Boolean,
        hasConversationIdColumn: Boolean
    ): TestEntity? {
        val questionCell = getCellValueAsString(row.getCell(0))?.trim() ?: return null
        val answerCell = getCellValueAsString(row.getCell(1))?.trim() ?: return null

        return TestEntity().apply {
            question = questionCell
            groundTruthAnswer = answerCell
            // optional rows
            segment = if (hasSegmentColumn) getCellValueAsString(row.getCell(2))?.trim() else null
            conversationId = if (hasConversationIdColumn) getCellValueAsString(row.getCell(3))?.trim() else null
        }
    }

    private fun getCellValueAsString(cell: Cell?): String? {
        if (cell == null) return null
        return when (cell.cellType) {
            CellType.STRING -> cell.stringCellValue
            CellType.NUMERIC -> {
                if (DateUtil.isCellDateFormatted(cell)) {
                    cell.localDateTimeCellValue.toString()
                } else {
                    // Remove trailing .0 from numbers
                    cell.numericCellValue.toBigDecimal().stripTrailingZeros().toPlainString()
                }
            }

            CellType.BOOLEAN -> cell.booleanCellValue.toString()
            CellType.FORMULA -> {
                when (cell.cachedFormulaResultType) {
                    CellType.STRING -> cell.stringCellValue
                    CellType.NUMERIC -> cell.numericCellValue.toString()
                    CellType.BOOLEAN -> cell.booleanCellValue.toString()
                    else -> null
                }
            }

            else -> null
        }
    }


    companion object {
        private const val QUESTION_HEADER = "question"
        private const val GROUND_TRUTH_ANSWER_HEADER = "ground truth answer"
        private const val SEGMENT_HEADER = "segment"
        private const val CONVERSTION_ID_HEADER = "conversation id"

        private val REQUIRED_HEADERS = listOf(QUESTION_HEADER, GROUND_TRUTH_ANSWER_HEADER)
    }
}
