package com.justai.khub.ingest.job

import com.justai.khub.common.configuration.dto.IngestProperties
import com.justai.khub.common.configuration.dto.KHubUser
import com.justai.khub.common.util.LoggingUtils
import com.justai.khub.ingest.service.IngestProcessor
import com.justai.khub.ingest.service.IngestService
import com.justai.khub.project.entity.ProjectFileEntity
import com.justai.khub.project.service.ProjectFileService
import com.justai.shutdown.CallbackAwaiter
import com.justai.shutdown.GracefulShutDownAware
import com.justai.shutdown.ShutDownLatch
import jakarta.annotation.PostConstruct
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Component
import java.util.*
import java.util.concurrent.ExecutorService
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit

@ConditionalOnProperty(value = ["scheduling.ingest.enabled"], havingValue = "true", matchIfMissing = true)
@Component
class IngestProcessingJob(
    private val ingestService: IngestService,
    private val ingestProcessor: IngestProcessor,
    private val projectFileService: ProjectFileService,
    private val ingestProperties: IngestProperties,
    private val ingestProcessingThreadPool: ThreadPoolExecutor,
    private val ingestProcessingExecutor: ExecutorService,
) : GracefulShutDownAware {
    private val log = LoggerFactory.getLogger(this::class.java)

    private val shutDownLatch = ShutDownLatch()

    @PostConstruct
    fun init() {
        releaseActiveJobsAndFiles()
    }

    fun releaseActiveJobsAndFiles() {
        val releasedFiles = projectFileService.releaseLockedActiveFiles()
        if (releasedFiles > 0) {
            log.warn("Released {} active files", releasedFiles)
        }
    }

    @Scheduled(fixedDelay = 1000)
    fun scheduleIngestionJobs() {
        shutDownLatch.run {
            val availableThreads = ingestProperties.ingestJobThreadPoolSize - ingestProcessingThreadPool.activeCount
            if (availableThreads <= 0) {
                return@run
            }
            val filesToProcess = ingestService
                .getReadyToIngest(availableThreads)
            filesToProcess.forEach {
                ingestProcessingExecutor.submit { processIngestionJob(it) }
            }
        }
    }

    // WARN! Updates MDC and SecurityContext for background thread execution
    private fun processIngestionJob(projectFile: ProjectFileEntity) {
        try {
            SecurityContextHolder.getContext().authentication = KHubUser(accountId = projectFile.createdBy.accountId)
            LoggingUtils.addToMDC(accountId = projectFile.createdBy.accountId, requestId = UUID.randomUUID().toString())
            ingestProcessor.ingestFile(projectFile)
        } finally {
            SecurityContextHolder.clearContext()
            LoggingUtils.clearAll()
        }
    }

    override fun shutDownGracefully(runnable: Runnable) {
        CallbackAwaiter.withCallback(runnable).awaitAsync {
            log.info("Stop IngestProcessingJob...")
            ingestProcessingExecutor.shutdownNow()
            shutDownLatch.awaitShutdown()
            if (!ingestProcessingExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                log.warn("release active jobs")
                releaseActiveJobsAndFiles()
            }
            log.info("Stop IngestProcessingJob... DONE")
        }
    }
}
