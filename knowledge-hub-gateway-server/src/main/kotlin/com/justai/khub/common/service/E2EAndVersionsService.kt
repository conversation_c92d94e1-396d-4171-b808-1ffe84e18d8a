package com.justai.khub.common.service

import com.justai.e2echeck.E2ECheck.STATUS_FAIL
import com.justai.e2echeck.E2EComponentDTO
import com.justai.khub.common.dto.ServiceVersion
import com.justai.khub.common.service.e2e.E2EChecker
import kotlinx.coroutines.*
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.Instant
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.LinkedBlockingQueue
import kotlin.time.Duration.Companion.seconds

@Service
class E2EAndVersionsService(
    private val checkers: List<E2EChecker>,
) {
    private val E2E_CHECK_TIMEOUT = 25.seconds
    private val VERSION_CHECK_TIMEOUT = 25.seconds

    private val log = LoggerFactory.getLogger(this::class.java)

    fun getVersions(): List<ServiceVersion> = runBlocking {
        val results = LinkedBlockingQueue<ServiceVersion>()
        coroutineScope { checkers.map { getVersion(it, results) } }.joinAll()
        results.sortedBy { it.name }
    }


    fun performE2E(requestId: String, loggingEnabledE2ECheck: Boolean): List<E2EComponentDTO> = runBlocking {
        val results = ConcurrentHashMap<String, E2EComponentDTO>()
        coroutineScope { checkers.map { performE2E(it, requestId, loggingEnabledE2ECheck, results) }.joinAll() }
        results.values.sortedBy { it.name }
    }

    fun CoroutineScope.performE2E(
        checker: E2EChecker,
        requestId: String,
        loggingEnabledE2ECheck: Boolean,
        results: ConcurrentHashMap<String, E2EComponentDTO>
    ): Job {
        return launch(Dispatchers.IO) {
            val startedAt = Instant.now()
            try {
                withTimeout(E2E_CHECK_TIMEOUT) {
                    checker.performE2E(requestId, loggingEnabledE2ECheck)
                        .forEach { results.putIfAbsent(it.name, it) }
                }
            } catch (ex: Exception) {
                val elapsedTime = Instant.now().toEpochMilli() - startedAt.toEpochMilli()
                if (loggingEnabledE2ECheck) {
                    if (ex is TimeoutCancellationException) {
                        log.warn("Timeout performing e2e: {}", checker.serviceName, ex)
                    } else {
                        log.warn("Failed to perform e2e: {}", checker.serviceName, ex)
                    }
                }

                results[checker.serviceName] = E2EComponentDTO(checker.serviceName, STATUS_FAIL, startedAt.toString(), elapsedTime)
            }
        }
    }

    fun CoroutineScope.getVersion(checker: E2EChecker, results: LinkedBlockingQueue<ServiceVersion>): Job {
        return launch(Dispatchers.IO) {
            try {
                withTimeout(VERSION_CHECK_TIMEOUT) {
                    checker.getVersion().forEach { results.add(it) }
                }
            } catch (ex: Exception) {
                if (ex is TimeoutCancellationException) {
                    log.warn("Version request timeout: '{}'", checker.serviceName, ex)
                } else {
                    log.warn("Failed to get version: '{}'", checker.serviceName, ex)
                }
                results.add(
                    ServiceVersion(
                        name = checker.serviceName,
                        version = ServiceVersion.STATUS_UNAVAILABLE,
                        status = ServiceVersion.STATUS_ERROR
                    )
                )
            }
        }
    }
}
