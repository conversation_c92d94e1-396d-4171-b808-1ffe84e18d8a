package com.justai.khub.project.repository

import com.justai.khub.project.entity.ProjectEntity
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface ProjectRepository : JpaRepository<ProjectEntity, Long> {
    @Query("SELECT p FROM ProjectEntity p WHERE p.createdBy.accountId = :accountId AND p.name = :name AND p.deletedAt IS NULL")
    fun findByNameAndCreatedByAndDeletedAtIsNull(name: String, accountId: Long): ProjectEntity?
    fun findByIdAndDeletedAtIsNull(id: Long): ProjectEntity?
}

