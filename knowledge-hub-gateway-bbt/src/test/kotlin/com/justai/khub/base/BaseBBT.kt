package com.justai.khub.base

import com.justai.khub.util.Auth
import com.justai.khub.util.TestAccountsService
import com.justai.khub.util.BBTContext
import com.justai.khub.util.BBTContextFactory
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.TestInstance
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@ActiveProfiles("test", "local")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
abstract class BaseBBT {

    @Autowired
    protected lateinit var testAccountsService: TestAccountsService

    @Autowired
    protected lateinit var bbtContextFactory: BBTContextFactory

    protected lateinit var defaultAuth: Auth
    protected lateinit var defaultBBTContext: BBTContext

    @BeforeAll
    fun init() {
        defaultAuth = testAccountsService.loginWithNewAccount()
        testAccountsService.changeTestAccountBalance(defaultAuth.user.accountId!!)
        defaultBBTContext = bbtContextFactory.bbtContext(defaultAuth)
    }

    fun withDefaultAuth(action: BBTContext.() -> Unit) {
        defaultBBTContext.action()
    }

    fun withAuth(auth: Auth, action: BBTContext.() -> Unit) {
        bbtContextFactory.bbtContext(auth).action()
    }
}
