package com.justai.khub.test

import com.justai.khub.base.BaseBBT
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class SystemBBT : BaseBBT() {

    @Test
    fun testHealthcheck() = withDefaultAuth {
        val healthcheck = systemApi.healthCheck()
        assertEquals("Ok", healthcheck)
    }

    @Test
    fun testVersion() = withDefaultAuth {
        val versionInfo = systemApi.version
        assertEquals("knowledge-hub-gateway-server", versionInfo.get("projectArtifactId").asText())
    }

    @Test
    fun testE2E() = withDefaultAuth {
        val e2eResult = systemApi.e2ECheck("bbt", true)
        e2eResult.e2EComponents.forEach {
            assertEquals("ok", it.status, "Unexpected E2E status for service '${it.name}'")
        }
        assertEquals("ok", e2eResult.totalStatus)
        assertEquals(6, e2eResult.e2EComponents.count { it.name.startsWith("knowledge-hub-gateway") })
        assertEquals(2, e2eResult.e2EComponents.count { it.name.startsWith("knowledge-hub-parser") })
        assertEquals(2, e2eResult.e2EComponents.count { it.name.startsWith("knowledge-hub-ingester") })
        assertEquals(4, e2eResult.e2EComponents.count { it.name.startsWith("knowledge-hub-rag") })
    }

    @Test
    fun testVersions() = withDefaultAuth {
        val versions = systemApi.versions().map { it as LinkedHashMap<*, *> }

        assertEquals(9, versions.size)
        val expectedNames = listOf("accountsadmin", "java", "knowledge-hub-evaluator", "knowledge-hub-gateway", "knowledge-hub-ingester", "knowledge-hub-parser", "knowledge-hub-rag", "lakefs", "postgres")
        expectedNames.forEachIndexed { index, serviceName ->
            assertEquals(serviceName, versions[index]["name"], "Unexpected service order in '/versions' response")
            assertEquals("ok", versions[index]["status"], "Unexpected service '$serviceName' status")
        }
    }
}
