package com.justai.khub.user.mapper

import com.justai.khub.api.fe.model.AnalyticsDTO
import com.justai.khub.api.fe.model.IngestSettingsDTO
import com.justai.khub.api.fe.model.UserBalanceDTO
import com.justai.khub.api.fe.model.UserInfoDTO
import com.justai.khub.billing.entity.AccountBalanceEntity
import com.justai.khub.common.configuration.AnalyticsConfiguration
import com.justai.khub.common.configuration.dto.IngestProperties
import com.justai.khub.common.configuration.dto.KHubUser
import java.time.ZoneId

object UserMapper {
    val EMPTY_BALANCE = UserBalanceDTO(
        availableRequestsByTariff = 0,
        usedRequestsByTariff = 0,
        availableRequestsByPackages = 0,
        usedRequestsByPackages = 0,
        operationsSuspended = false,
        updatedAt = null
    )

    fun KHubUser.toUserInfoDTO(localFeatures: List<String>, ingestProperties: IngestProperties, analyticsConfiguration: AnalyticsConfiguration): UserInfoDTO {
        return UserInfoDTO(
            accountId = this.accountId,
            login = this.userData?.login ?: "",
            email = this.userData?.email ?: "",
            name = this.userData?.fullName ?: "",
            features = this.userData?.features ?: listOf(),
            localFeatures = localFeatures,
            ingestSettings = IngestSettingsDTO(
                supportedExtensions = ingestProperties.supportedExtensions.toList().sorted()
            ),
            analytics = AnalyticsDTO(gtm = analyticsConfiguration.gtm)
        )
    }

    fun AccountBalanceEntity?.toUserBalanceDTO(): UserBalanceDTO {
        if (this == null) return EMPTY_BALANCE
        return UserBalanceDTO(
            availableRequestsByTariff = this.availableRequestsByTariff,
            usedRequestsByTariff = this.usedRequestsByTariff,
            availableRequestsByPackages = this.availableRequestsByPackages,
            usedRequestsByPackages = this.usedRequestsByPackages,
            operationsSuspended = this.operationsSuspended,
            updatedAt = this.updatedAt.atZone(ZoneId.systemDefault()).toInstant(),
        )
    }
}
