package com.justai.khub.query.dto

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
enum class ParticipantRole {
    user,
    assistant
}

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class UserHistoryRecord(
    val message: String,
    val role: ParticipantRole
)

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class UserHistory(
    val historyRecords: List<UserHistoryRecord>
)
