package com.justai.khub.ingest.mapper

import com.justai.khub.ingest.dto.FileLinkDTO
import com.justai.khub.ingest.dto.StartParsingRequest
import com.justai.khub.project.dto.FileLink

object ParsingServiceMapper {

    fun toStartParsingRequest(file: FileLink, config: Map<String, String>): StartParsingRequest {
        return StartParsingRequest(
            file = FileLinkDTO(
                repositoryId = file.repositoryId,
                branchName = file.branchName,
                relativePath = file.path,
            ),
            config = config
        )
    }
}
