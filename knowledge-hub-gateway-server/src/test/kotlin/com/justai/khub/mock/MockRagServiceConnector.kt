package com.justai.khub.mock

import com.justai.khub.api.fe.model.E2EComponent
import com.justai.khub.attachment.service.AttachmentService
import com.justai.khub.common.configuration.dto.IntegrationProperties
import com.justai.khub.common.connector.RagServiceConnector
import com.justai.khub.common.dto.ServiceVersionResponse
import com.justai.khub.common.dto.rag.QueryResponse
import com.justai.khub.common.dto.rag.RetrieveChunksResponse
import com.justai.khub.common.dto.rag.TestGenerateResponse
import com.justai.khub.project.dto.FileLink
import com.justai.khub.project.dto.IngestPipelineConfig
import com.justai.khub.project.dto.LLMConfig
import com.justai.khub.project.dto.SearchPipelineConfig
import com.justai.khub.project.mapper.RagServiceMapper
import com.justai.khub.query.dto.UserHistory
import com.justai.khub.util.RagConnectorDump.QueryDump
import com.justai.khub.util.TestDataFactory.testChunk
import com.justai.khub.util.TestDataFactory.testRetrieveChunksResponse
import com.justai.loadbalancer.annotations.HttpInternal
import org.slf4j.LoggerFactory
import org.springframework.web.client.RestTemplate
import java.util.concurrent.ConcurrentHashMap

class MockRagServiceConnector(
    ragServiceMapper: RagServiceMapper,
    @HttpInternal ragServiceRestTemplate: RestTemplate,
    attachmentService: AttachmentService,
    integrationProperties: IntegrationProperties
) : RagServiceConnector(ragServiceMapper, ragServiceRestTemplate, attachmentService, integrationProperties) {

    val queriesCache = ConcurrentHashMap<String, QueryDump>()

    private val log = LoggerFactory.getLogger(this::class.java)

    fun getQueryByMessage(message: String): QueryDump? {
        val query = queriesCache[message]
        log.info("Requested query by message: {}. Found: {}, mock hash code: {}", message, query != null, hashCode())
        return query
    }

    override fun clearHistory(conversationId: String) {
        log.info("Clearing history for conversation {}", conversationId)
    }

    override fun generateTests(fileLink: FileLink, parsedFileName: String, llm: LLMConfig, prompt: String, testsCount: Int): TestGenerateResponse {
        return TestGenerateResponse(listOf())
    }

    override fun performE2E(requestId: String, loggingEnabledE2ECheck: Boolean): List<E2EComponent> {
        return listOf()
    }

    override fun getChunksArchive(indexName: String, filePath: String): ByteArray? {
        return null
    }

    override fun getVersion(): ServiceVersionResponse {
        return ServiceVersionResponse()
    }

    override fun retrieveChunks(
        query: String,
        searchConfig: SearchPipelineConfig,
        ingestConfig: IngestPipelineConfig,
        topK: Int?,
        conversationId: String?,
        segment: String?,
        history: UserHistory?
    ): RetrieveChunksResponse {
        log.info("Retrieve chunks with query {}", query)
        return testRetrieveChunksResponse()
    }

    override fun query(
        message: String,
        segment: String?,
        conversationId: String,
        searchPipelineConfig: SearchPipelineConfig,
        ingestSettings: IngestPipelineConfig,
        history: UserHistory?
    ): QueryResponse {
        log.info("Saved rag query with message {}, mock hash code: {}", message, hashCode())
        queriesCache[message] = QueryDump(
            message = message,
            segment = segment,
            conversationId = conversationId,
            searchPipelineConfig = searchPipelineConfig,
            ingestSettings = ingestSettings,
            history = history
        )
        return when {
            message.startsWith("long query") -> {
                Thread.sleep(3000)
                QueryResponse(
                    "Test long response",
                    10,
                    100,
                    usedChunks = listOf(testChunk())
                )
            }

            message.startsWith("very long query") -> {
                Thread.sleep(6000)
                QueryResponse(
                    "Test long response",
                    10,
                    100,
                    usedChunks = listOf(testChunk())
                )
            }

            message.startsWith("query with docs") -> {
                Thread.sleep(3000)
                QueryResponse(
                    "Test response with relevant docs",
                    10,
                    100,
                    usedChunks = listOf(testChunk("doc1.docx")),
                    relevantSources = listOf("doc1.docx", "doc2.docx")
                )
            }

            else -> QueryResponse(
                "Test response",
                10,
                100,
                usedChunks = listOf(testChunk("doc1.docx"))
            )
        }
    }
}
