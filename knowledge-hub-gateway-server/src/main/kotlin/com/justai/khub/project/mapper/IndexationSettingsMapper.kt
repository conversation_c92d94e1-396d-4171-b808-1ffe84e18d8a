package com.justai.khub.project.mapper

import com.justai.khub.common.configuration.dto.IngestProperties
import com.justai.khub.common.configuration.dto.PropertiesOption
import com.justai.khub.common.configuration.dto.RagProperties
import com.justai.khub.common.service.MessageService
import com.justai.khub.project.dto.*
import com.justai.khub.project.mapper.ProjectSettingsMapper.Companion.DEFAULT_CHUNK_SIZE
import com.justai.khub.project.mapper.ProjectSettingsMapper.Companion.DEFAULT_CONTEXT_WINDOW
import com.justai.khub.project.mapper.ProjectSettingsMapper.Companion.DEFAULT_MAX_TOKENS
import com.justai.khub.project.mapper.ProjectSettingsMapper.Companion.DEFAULT_TEMPERATURE
import com.justai.khub.project.mapper.ProjectSettingsMapper.Companion.DEFAULT_TOKENS_SOFT_LIMIT
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class IndexationSettingsMapper(
    private val messageService: MessageService,
    private val ingestProperties: IngestProperties,
    private val ragProperties: RagProperties,
    @Value("\${spring.messages.default-locale}") private val defaultLocale: String,
) {

    fun indexPartition(source: IngestPipelineConfig): SettingPartition {
        val availableChunkerTypes = ChunkerType.entries.filter { ingestProperties.chunker.llmChunkerEnabled || it != ChunkerType.llm }
        return SettingPartition(
            name = "indexation",
            title = LocaleObject(
                ru = messageService.getMessage("project.settings.indexation.title.ru"),
                en = messageService.getMessage("project.settings.indexation.title.en")
            ),
            menuTitle = LocaleObject(
                ru = messageService.getMessage("project.settings.indexation.menutitle.ru"),
                en = messageService.getMessage("project.settings.indexation.menutitle.en")
            ),
            description = LocaleObject(
                ru = messageService.getMessage("project.settings.indexation.description.ru"),
                en = messageService.getMessage("project.settings.indexation.description.en")
            ),
            more = MoreObject(
                label = LocaleObject(
                    ru = messageService.getMessage("project.settings.indexation.docs.label.ru"),
                    en = messageService.getMessage("project.settings.indexation.docs.label.en")
                ),
                link = LocaleObject(
                    ru = messageService.getMessage("project.settings.indexation.docs.link.ru"),
                    en = messageService.getMessage("project.settings.indexation.docs.link.en")
                )
            ),
            sections = mapOf(
                "main" to mainIndexationSection(source.vectorizer),
                "chunk_method" to SettingsSection(
                    isCard = true,
                    title = LocaleObject(
                        ru = messageService.getMessage("project.settings.indexation.chunker.type.label.ru"),
                        en = messageService.getMessage("project.settings.indexation.chunker.type.label.en")
                    ),
                    settings = mapOf(
                        "type" to CardsSettingDescription(
                            label = null,
                            value = source.chunker.type.name,
                            required = true,
                            options = asOptions(availableChunkerTypes, "project.settings.indexation.chunker.type.options")
                        )
                    )
                ),
                "chunker" to SettingsSection(
                    isCard = false,
                    title = LocaleObject(
                        ru = messageService.getMessage("project.settings.indexation.chunker.chunk_size.title.ru"),
                        en = messageService.getMessage("project.settings.indexation.chunker.chunk_size.title.en")
                    ),
                    settings = mapOf(
                        "chunk_size" to SliderSettingDescription(
                            dependsOn = listOf(
                                DependencyConfig(
                                    "indexation.chunk_method.type",
                                    listOf(ChunkerType.sentence.name)
                                )
                            ),
                            format = NumberFormat.int,
                            label = LocaleObject(
                                ru = messageService.getMessage("project.settings.indexation.chunker.chunk_size.label.ru"),
                                en = messageService.getMessage("project.settings.indexation.chunker.chunk_size.label.en")
                            ),
                            value = source.chunker.chunkSize?.toDouble() ?: DEFAULT_CHUNK_SIZE.toDouble(),
                            min = 500.0,
                            max = 8000.0,
                            step = 100.0,
                            visibleStep = 1000.0
                        ),
                        "language" to CardsSettingDescription(
                            dependsOn = listOf(
                                DependencyConfig(
                                    "indexation.chunk_method.type",
                                    listOf(ChunkerType.sentence.name)
                                )
                            ),
                            label = LocaleObject(
                                ru = messageService.getMessage("project.settings.indexation.chunker.language.label.ru"),
                                en = messageService.getMessage("project.settings.indexation.chunker.language.label.en")
                            ),
                            value = source.chunker.language?.name?.takeIf {
                                if (defaultLocale == "ru") {
                                    true
                                } else it == Language.english.name
                            } ?: Language.english.name,
                            required = true,
                            options = asOptions(Language.entries.filter { defaultLocale == "ru" || it == Language.english }, "project.settings.indexation.chunker.language.options")
                        ),
                        "tokens_soft_limit" to SliderSettingDescription(
                            dependsOn = listOf(DependencyConfig("indexation.chunk_method.type", listOf(ChunkerType.llm.name))),
                            format = NumberFormat.int,
                            label = LocaleObject(
                                ru = messageService.getMessage("project.settings.indexation.chunker.tokens_soft_limit.label.ru"),
                                en = messageService.getMessage("project.settings.indexation.chunker.tokens_soft_limit.label.en")
                            ),
                            hint = LocaleObject(
                                ru = messageService.getMessage("project.settings.indexation.chunker.tokens_soft_limit.hint.ru"),
                                en = messageService.getMessage("project.settings.indexation.chunker.tokens_soft_limit.hint.en")
                            ),
                            value = source.chunker.tokensSoftLimit?.toDouble() ?: DEFAULT_TOKENS_SOFT_LIMIT.toDouble(),
                            min = 250.0,
                            max = 4000.0,
                            step = 50.0,
                            visibleStep = 750.0
                        ),
                        "use_for_large_tables" to SwitchSettingDescription(
                            dependsOn = listOf(DependencyConfig("indexation.chunk_method.type", listOf(ChunkerType.llm.name))),
                            value = source.chunker.useForLargeTables ?: true,
                            label = LocaleObject(
                                ru = messageService.getMessage("project.settings.indexation.chunker.use_for_large_tables.label.ru"),
                                en = messageService.getMessage("project.settings.indexation.chunker.use_for_large_tables.label.en")
                            ),
                            hint = LocaleObject(
                                ru = messageService.getMessage("project.settings.indexation.chunker.use_for_large_tables.hint.ru"),
                                en = messageService.getMessage("project.settings.indexation.chunker.use_for_large_tables.hint.en")
                            )
                        )
                    )
                ),
                "llm" to SettingsSection(
                    isCard = false,
                    title = LocaleObject(
                        ru = messageService.getMessage("project.settings.llm.title.ru"),
                        en = messageService.getMessage("project.settings.llm.title.en")
                    ),
                    settings = mapOf(
                        "model" to SelectSettingDescription(
                            required = true,
                            value = source.chunker.model ?: ragProperties.llm.models.first { it.indexationEnabled }.value,
                            label = LocaleObject(
                                ru = messageService.getMessage("project.settings.llm.model.label.ru"),
                                en = messageService.getMessage("project.settings.llm.model.label.en")
                            ),
                            options = ragProperties.llm.models.filter { it.indexationEnabled  }.map {
                                Option(
                                    title = LocaleObject(
                                        ru = messageService.getMessage(it.title),
                                        en = messageService.getMessage(it.title)
                                    ),
                                    value = it.value
                                )
                            }
                        ),
                        "context_window" to SliderSettingDescription(
                            format = NumberFormat.int,
                            label = LocaleObject(
                                ru = messageService.getMessage("project.settings.llm.contextWindow.label.ru"),
                                en = messageService.getMessage("project.settings.llm.contextWindow.label.en")
                            ),
                            value = source.chunker.contextWindow?.toDouble() ?: DEFAULT_CONTEXT_WINDOW.toDouble(),
                            min = 4000.0,
                            max = 128000.0,
                            step = 800.0,
                            visibleStep = 24800.0,
                            hint = LocaleObject(
                                ru = messageService.getMessage("project.settings.llm.contextWindow.hint.ru"),
                                en = messageService.getMessage("project.settings.llm.contextWindow.hint.en")
                            )
                        ),
                        "max_tokens" to SliderSettingDescription(
                            format = NumberFormat.int,
                            label = LocaleObject(
                                ru = messageService.getMessage("project.settings.llm.contextWindow.label.ru"),
                                en = messageService.getMessage("project.settings.llm.contextWindow.label.en")
                            ),
                            value = source.chunker.maxTokens?.toDouble() ?: DEFAULT_MAX_TOKENS.toDouble(),
                            min = 500.0,
                            max = 2000.0,
                            step = 100.0,
                            visibleStep = 500.0,
                            hint = LocaleObject(
                                ru = messageService.getMessage("project.settings.llm.maxTokens.hint.ru"),
                                en = messageService.getMessage("project.settings.llm.maxTokens.hint.en")
                            )
                        ),
                        "temperature" to SliderSettingDescription(
                            format = NumberFormat.double,
                            label = LocaleObject(
                                ru = messageService.getMessage("project.settings.llm.temperature.label.ru"),
                                en = messageService.getMessage("project.settings.llm.temperature.label.en")
                            ),
                            value = source.chunker.temperature ?: DEFAULT_TEMPERATURE,
                            min = 0.0,
                            max = 1.0,
                            step = 0.01,
                            visibleStep = 0.25,
                            hint = LocaleObject(
                                ru = messageService.getMessage("project.settings.llm.temperature.hint.ru"),
                                en = messageService.getMessage("project.settings.llm.temperature.hint.en")
                            )
                        )
                    )
                )
            )
        )
    }

    private fun mainIndexationSection(currentValue: VectorizerConfig): SettingsSection {
        return SettingsSection(
            isCard = true,
            title = LocaleObject(
                ru = messageService.getMessage("project.settings.indexation.vectorizer.title.ru"),
                en = messageService.getMessage("project.settings.indexation.vectorizer.title.en")
            ),
            description = LocaleObject(
                ru = messageService.getMessage("project.settings.indexation.vectorizer.description.ru"),
                en = messageService.getMessage("project.settings.indexation.vectorizer.description.en")
            ),
            settings = mapOf(
                "type" to CardsSettingDescription(
                    label = LocaleObject(
                        ru = messageService.getMessage("project.settings.indexation.vectorizer.model.label.ru"),
                        en = messageService.getMessage("project.settings.indexation.vectorizer.model.label.en")
                    ),
                    required = true,
                    value = currentValue.model ?: ingestProperties.vectorizer.options.first().value,
                    options = ingestProperties.vectorizer.options.map { it.toOption("project.settings.indexation.vectorizer.type.options") }
                )
            )
        )
    }

    private fun asOptions(entries: List<Enum<*>>, messagePrefix: String): List<Option> {
        return entries.map {
            Option(
                title = LocaleObject(
                    ru = messageService.getMessage("${messagePrefix}.${it}.title.ru"),
                    en = messageService.getMessage("${messagePrefix}.${it}.title.en")
                ),
                description = LocaleObject(
                    ru = messageService.getMessage("${messagePrefix}.${it}.description.ru"),
                    en = messageService.getMessage("${messagePrefix}.${it}.description.en")
                ),
                value = it.name
            )
        }
    }

    private fun PropertiesOption.toOption(messagePrefix: String): Option {
        return Option(
            title = LocaleObject(
                ru = messageService.getMessage("${messagePrefix}.${this.title}.title.ru"),
                en = messageService.getMessage("${messagePrefix}.${this.title}.title.en")
            ),
            description = LocaleObject(
                ru = messageService.getMessage("${messagePrefix}.${this.title}.description.ru"),
                en = messageService.getMessage("${messagePrefix}.${this.title}.description.en")
            ),
            value = this.value,
            img = this.img
        )
    }
}
