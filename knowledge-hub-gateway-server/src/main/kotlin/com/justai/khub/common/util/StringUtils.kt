package com.justai.khub.common.util

import com.ibm.icu.text.Transliterator
import com.justai.khub.project.entity.ProjectEntity
import org.apache.commons.lang3.RegExUtils


object StringUtils {
    var transliterator: Transliterator = Transliterator.getInstance("Russian-Latin/BGN")
    val MAX_INDEX_NAME_LENGTH = 255
    val MAX_REPOSITORY_NAME_LENGTH = 63

    fun generateIndexName(project: ProjectEntity, versionName: String): String {
        var sanitizedProjectName = sanitize(project.name)
        val sanitizedVersionName = sanitize(versionName)
        val indexName = "${sanitizedProjectName}_${sanitizedVersionName}_${project.id}_${project.createdBy.accountId}"
        if (indexName.length > MAX_INDEX_NAME_LENGTH) {
            sanitizedProjectName = sanitizedProjectName.substring(MAX_INDEX_NAME_LENGTH - indexName.length)
        }
        return "${sanitizedProjectName}-${sanitizedVersionName}-${project.id}-${project.createdBy.accountId}"
    }

    private fun sanitize(source: String): String {
        var sanitized = transliterator.transliterate(source)
        sanitized = RegExUtils.replaceAll(sanitized, "[^a-zA-Z0-9]", "").lowercase()
        if (!sanitized.first().isLetterOrDigit()) {
            sanitized = "s-$sanitized"
        }
        return sanitized
    }


    fun generateRepositoryId(project: ProjectEntity, accountId: Long): String {
        var sanitizedProjectName = sanitize(project.name)
        val suffixLength = "-${accountId}-${System.currentTimeMillis() / 1000}".length
        if (sanitizedProjectName.length + suffixLength > MAX_REPOSITORY_NAME_LENGTH) {
            sanitizedProjectName = sanitizedProjectName.substring(0, MAX_REPOSITORY_NAME_LENGTH - suffixLength)
        }
        return "${sanitizedProjectName}-${accountId}-${System.currentTimeMillis() / 1000}"
    }
}
