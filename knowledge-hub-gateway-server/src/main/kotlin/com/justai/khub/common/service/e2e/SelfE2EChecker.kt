package com.justai.khub.common.service.e2e

import com.justai.khub.common.dto.ServiceVersion
import com.justai.khub.common.dto.ServiceVersionResponse
import com.justai.khub.common.mapper.CommonMapper.toDTO
import com.justai.khub.common.service.MetricsService
import com.justai.khub.common.util.JSON
import jakarta.annotation.PostConstruct
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.io.Resource
import org.springframework.stereotype.Component
import org.springframework.util.StreamUtils
import java.nio.charset.StandardCharsets

@Component
class SelfE2EChecker(
    private val metricsService: MetricsService,
    @Value("classpath:version.json")
    versionResource: Resource
) : E2EChecker("") {

    val selfVersion: ServiceVersionResponse = JSON.parse(
        StreamUtils.copyToString(versionResource.inputStream, StandardCharsets.UTF_8),
        ServiceVersionResponse::class.java
    )

    @PostConstruct
    fun init() {
        metricsService.gaugeAppInfo(selfVersion)
    }

    override fun getVersion(): List<ServiceVersion> {
        return listOf(
            selfVersion.toDTO(NAME)
        )
    }

    companion object {
        const val NAME = "knowledge-hub-gateway"
    }
}
