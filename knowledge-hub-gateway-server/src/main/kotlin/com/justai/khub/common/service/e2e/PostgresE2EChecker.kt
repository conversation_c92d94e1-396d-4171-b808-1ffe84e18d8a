package com.justai.khub.common.service.e2e

import com.justai.e2echeck.E2ECheck.STATUS_OK
import com.justai.e2echeck.E2EComponentDTO
import com.justai.khub.billing.repository.AccountBalanceRepository
import com.justai.khub.common.dto.ServiceVersion
import org.springframework.stereotype.Component
import org.springframework.transaction.support.TransactionTemplate
import java.time.Instant
import javax.sql.DataSource

@Component
class PostgresE2EChecker(
    private val accountBalanceRepository: AccountBalanceRepository,
    private val transactionTemplate: TransactionTemplate,
    private val dataSource: DataSource,
) : E2EChecker(NAME) {
    override fun performE2E(requestId: String, loggingEnabledE2ECheck: Boolean): List<E2EComponentDTO> {
        val startedAt = Instant.now()
        transactionTemplate.execute { accountBalanceRepository.findByAccountIds(listOf(-1)) }
        val processingTime = Instant.now().toEpochMilli() - startedAt.toEpochMilli()
        return listOf(E2EComponentDTO(formatE2ECheckName(), STATUS_OK, startedAt.toString(), processingTime))
    }

    override fun getVersion(): List<ServiceVersion> {
        val version = dataSource.connection.use {
            "${it.metaData.databaseProductName} ${it.metaData.databaseProductVersion}"
        }
        return listOf(
            ServiceVersion(
                name = NAME,
                version = version,
                status = ServiceVersion.STATUS_OK
            )
        )
    }

    companion object {
        const val NAME = "postgres"
    }
}
