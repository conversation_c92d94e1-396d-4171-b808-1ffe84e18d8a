package com.justai.khub.channel.dto

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import java.time.Instant

@JsonNaming(PropertyNamingStrategies.LowerCamelCaseStrategy::class)
data class BotProjectReadDto(
    val id: Long,
    val name: String,
    val shortName: String? = null,
    val defaultBranch: String? = null,
    val product: String? = null,
    val botType: String? = null,
    val webHookUrl: String? = null,
    val templateName: String? = null,
    val lastModificationData: Instant? = null,
    val botConfigs: List<BotConfigDto> = emptyList(),
)
