package com.justai.khub.util

import org.apache.commons.io.IOUtils
import org.apache.hc.client5.http.config.RequestConfig
import org.apache.hc.client5.http.impl.classic.HttpClients
import org.slf4j.LoggerFactory
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.client.BufferingClientHttpRequestFactory
import org.springframework.http.client.ClientHttpRequestInterceptor
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.web.client.RestTemplate
import org.springframework.web.util.DefaultUriBuilderFactory
import java.nio.charset.StandardCharsets
import java.util.concurrent.TimeUnit

object RestTemplateFactory {
    private val log = LoggerFactory.getLogger("RestTemplate")

    fun createRestTemplate(
        auth: Auth? = null,
        baseUrl: String? = null
    ): RestTemplate {
        val bufferingClientHttpRequestFactory = BufferingClientHttpRequestFactory(
            HttpComponentsClientHttpRequestFactory(
                HttpClients.custom()
                    .setDefaultRequestConfig(
                        RequestConfig.custom()
                            .setConnectionRequestTimeout(10, TimeUnit.SECONDS)
                            .setResponseTimeout(90, TimeUnit.SECONDS)
                            .build()
                    )
                    .build()
            )
        )
        val restTemplate = RestTemplate(bufferingClientHttpRequestFactory)
        val jacksonConverter = restTemplate.messageConverters.find {
            it is MappingJackson2HttpMessageConverter
        } as MappingJackson2HttpMessageConverter

        jacksonConverter.objectMapper = JSON.mapper
        baseUrl?.let { restTemplate.uriTemplateHandler = DefaultUriBuilderFactory(baseUrl); }

        restTemplate.interceptors.add(ClientHttpRequestInterceptor { request, body, execution ->
            auth?.apply {
                request.headers[HttpHeaders.COOKIE] = cookies.joinToString("; ")
                request.headers["X-XSRF-TOKEN"] = xXsrfToken
            }

            var requestLogMessage = "\n\tRequest: ${request.method} ${request.uri}"
            requestLogMessage += if (request.headers.contentType?.isCompatibleWith(MediaType.APPLICATION_JSON) == true) {
                "\n\tBody: ${String(body)}"
            } else {
                "\n\tBody type: ${request.headers.contentType}"
            }
            log.info(requestLogMessage)

            val startedAt = System.currentTimeMillis()
            val response = execution.execute(request, body)
            val processingTime = System.currentTimeMillis() - startedAt

            var responseLogMessage = "Response: ${request.method} ${request.uri}\n\t" +
                "Status: ${response.statusCode} ${response.statusText}\n\t" +
                "Processing time: ${processingTime}ms"
            responseLogMessage += if (response.headers.contentType?.isCompatibleWith(MediaType.APPLICATION_JSON) == true) {
                "\n\tBody: ${IOUtils.toString(response.body, StandardCharsets.UTF_8)}"
            } else {
                "\n\tBody type: ${response.headers.contentType}"
            }
            log.info(responseLogMessage)
            response
        })

        return restTemplate
    }

}
