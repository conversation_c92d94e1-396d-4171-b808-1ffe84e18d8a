package com.justai.khub.qa.entity

import com.fasterxml.jackson.annotation.JsonProperty
import com.justai.khub.common.entity.CCUserEntity
import com.justai.khub.project.entity.ProjectEntity
import com.justai.khub.qa.dto.TestSetProperties
import com.justai.khub.qa.enumeration.TestSetStatus
import com.justai.khub.qa.enumeration.TestSetType
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.*
import org.hibernate.annotations.Type
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

@Entity
@Table(name = "test_set")
@EntityListeners(AuditingEntityListener::class)
class TestSetEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = -1

    @Column(nullable = false)
    lateinit var name: String

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    lateinit var type: TestSetType

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    lateinit var status: TestSetStatus

    @Column
    var archived: Boolean = false

    @Column(nullable = false, columnDefinition = "jsonb")
    @Type(JsonType::class)
    @JsonProperty
    lateinit var properties: TestSetProperties

    @Column
    var description: String? = null

    @Column(nullable = false)
    var totalTests: Int = 0

    @Column(nullable = false)
    var usedCompletionTokens: Int = 0

    @Column(nullable = false)
    var usedPromptTokens: Int = 0

    @ManyToOne
    @JoinColumn(name = "project_id", nullable = false)
    lateinit var project: ProjectEntity

    @Column(nullable = false)
    @CreatedDate
    lateinit var createdAt: LocalDateTime

    @Column
    @LastModifiedDate
    var updatedAt: LocalDateTime? = null

    @Column(name = "last_error")
    var lastError: String? = null

    @OneToOne(mappedBy = "testSet")
    var schedule: TestSetScheduleEntity? = null

    @CreatedBy
    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "accountId", column = Column(name = "created_by_account_id")),
        AttributeOverride(name = "userId", column = Column(name = "created_by_user_id"))
    )
    var createdBy: CCUserEntity = CCUserEntity()
}

