package com.justai.khub.integration.service

import com.justai.khub.common.configuration.dto.IntegrationProperties
import com.justai.khub.common.util.WebUtils
import com.justai.khub.common.util.WebUtils.checkUrlValid
import com.justai.khub.common.util.WebUtils.saveAsTempFile
import com.justai.khub.integration.dto.AttachmentWithLink
import com.justai.khub.integration.dto.ConfluencePage
import com.justai.khub.integration.dto.ConfluenceSettingsCommon
import com.justai.khub.integration.dto.MinervaIntegrationSettings
import com.justai.khub.integration.entity.IntegrationEntity
import com.justai.khub.project.dto.ExternalFileShort
import com.justai.khub.project.entity.ProjectFileEntity
import com.justai.khub.project.mapper.ProjectFileMapper.createOrUpdateEntity
import com.justai.khub.project.mapper.ProjectFileMapper.path
import com.justai.khub.project.mapper.ProjectFileMapper.toExternalLink
import com.justai.khub.project.service.ProjectFileService
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate
import java.nio.file.Files

@Service
class ExternalUpdateApplier(
    private val projectFileService: ProjectFileService,
    private val integrationProperties: IntegrationProperties,
    private val unbufferedRestTemplate: RestTemplate,
) {
    private val log = LoggerFactory.getLogger(this::class.java)
    private val MAX_FILE_SIZE = 100 * 1024 * 1024L

    fun deleteFile(existedFile: ExternalFileShort, integration: IntegrationEntity): Boolean {
        return try {
            projectFileService.deleteFile(integration.version.project.id, existedFile.id, integration.createdBy.accountId)
            true
        } catch (ex: Exception) {
            log.error("Failed to delete file {}", existedFile.id, ex)
            false
        }
    }

    fun addConfluencePage(page: ConfluencePage, integration: IntegrationEntity): Boolean {
        return try {
            val newEntity = page.content.createOrUpdateEntity(null, integration)
            val tempFile = page.content.body.exportView.value.saveAsTempFile(newEntity.pathInStorage)
            val pageEntity = try {
                projectFileService.addFile(newEntity, tempFile)
            } finally {
                Files.deleteIfExists(tempFile)
            }
            page.attachments.forEach { attachment ->
                try {
                    addAttachment(attachment = attachment, parentPageEntity = pageEntity, integration = integration)
                } catch (attachmentException: Exception) {
                    log.error("Can not add attachment {}", attachment, attachmentException)
                }
            }
            true
        } catch (ex: Exception) {
            log.error("Failed to add file {}", page.content.title, ex)
            false
        }
    }

    fun updateConfluencePage(
        existedConfluenceFile: ExternalFileShort,
        childrenFiles: List<ExternalFileShort>,
        page: ConfluencePage,
        integration: IntegrationEntity
    ): Boolean {
        return try {
            if ("${page.content.version.number}" == existedConfluenceFile.contentVersion && "${integration.name}/${page.content.path()}" == existedConfluenceFile.relativePath) {
                val existedEntity = projectFileService.getFile(existedConfluenceFile.id)
                val updatedMetadata = existedConfluenceFile.externalLink == null && when (integration.settings) {
                    is ConfluenceSettingsCommon, is MinervaIntegrationSettings -> {
                        projectFileService.setMetadata(
                            fileId = existedEntity.id,
                            metadata = existedEntity.metadata.copy(
                                externalLink = page.content.toExternalLink(integration)
                            )
                        )
                        true
                    }
                }
                val updatedAttachment = updateAttachments(page, childrenFiles, integration, existedEntity)
                if (updatedAttachment) {
                    /**
                        if attachments were updated - recreate in database and storage - we must reset ingestion of
                        parent page for resolving new attachments links on parsing step.
                    **/
                    projectFileService.resetFileIngestion(existedEntity, integration.autoIngest)
                    log.info("Reset page ${existedEntity.relativePath} ingestion")
                }
                return updatedMetadata || updatedAttachment
            } else {
                val existedPageEntity = projectFileService.getFile(existedConfluenceFile.id)
                val newEntity = page.content.createOrUpdateEntity(existedPageEntity, integration)
                val tempFile = page.content.body.exportView.value.saveAsTempFile(newEntity.pathInStorage)
                try {
                    projectFileService.updateFileContent(newEntity, tempFile, integration.autoIngest)
                    log.info("Updated page ${newEntity.relativePath}")
                } finally {
                    Files.deleteIfExists(tempFile)
                }
                updateAttachments(page, childrenFiles, integration, newEntity)
                true
            }
        } catch (ex: Exception) {
            log.error("Failed to update file {}", page.content.title, ex)
            false
        }
    }

    private fun updateAttachments(
        page: ConfluencePage,
        attachmentsEntities: List<ExternalFileShort>,
        integration: IntegrationEntity,
        existedPageEntity: ProjectFileEntity
    ): Boolean {
        val attachmentExistedFiles = attachmentsEntities.associateBy { it.externalId }
        var updated = false
        val actualConfluenceAttachmentIds = page.attachments.map { it.attachment.id }.toSet()
        attachmentsEntities.filter { existedAttachmentEntity ->
            !actualConfluenceAttachmentIds.contains(existedAttachmentEntity.externalId)
        }.forEach { nonActualAttachmentEntity ->
            projectFileService.deleteFile(
                projectId = integration.version.project.id,
                fileId = nonActualAttachmentEntity.id,
                accountId = integration.createdBy.accountId
            )
            updated = true
        }
        page.attachments.forEach { attachment ->
            val attachmentExistedFile = attachmentExistedFiles[attachment.attachment.id]
            try {
                if (attachmentExistedFile != null
                    && ("${attachment.attachment.version.number}" != attachmentExistedFile.contentVersion
                        || "${integration.name}/${attachment.attachment.path(existedPageEntity)}" != attachmentExistedFile.relativePath)
                ) {
                    projectFileService.deleteFile(
                        projectId = integration.version.project.id,
                        fileId = attachmentExistedFile.id,
                        accountId = integration.createdBy.accountId
                    )
                    addAttachment(attachment = attachment, parentPageEntity = existedPageEntity, integration = integration)?.also {
                        log.info("Updated attachment ${it.relativePath}")
                    }
                    updated = true
                } else if (attachmentExistedFile == null) {
                    addAttachment(attachment = attachment, parentPageEntity = existedPageEntity, integration = integration)?.also {
                        log.info("Add new attachment ${it.relativePath}")
                        updated = true
                    }
                }
            } catch (attachmentException: Exception) {
                log.error("Can not add attachment {}", attachment, attachmentException)
            }
        }
        return updated
    }

    private fun addAttachment(attachment: AttachmentWithLink, parentPageEntity: ProjectFileEntity, integration: IntegrationEntity): ProjectFileEntity? {
        val url = checkUrlValid(attachment.downloadLink.link)

        val attachmentEntity = attachment.createOrUpdateEntity(
            existedEntity = null,
            integration = integration,
            parentEntity = parentPageEntity,
        )
        return if (!integrationProperties.externalSources.acceptedAttachmentTypes.contains(attachmentEntity.metadata.extension)) {
            log.warn("Unsupported attachment: ${attachmentEntity.metadata.title}")
            null
        } else {
            val content = Files.createTempFile("upload", ".tmp")
            try {
                WebUtils.downloadFileFromUrl(
                    restTemplate = unbufferedRestTemplate,
                    url = url,
                    maxFileSize = MAX_FILE_SIZE,
                    outputPath = content,
                    customRequestHeaders = attachment.downloadLink.headers,
                )
                projectFileService.addFile(
                    entity = attachmentEntity.apply { this.sizeBytes = Files.size(content).toInt() },
                    content = content,
                )
            } finally {
                Files.deleteIfExists(content)
            }
        }
    }
}
